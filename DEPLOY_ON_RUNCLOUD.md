# Guide: Deploying a Nuxt 3 App to RunCloud

This guide provides a complete step-by-step process for deploying this Nuxt 3 application to a RunCloud-managed server.

**Scenario:**
*   **Backend:** An existing Laravel application located at `/home/<USER>/webapps/jubel`.
*   **Frontend:** This Nuxt 3 application, which will be deployed to `/home/<USER>/webapps/otoresell`.
*   **Version Control:** GitLab (but the steps are similar for GitHub/Bitbucket).

---

### **Phase 1: Server Preparation & Web App Creation**

#### **Step 1: Select Your Server & Check Node.js Version**
1.  Log into your RunCloud account.
2.  Click on the server where your Laravel application is running.
3.  In the server's left-hand menu, click **"Services"**.
4.  Find the **Node.js** service. Ensure the installed version is **v18.x or newer**. If not, install a newer version from this screen.

#### **Step 2: Create a New Web Application for Nuxt**
1.  In the same server view, click on **"Web Applications"** in the left menu.
2.  Click the **"Create Web App"** button.
3.  Choose **"Node.js"** as the application type.
4.  Fill out the form:
    *   **Name:** `otoresell`
    *   **Domain Name:** The domain or subdomain for your frontend (e.g., `app.yourdomain.com`).
    *   **Node.js Version:** Select the v18+ version you confirmed earlier.
    *   **Web Application Owner:** Choose your `runcloud` system user.
5.  Click **"Create Web App"**. This will create the `/home/<USER>/webapps/otoresell/` directory.

---

### **Phase 2: Configuration and Deployment**

#### **Step 3: Connect Your GitLab Repository**
1.  Navigate into your new `otoresell` web application in the RunCloud dashboard.
2.  In the left menu, click on **"Git"**.
3.  Connect your GitLab account to RunCloud if you haven't already.
4.  Select this project's repository and the branch you want to deploy (e.g., `main`).
5.  **Important:** Ensure the **"Auto Deploy"** toggle is switched **OFF** for now.

#### **Step 4: Configure the Deployment Script**
This is the most critical step. It tells RunCloud how to build your application.
1.  In the **"Git"** section, scroll down to the **"Deployment Script"** panel.
2.  Delete any default content and paste in the following script:

    ```bash
    # This script runs every time you deploy from GitLab
    
    # Navigate to your Nuxt app's directory
    cd /home/<USER>/webapps/otoresell
    
    # Install project dependencies using npm
    # If you use yarn or pnpm, change this to 'yarn install' or 'pnpm install'
    npm install
    
    # Build the Nuxt app for production.
    # This creates the essential '.output' directory.
    npm run build
    ```
3.  Click **"Save Deployment Script"**.

#### **Step 5: Configure the Node.js Runner**
This tells RunCloud's process manager (PM2) how to run your built application.
1.  In the left menu for `otoresell`, click on **"Node.js"**.
2.  Set the fields exactly as follows:
    *   **Application Mode:** `Production`
    *   **App Startup File:** `server/index.mjs`
    *   **App Base Directory:** `.output`
3.  Click **"Update Settings"**.

#### **Step 6: Set Your Environment Variables**
Never use a `.env` file in production. Use RunCloud's secure variable manager.
1.  In the left menu for `otoresell`, click on **"Environment Variables"**.
2.  Add the variables your Nuxt app needs. The most important one is the URL for your backend API.
    *   **Key:** `NUXT_PUBLIC_API_BASE_URL` (or the name used in your `nuxt.config.ts`)
    *   **Value:** The full URL of your live Laravel API (e.g., `https://api.yourdomain.com`)
3.  Add any other secret keys or variables your application requires.

---

### **Phase 3: Go Live**

#### **Step 7: Run the First Manual Deployment**
1.  Go back to the **"Git"** section.
2.  Click the **"Deploy Now"** button.
3.  A log window will appear. Watch it carefully for any errors during the `npm install` or `npm run build` process.
4.  If it completes successfully, your Nuxt app is now running.

#### **Step 8: Configure Domain and SSL**
1.  Go to the **"Domain Name"** section. Ensure your DNS records (at your domain registrar) point your domain/subdomain to your server's IP address.
2.  Go to the **"SSL/TLS"** section.
3.  Choose **"Let's Encrypt"** and follow the prompts to issue a free SSL certificate.

#### **Step 9: Test Your Live Application**
1.  Open a browser and navigate to your domain (e.g., `https://app.yourdomain.com`).
2.  Verify that your application loads and functions correctly, especially features that communicate with your backend.

#### **Step 10: Enable Auto-Deployment**
Once you've confirmed everything works, you can automate future deployments.
1.  Return to the **"Git"** section.
2.  Switch the **"Auto Deploy"** toggle to **ON**.
3.  RunCloud will provide a webhook URL. Copy it.
4.  In your GitLab repository, go to **"Settings" -> "Webhooks"**. Paste the URL, set the trigger to **"Push events"**, and save.

**Deployment is now complete and automated.**