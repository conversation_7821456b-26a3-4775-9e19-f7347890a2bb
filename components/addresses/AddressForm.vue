<template>
  <v-dialog v-model="dialogVisible" max-width="600">
    <v-card>
      <v-card-title class="text-h5 py-3 px-4 bg-grey-lighten-4">
        {{ isEditing ? 'Edit Address' : 'Add New Address' }}
      </v-card-title>
            
      <v-card-text class="pa-4">
        <v-form ref="addressFormRef" @submit.prevent="saveAddress">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.shipping_full_name"
                label="Recipient Name"
                variant="outlined"
                density="comfortable"
                :error-messages="formErrors.shipping_full_name"
              ></v-text-field>
            </v-col>
                        
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.shipping_phone"
                label="Whatsapp Number"
                prepend-inner-icon="mdi-whatsapp"
                variant="outlined"
                density="comfortable"
                :error-messages="formErrors.shipping_phone"
              ></v-text-field>
            </v-col>
                        
            <v-col cols="12">
              <v-textarea
                v-model="formData.shipping_address"
                label="Full Address"
                variant="outlined"
                density="comfortable"
                rows="3"
                :error-messages="formErrors.shipping_address"
              ></v-textarea>
            </v-col>
                        
            <!-- Province Autocomplete -->
            <v-col cols="12" md="6">
              <v-autocomplete
                v-model="formData.shipping_province_id"
                label="Province"
                variant="outlined"
                density="comfortable"
                :items="provinceOptions"
                item-title="name"
                item-value="province_id"
                :error-messages="formErrors.shipping_province_id"
                :loading="addressStore.loadingProvinces"
                @update:model-value="onProvinceChange"
                clearable
                no-data-text="No provinces found"
                :search-input.sync="provinceSearch"
              ></v-autocomplete>
            </v-col>

            <!-- City Autocomplete -->
            <v-col cols="12" md="6">
              <v-autocomplete
                v-model="formData.shipping_city_id"
                label="City"
                variant="outlined"
                density="comfortable"
                :items="cityOptions"
                item-title="name"
                item-value="city_id"
                :error-messages="formErrors.shipping_city_id"
                :disabled="!formData.shipping_province_id"
                :loading="addressStore.loadingCities"
                @update:model-value="onCityChange"
                clearable
                no-data-text="No cities found"
                :search-input.sync="citySearch"
              ></v-autocomplete>
            </v-col>

            <!-- District Autocomplete -->
            <v-col cols="12" md="6">
              <v-autocomplete
                v-model="formData.shipping_district_id"
                label="District"
                variant="outlined"
                density="comfortable"
                :items="districtOptions"
                item-title="name"
                item-value="district_id"
                :error-messages="formErrors.shipping_district_id"
                :disabled="!formData.shipping_city_id"
                :loading="addressStore.loadingDistricts"
                @update:model-value="onDistrictChange"
                clearable
                no-data-text="No districts found"
                :search-input.sync="districtSearch"
              ></v-autocomplete>
            </v-col>

            <!-- Subdistrict Autocomplete -->
            <v-col cols="12" md="6">
              <v-autocomplete
                v-model="formData.shipping_subdistrict_id"
                label="Subdistrict"
                variant="outlined"
                density="comfortable"
                :items="subdistrictOptions"
                item-title="name"
                item-value="subdistrict_id"
                :error-messages="formErrors.shipping_subdistrict_id"
                :disabled="!formData.shipping_district_id"
                :loading="addressStore.loadingSubdistricts"
                @update:model-value="onSubdistrictChange"
                clearable
                no-data-text="No subdistricts found"
                :search-input.sync="subdistrictSearch"
              ></v-autocomplete>
            </v-col>

                        
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.shipping_post_code"
                label="Postal Code"
                variant="outlined"
                density="comfortable"
                :error-messages="formErrors.shipping_post_code"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
            
      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn color="grey" variant="text" @click="closeDialog">
          Cancel
        </v-btn>
        <v-btn
          color="deep-purple"
          variant="elevated"
          @click="saveAddress"
          :loading="saving"
          :disabled="saving"
        >
          Save Address
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { z } from 'zod/v3';
import { useAddressStore } from '~/stores/address';
import { useShippingAddressStore } from '~/stores/shipping-address';
import { useAuthStore } from '~/stores/auth';
import {
  createShippingAddressSchema,
  updateShippingAddressSchema,
  type ShippingAddress,
  type CreateShippingAddress,
  type UpdateShippingAddress,
} from '~/schemas/otoapi/shipping-address';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editingAddress: {
    type: Object as () => ShippingAddress | null,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'address-saved', 'address-canceled']);

// Stores
const addressStore = useAddressStore();
const shippingAddressStore = useShippingAddressStore();
const authStore = useAuthStore();

// UI State
const addressFormRef = ref(null);
const saving = ref(false);
const provinceSearch = ref('');
const citySearch = ref('');
const districtSearch = ref('');
const subdistrictSearch = ref('');
const formErrors = ref<Record<string, string[]>>({});

// Form state - Updated to match ShippingAddress interface
const formData = ref<UpdateShippingAddress>({
  shipping_full_name: '',
  shipping_phone: '',
  shipping_address: '',
  shipping_province_id: '',
  shipping_province: '',
  shipping_city_id: '',
  shipping_city: '',
  shipping_district_id: '',
  shipping_district: '',
  shipping_subdistrict_id: '',
  shipping_subdistrict: '',
  shipping_post_code: '',
});

// Computed properties
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEditing = computed(() => !!props.editingAddress);

const formSchema = computed(() => isEditing.value ? updateShippingAddressSchema : createShippingAddressSchema);
const provinceOptions = computed(() => addressStore.provinces);
const cityOptions = computed(() => addressStore.cities);
const districtOptions = computed(() => addressStore.districts);
const subdistrictOptions = computed(() => addressStore.subdistricts);
const currentUserId = computed(() => {
  return authStore.user?.id || null
})

// Methods
const onProvinceChange = async (provinceId: string) => {
  if (!provinceId) return;
    
  formData.value.shipping_city_id = '';
  formData.value.shipping_district_id = '';
  formData.value.shipping_subdistrict_id = '';
  
  // Clear dependent search inputs
  citySearch.value = '';
  districtSearch.value = '';
  subdistrictSearch.value = '';
    
  // Find the province name
  const province = addressStore.provinces.find(p => p.province_id === provinceId);
  if (province) {
    formData.value.shipping_province = province.name;
  }
    
  await addressStore.fetchCities(provinceId);
}

const onCityChange = async (cityId: string) => {
  if (!cityId) return;
    
  formData.value.shipping_district_id = '';
  formData.value.shipping_subdistrict_id = '';
  
  // Clear dependent search inputs
  districtSearch.value = '';
  subdistrictSearch.value = '';
    
  // Find the city name
  const city = addressStore.cities.find(c => c.city_id === cityId);
  if (city) {
    formData.value.shipping_city = city.name;
  }
    
  await addressStore.fetchDistricts(cityId);
}

const onDistrictChange = async (districtId: string) => {
  if (!districtId) return;
    
  formData.value.shipping_subdistrict_id = '';
  
  // Clear dependent search input
  subdistrictSearch.value = '';
    
  // Find the district name
  const district = addressStore.districts.find(d => d.district_id === districtId);
  if (district) {
    formData.value.shipping_district = district.name;
  }
    
  // Fetch subdistricts for this district
  await addressStore.fetchSubdistricts(districtId);
}

const onSubdistrictChange = (subdistrictId: string) => {
  if (!subdistrictId) {
    formData.value.shipping_subdistrict = '';
    return;
  }
  
  // Find the subdistrict name
  const subdistrict = addressStore.subdistricts.find(s => s.subdistrict_id === subdistrictId);
  if (subdistrict) {
    formData.value.shipping_subdistrict = subdistrict.name;
  }
}

const resetForm = () => {
  formData.value = {
    shipping_full_name: '',
    shipping_phone: '',
    shipping_address: '',
    shipping_province_id: '',
    shipping_province: '',
    shipping_city_id: '',
    shipping_city: '',
    shipping_district_id: '',
    shipping_district: '',
    shipping_subdistrict_id: '',
    shipping_subdistrict: '',
    shipping_post_code: '',
  };
  
  // Clear search inputs
  provinceSearch.value = '';
  citySearch.value = '';
  districtSearch.value = '';
  subdistrictSearch.value = '';
}

const closeDialog = () => {
  resetForm();
  emit('address-canceled');
  dialogVisible.value = false;
}

const validateForm = () => {
  const dataToValidate: any = { ...formData.value };
  if (!isEditing.value) {
    dataToValidate.user_id = currentUserId.value;
  }

  const result = formSchema.value.safeParse(dataToValidate);

  if (!result.success) {
    formErrors.value = result.error.flatten().fieldErrors;
    return false;
  }

  formErrors.value = {};
  return true;
};

const saveAddress = async () => {
  if (!validateForm()) {
    return;
  }

  // Check if user is authenticated
  if (!currentUserId.value) {
    emit('address-saved', { success: false, message: 'User not authenticated' });
    return;
  }
    
  saving.value = true;
    
  try {
    let result;
        
    if (isEditing.value && props.editingAddress) {
      // Update existing address
      const updateData: UpdateShippingAddress = {
        shipping_full_name: formData.value.shipping_full_name,
        shipping_phone: formData.value.shipping_phone,
        shipping_address: formData.value.shipping_address,
        shipping_province_id: formData.value.shipping_province_id,
        shipping_province: formData.value.shipping_province,
        shipping_city_id: formData.value.shipping_city_id,
        shipping_city: formData.value.shipping_city,
        shipping_district_id: formData.value.shipping_district_id,
        shipping_district: formData.value.shipping_district,
        shipping_subdistrict_id: formData.value.shipping_subdistrict_id,
        shipping_subdistrict: formData.value.shipping_subdistrict,
        shipping_post_code: formData.value.shipping_post_code || null
      };
            
      const updatedAddress = await shippingAddressStore.updateAddress(props.editingAddress.id, updateData);
      result = {
        success: !!updatedAddress,
        message: updatedAddress ? 'Address updated successfully' : 'Failed to update address',
        addressId: props.editingAddress.id
      };
    } else {
      // Add new address
      const createData: CreateShippingAddress = {
        ...(formData.value as CreateShippingAddress),
        user_id: currentUserId.value as number,
      };
            
      const newAddress = await shippingAddressStore.createAddress(createData);
      result = {
        success: !!newAddress,
        message: newAddress ? 'Address added successfully' : 'Failed to add address',
        addressId: newAddress?.id
      };
    }
        
    if (result.success) {
      emit('address-saved', result);
      resetForm();
      dialogVisible.value = false;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Error saving address:', error);
    emit('address-saved', {
      success: false,
      message: 'An error occurred while saving the address'
    });
  } finally {
    saving.value = false;
  }
}

// Watch for changes in editing address
watch(() => props.editingAddress, (newAddress) => {
  if (newAddress) {
    // Populate form with address data
    formData.value = {
      shipping_full_name: newAddress.shipping_full_name,
      shipping_phone: newAddress.shipping_phone,
      shipping_address: newAddress.shipping_address,
      shipping_province_id: newAddress.shipping_province_id,
      shipping_province: newAddress.shipping_province,
      shipping_city_id: newAddress.shipping_city_id,
      shipping_city: newAddress.shipping_city,
      shipping_district_id: newAddress.shipping_district_id,
      shipping_district: newAddress.shipping_district,
      shipping_subdistrict_id: newAddress.shipping_subdistrict_id,
      shipping_subdistrict: newAddress.shipping_subdistrict,
      shipping_post_code: newAddress.shipping_post_code || '',
    };
    
    // Load provinces first, then cascade load other regions
    addressStore.fetchProvinces().then(() => {
      if (newAddress.shipping_province_id) {
        addressStore.fetchCities(newAddress.shipping_province_id).then(() => {
          if (newAddress.shipping_city_id) {
            addressStore.fetchDistricts(newAddress.shipping_city_id).then(() => {
              if (newAddress.shipping_district_id) {
                addressStore.fetchSubdistricts(newAddress.shipping_district_id);
              }
            });
          }
        });
      }
    });
  } else {
    resetForm();
  }
}, { immediate: true });

// Load data when component mounts
onMounted(async () => {
  try {
    await addressStore.fetchProvinces();
  } catch (error) {
    console.error('Error loading provinces:', error);
  }
});
</script>
