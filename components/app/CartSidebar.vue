<template>
  <v-navigation-drawer
    v-model="cartDrawer"
    location="right"
    temporary
    width="400"
    class="cart-drawer"
  >
    <v-list-item class="py-3">
      <v-list-item-title class="text-h6 font-weight-bold">
        <v-icon start color="deep-purple">mdi-cart</v-icon>
        Your Cart
        <!-- Loading indicator -->
        <v-progress-circular
          v-if="cartStore.isLoading"
          indeterminate
          size="16"
          width="2"
          color="deep-purple"
          class="ml-2"
        ></v-progress-circular>
      </v-list-item-title>
      <template v-slot:append>
        <v-btn size="small" icon variant="text" @click="toggleCart">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </template>
    </v-list-item>
    <v-divider></v-divider>
        
    <!-- Empty cart state -->
    <div v-if="cartStore.items.length === 0" class="pa-6 text-center empty-cart">
      <v-icon size="80" color="grey-lighten-2" class="mb-4">mdi-cart-outline</v-icon>
      <div class="text-h6 text-grey-darken-1">Your cart is empty</div>
      <div class="text-body-2 text-grey mb-6">Add items to get started</div>
      <v-btn
        color="deep-purple"
        variant="elevated"
        prepend-icon="mdi-shopping"
        class="px-6"
        to="/products"
        @click="toggleCart"
      >
        Browse Products
      </v-btn>
    </div>
        
    <!-- Cart items -->
    <v-list v-else class="cart-items py-2">
      <v-list-item
        v-for="item in cartStore.items"
        :key="item.jubelio_item_id"
        class="cart-item mb-2"
        rounded
      >
        <template v-slot:prepend>
          <v-avatar size="50" rounded class="elevation-1">
            <v-img 
              :src="item.image || '/images/placeholder-product.png'"
              alt="Product"
              cover
            ></v-img>
          </v-avatar>
        </template>
                
        <v-list-item-title class="text-truncate font-weight-medium">
          <router-link 
            :to="`/products/${item.jubelio_item_id}`"
            class="text-decoration-none text-deep-purple-darken-1 hover-underline"
          >
            {{ item.name }}
          </router-link>
        </v-list-item-title>
                
        <v-list-item-subtitle>
          <div class="d-flex flex-column">
            <div class="d-flex align-center mt-1 ga-2">
              <v-chip size="x-small" color="grey-darken-3">
                {{ item.quantity }} {{ item.unit || 'pcs' }}
              </v-chip>
              <span v-if="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0" class="text-caption text-grey text-decoration-line-through">{{ formatRupiah(item.price) }}</span>
              <div class="d-flex align-center">
                <v-icon
                  v-if="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0"
                  size="x-small"
                  color="red"
                  class="mr-1"
                >
                  mdi-ticket-percent-outline
                </v-icon>
                <span
                  class="font-weight-medium"
                  :class="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0 ? 'text-red' : 'text-deep-purple'"
                >
                  {{ formatRupiah(calculateDiscountedPrice(item.price, calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage))) }}
                </span>
              </div>
            </div>
            <span v-if="hasVariations(item)" class="text-caption text-grey-darken-3 mt-1">
              {{ formatVariations(item.variant) }}
            </span>
          </div>
        </v-list-item-subtitle>
                
        <template v-slot:append>
          <v-btn 
            icon 
            size="small"
            color="error"
            variant="text"
            @click="removeFromCart(item.jubelio_item_id)"
            class="remove-btn"
            :loading="cartStore.isLoading"
          >
            <v-icon>mdi-delete-outline</v-icon>
          </v-btn>
        </template>
      </v-list-item>
    </v-list>
        
    <!-- Cart summary and actions -->
    <template v-if="cartStore.items.length > 0">
      <v-divider></v-divider>
            
      <div class="pa-4 cart-summary">
        <div class="d-flex justify-space-between align-center mb-2">
          <div>
            <div class="text-body-2 text-grey">Subtotal ({{ cartStore.itemCount }} quantity items)</div>
            <div class="text-h6 font-weight-bold">{{ formatRupiah(discountedTotal) }}</div>
          </div>
          <!-- <v-btn
            color="deep-purple"
            variant="text"
            @click="clearCart"
            prepend-icon="mdi-delete-sweep"
            density="comfortable"
            :loading="cartStore.isLoading"
          >
            Clear
          </v-btn> -->
        </div>
                
        <v-btn
          color="deep-purple"
          block
          size="large"
          class="mt-4 checkout-btn"
          to="/cart"
          @click="toggleCart"
          elevation="2"
        >
          <v-icon start>mdi-cart-outline</v-icon>
          View Cart & Checkout
        </v-btn>
                
        <div class="text-center mt-4">
          <v-btn
            variant="text"
            color="deep-purple"
            size="small"
            @click="toggleCart"
            class="continue-shopping"
          >
            <v-icon start size="small">mdi-arrow-left</v-icon>
            Continue Shopping
          </v-btn>
        </div>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { useCartStore } from '~/stores/cart';
import { useDashboardLayoutStore } from '~/stores/dashboard-layout';
import { useProductDiscountStore } from '~/stores/product-discount';
import { useDiscountCalculator } from '~/composables/useDiscountCalculator';
import type { CartProduct } from '~/schemas/otoapi/cart';

// Get cart data from store
const cartStore = useCartStore();
const productDiscountStore = useProductDiscountStore();
const { calculateDiscountedPrice, calculateDiscountPercentage } = useDiscountCalculator();
const dashboardStore = useDashboardLayoutStore();
const { toggleCart } = dashboardStore;
const { cartDrawer } = storeToRefs(dashboardStore);

const discountedTotal = computed(() => {
  return cartStore.items.reduce((total, item) => {
    const productDiscount = productDiscountStore.discounts.get(item.jubelio_item_group_id);
    const discountPercentage = calculateDiscountPercentage(productDiscount?.max_discount_percentage);
    return total + (calculateDiscountedPrice(item.price, discountPercentage) * item.quantity);
  }, 0);
});

watch(
  () => cartStore.items,
  (newItems) => {
    if (newItems.length > 0) {
      const itemGroupIds = newItems.map((item) => item.jubelio_item_group_id.toString());
      productDiscountStore.fetchBatchProductDiscounts(itemGroupIds);
    }
  },
  { deep: true, immediate: true }
);

// Updated to use async server sync
const removeFromCart = async (id: number | string): Promise<void> => {
  try {
    await cartStore.removeItem(id)
  } catch (error) {
    console.error('Failed to remove item from cart:', error)
    // You could show a toast notification here
  }
}

// Updated to use async server sync
const clearCart = async (): Promise<void> => {
  try {
    await cartStore.clearCart() // true = sync to server
  } catch (error) {
    console.error('Failed to clear cart:', error)
    // You could show a toast notification here
  }
}

const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const hasVariations = (item: CartProduct): boolean => {
  return Array.isArray(item.variant) && item.variant.length > 0;
}

const formatVariations = (variations: any[] | null): string => {
  if (!variations || variations.length === 0) return ''
  return variations.map(v => `${v.label}: ${v.value}`).join(', ')
}
</script>

<style scoped>
.cart-drawer {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 230px;
}

.cart-items {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.cart-item {
  transition: background-color 0.2s ease;
  border-radius: 8px;
  margin: 0 8px;
}

.cart-item:hover {
  background-color: rgba(103, 58, 183, 0.05);
}

.remove-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.remove-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.checkout-btn {
  border-radius: 8px;
  text-transform: none;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
}

.continue-shopping {
  text-transform: none;
  letter-spacing: 0;
}

.cart-summary {
  background-color: rgba(103, 58, 183, 0.03);
  border-top: 1px solid rgba(103, 58, 183, 0.1);
}

.hover-underline:hover {
  text-decoration: underline !important;
}
</style>
