<template>
  <v-footer
    app
    color=""
    dark
    class="text-caption text-disabled"
  >
    <span>&copy; {{ currentYear }} - Otoresell | Zafrada</span>
    <v-spacer></v-spacer>
    <span>v{{ version }}</span>
  </v-footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const config = useRuntimeConfig()
const version = config.public.app.version
const currentYear = computed(() => new Date().getFullYear())

</script>
