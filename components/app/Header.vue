<template>
  <v-app-bar app color="" dark>
    <v-app-bar-nav-icon @click="dashboardStore.toggleSidebar"></v-app-bar-nav-icon>
                    
    <!-- Logo instead of text -->
    <v-img
      :src="dashboardStore.otoresellLogoPath"
      alt="OTORESELL"
      max-width="150"
      height="40"
      contain
      class="ml-2"
    />
                    
    <v-spacer></v-spacer>
    
    <!-- Notification Badge -->
    <NotificationBadge />
                    
    <!-- Cart with Badge -->
    <v-btn 
      ref="cartIconRef"
      icon 
      class="mr-4" 
      @click="dashboardStore.toggleCart"
      id="header-cart-icon"
    >
      <v-badge
        :content="cartStore.itemCount.toString()"
        :value="cartStore.itemCount > 0"
        color="error"
        overlap
        :class="{ 'cart-bounce': isCartAnimating }"
      >
        <v-icon>mdi-cart</v-icon>
      </v-badge>
    </v-btn>
                    
    <!-- User Menu wrapped in ClientOnly -->
    <UserMenuWrapper />
  </v-app-bar>
</template>

<script setup lang="ts">
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'
import { useCartStore } from '~/stores/cart'
import { useAuthStore } from '~/stores/auth'
import UserMenuWrapper from '~/components/app/UserMenuWrapper.vue'
import NotificationBadge from '~/components/app/NotificationBadge.vue'
import { ref, watch } from 'vue'

const dashboardStore = useDashboardLayoutStore()
const cartStore = useCartStore()
const authStore = useAuthStore()

const isCartAnimating = ref(false)
const cartIconRef = ref<HTMLElement | null>(null)

// Expose cart icon position for animations
const getCartIconPosition = () => {
  if (!cartIconRef.value) return null
  
  const element = cartIconRef.value as HTMLElement
  const rect = element.getBoundingClientRect()
  
  return {
    x: rect.left + rect.width / 2,
    y: rect.top + rect.height / 2
  }
}

// Expose method globally for other components
if (import.meta.client) {
  (window as any).getHeaderCartPosition = getCartIconPosition
}

// Watch for changes in cart count and trigger animation when items are added
watch(() => cartStore.itemCount, (newCount, oldCount) => {
  // Trigger animation only when count increases (item added)
  if (newCount > oldCount) {
    isCartAnimating.value = true
    setTimeout(() => {
      isCartAnimating.value = false
    }, 600)
  }
})
</script>

<style scoped>
.cart-bounce {
  animation: cartBounce 0.6s ease-in-out;
}

@keyframes cartBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
