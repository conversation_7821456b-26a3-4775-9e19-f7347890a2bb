<template>
  <v-menu
    v-model="menu"
    :close-on-content-click="false"
    location="bottom end"
    offset="10"
    max-width="400"
    max-height="500"
  >
    <template v-slot:activator="{ props }">
      <v-btn
        icon
        class="mr-4"
        v-bind="props"
        :loading="loading"
      >
        <v-badge
          :content="unreadCount.toString()"
          :model-value="unreadCount > 0"
          color="error"
          overlap
        >
          <v-icon>mdi-bell</v-icon>
        </v-badge>
      </v-btn>
    </template>

    <v-card class="notification-card">
      <!-- Header -->
      <v-card-title class="d-flex justify-space-between align-center pa-4">
        <span class="text-h6">Notifications</span>
        <div class="d-flex ga-2">
          <v-btn
            v-if="unreadCount > 0"
            size="small"
            variant="text"
            color="deep-purple"
            @click="handleMarkAllAsRead"
            :loading="loading"
          >
            Mark all read
          </v-btn>
          <v-btn
            size="small"
            variant="text"
            color="error"
            @click="handleClearAll"
            :loading="loading"
          >
            Clear all
          </v-btn>
        </div>
      </v-card-title>

      <v-divider></v-divider>

      <!-- Notifications List -->
      <div class="notification-list notification-style">
        <template v-if="notifications.length > 0">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read_at }"
          >
            <div class="d-flex align-start pa-3">
              <!-- Notification Icon -->
              <v-avatar
                size="40"
                :color="getNotificationColor(notification)"
                class="mr-3"
              >
                <v-icon color="white" size="20">
                  {{ getNotificationIcon(notification) }}
                </v-icon>
              </v-avatar>

              <!-- Notification Content -->
              <div class="flex-grow-1" @click="handleNotificationClick(notification)">
                <div class="d-flex justify-space-between align-start">
                  <div class="notification-content">
                    <p class="text-body-2 mb-1 font-weight-medium" v-html="getNotificationTitle(notification)"></p>
                    <p class="text-caption text-medium-emphasis mb-1" v-html="getNotificationMessage(notification)"></p>
                    <p class="text-caption text-disabled">
                      {{ dayjs(notification.created_at).fromNow() }}
                    </p>
                  </div>

                  <!-- Action Buttons -->
                  <v-menu location="bottom end">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon
                        size="small"
                        variant="text"
                        v-bind="props"
                      >
                        <v-icon size="16">mdi-dots-vertical</v-icon>
                      </v-btn>
                    </template>
                    <v-list density="compact">
                      <v-list-item
                        v-if="!notification.read_at"
                        @click="handleMarkAsRead(notification.id)"
                      >
                        <v-list-item-title>Mark as read</v-list-item-title>
                      </v-list-item>
                      <v-list-item
                        @click="handleDelete(notification.id)"
                        class="text-error"
                      >
                        <v-list-item-title>Delete</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
            </div>
            <v-divider v-if="notification !== notifications[notifications.length - 1]"></v-divider>
          </div>
        </template>

        <!-- Empty State -->
        <template v-else>
          <div class="text-center pa-8">
            <v-icon size="64" color="grey-lighten-1" class="mb-4">
              mdi-bell-outline
            </v-icon>
            <p class="text-body-1 text-medium-emphasis">
              No notifications yet
            </p>
          </div>
        </template>
      </div>

      <!-- Load More Button -->
      <template v-if="hasNextPage && notifications.length > 0">
        <v-divider></v-divider>
        <div class="pa-3 text-center">
          <v-btn
            variant="text"
            color="primary"
            @click="handleLoadMore"
            :loading="loading"
            block
          >
            Load more
          </v-btn>
        </div>
      </template>
    </v-card>
  </v-menu>
</template>

<script setup lang="ts">
import { useNotificationAppStore } from '~/stores/notification-app'
import type { Notification } from '~/schemas/otoapi/notification'
import { useNotificationPolling } from '~/composables/useNotificationPolling'
import { storeToRefs } from 'pinia'
import { useSnackbar } from '~/composables/useSnackbar'
import { useNotificationUtils } from '~/composables/useNotificationUtils'
import { useDayjs } from '#dayjs'

const dayjs = useDayjs()
const notificationStore = useNotificationAppStore()
const { showSnackbar } = useSnackbar()
const { startPolling, stopPolling } = useNotificationPolling(10)
const {
  getNotificationIcon,
  getNotificationColor,
  getNotificationTitle,
  getNotificationMessage,
  getNotificationLink,
} = useNotificationUtils()

const {
  notifications,
  loading,
  unreadCount,
  hasNextPage,
  pagination
} = storeToRefs(notificationStore)

const menu = ref(false)

// Actions
const handleMarkAsRead = async (id: string) => {
  try {
    await notificationStore.markNotificationAsRead(id)
    showSnackbar('Notification marked as read', 'success')
  } catch (error) {
    showSnackbar('Failed to mark notification as read', 'error')
  }
}

const handleMarkAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead()
    showSnackbar('All notifications marked as read', 'success')
  } catch (error) {
    showSnackbar('Failed to mark all notifications as read', 'error')
  }
}

const handleDelete = async (id: string) => {
  try {
    await notificationStore.deleteNotification(id)
    showSnackbar('Notification deleted', 'success')
  } catch (error) {
    showSnackbar('Failed to delete notification', 'error')
  }
}

const handleClearAll = async () => {
  try {
    await notificationStore.clearAllNotifications()
    showSnackbar('All notifications cleared', 'success')
  } catch (error) {
    showSnackbar('Failed to clear notifications', 'error')
  }
}

const handleLoadMore = async () => {
  try {
    await notificationStore.fetchNotifications(pagination.value.current_page + 1, 15)
  } catch (error) {
    showSnackbar('Failed to load more notifications', 'error')
  }
}

const handleNotificationClick = async (notification: Notification) => {
  const link = getNotificationLink(notification)
  
  if (link) {
    if (!notification.read_at) {
      await handleMarkAsRead(notification.id)
    }
    await navigateTo(link)
    menu.value = false
  }
}

// Lifecycle
onMounted(async () => {
  try {
    await notificationStore.fetchNotifications()
    startPolling()
  } catch (error) {
    console.error('Failed to load notifications:', error)
  }
})

onUnmounted(() => {
  stopPolling()
})

watch(menu, async (isOpen) => {
  if (isOpen) {
    try {
      await notificationStore.fetchNotifications(1, 15)
    } catch (error) {
      console.error('Failed to refresh notifications:', error)
    }
  }
})
</script>

<style scoped>
.notification-card {
  min-width: 400px;
}

.notification-item {
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.notification-item.unread {
  background-color: rgba(25, 118, 210, 0.08);
  border-left: 4px solid rgb(25, 118, 210);
}

.notification-content {
  max-width: 250px;
}

.notification-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: transparent;
}

.notification-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.notification-style {
  max-height: 400px; 
  overflow-y: auto;
}
</style>
