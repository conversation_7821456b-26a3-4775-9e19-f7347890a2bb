<template>
  <v-navigation-drawer
    v-model="drawer"
    :rail="rail"
    permanent
    app
    class="bg-deep-purple"
    theme="dark"
    width="220"
  >
    <v-list density="compact" nav>
      <v-list-item prepend-icon="mdi-view-dashboard" title="Dashboard" to="/"></v-list-item>
      <v-list-item 
        prepend-icon="mdi-shopping-search" 
        title="Products" 
        @click="navigateToProducts"
        :active="$route.path.startsWith('/products')"
        :exact="false"
      ></v-list-item>
      <v-list-item 
        prepend-icon="mdi-cart-variant" 
        title="Cart" 
        to="/cart"
        :active="$route.path.startsWith('/cart')"
        :exact="false"
      ></v-list-item>
      <v-list-item 
        prepend-icon="mdi-map-marker-multiple-outline" 
        title="Addresses" 
        to="/addresses"
        :active="$route.path.startsWith('/addresses')"
        :exact="false"
      ></v-list-item>
      <v-list-item 
        prepend-icon="mdi-clipboard-text-clock-outline" 
        title="Transactions" 
        to="/transactions"
        :active="$route.path.startsWith('/transactions')"
        :exact="false"
      ></v-list-item>
      <v-list-item 
        prepend-icon="mdi-bell-outline" 
        title="Notifications" 
        to="/notifications"
        :active="$route.path.startsWith('/notifications')"
        :exact="false"
      ></v-list-item>
      <v-list-item 
        prepend-icon="mdi-cog" 
        title="Settings" 
        to="/settings"
        :active="$route.path.startsWith('/settings')"
        :exact="false"
      ></v-list-item>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'
import { useProductStore } from '~/stores/product'

const dashboardStore = useDashboardLayoutStore()
const productStore = useProductStore()
const { drawer, rail } = storeToRefs(dashboardStore)

// Function to navigate to the products page
const navigateToProducts = () => {
  productStore.resetFilters();
  navigateTo('/products');
}

</script>
