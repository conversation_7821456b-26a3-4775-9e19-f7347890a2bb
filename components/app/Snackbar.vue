<template>
  <v-snackbar
    v-model="snackbarStore.show"
    :color="snackbarStore.color"
    :timeout="snackbarStore.timeout"
    location="top"
    @update:model-value="onClose"
  >
    {{ snackbarStore.message }}
    <template #actions>
      <v-btn
        variant="text"
        @click="snackbarStore.hideSnackbar"
      >
        Close
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script setup lang="ts">
import { useSnackbarStore } from '~/stores/snackbar'

const snackbarStore = useSnackbarStore()

const onClose = (value: boolean) => {
  if (!value) {
    snackbarStore.hideSnackbar()
  }
}
</script>