<template>
  <div class="splash-screen">
    <div class="splash-content">
      <v-img
        :src="otoresellLogoPath"
        alt="OTORESELL"
        width="200"
        height="60"
        contain
        class="mb-4"
      />
      <v-progress-circular
        indeterminate
        color="purple"
        size="30"
        width="4"
      ></v-progress-circular>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'

const dashboardStore = useDashboardLayoutStore()
const { otoresellLogoPath } = storeToRefs(dashboardStore)
</script>

<style scoped>
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #72457911 0%, #dbb1f7ca 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.splash-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
