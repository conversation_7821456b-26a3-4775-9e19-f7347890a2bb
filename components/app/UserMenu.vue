<template>
  <v-menu
    v-model="userMenuOpen"
    :close-on-content-click="false"
    location="bottom end"
    min-width="200"
  >
    <template v-slot:activator="{ props }">
      <v-btn
        v-bind="props"
        class="text-none"
        variant="text"
      >
        <v-avatar size="32" color="deep-purple" class="mr-2">
          <!-- <v-img
            v-if="user?.avatar"
            :src="user.avatar"
            alt="User"
          ></v-img> -->
          <span class="text-h6 text-white">{{ userInitial }}</span>
        </v-avatar>
        {{ userFullName }}
        <v-icon right>mdi-chevron-down</v-icon>
      </v-btn>
    </template>
    <v-card>
      <v-list>
        <v-list-item>
          <template v-slot:prepend>
            <v-avatar size="40" color="deep-purple">
              <!-- <v-img
                v-if="user?.avatar"
                :src="user.avatar"
                alt="User"
              ></v-img> -->
              <span class="avatar-text-large">{{ userInitial }}</span>
            </v-avatar>
          </template>
          <v-list-item-title>{{ userFullName }}</v-list-item-title>
          <v-list-item-subtitle>{{ user?.email || '' }}</v-list-item-subtitle>
        </v-list-item>
      </v-list>
      <v-divider></v-divider>
      <v-list density="compact">
        <v-list-item prepend-icon="mdi-cog-outline" title="Settings" to="/settings"></v-list-item>
      </v-list>
      <v-divider></v-divider>
      <v-card-actions>
        <v-btn
          color="deep-purple"
          variant="text"
          block
          @click="logout()"
        >
          <v-icon left>mdi-logout</v-icon>
          Logout
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-menu>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '~/stores/auth'

// User menu state
const userMenuOpen = ref<boolean>(false)

// Get user data from auth store
const authStore = useAuthStore()
const { user, userFullName } = storeToRefs(authStore)
const { logout } = authStore

// Compute user initial for avatar
const userInitial = computed(() => {
  if (user.value?.name) {
    return user.value.name.charAt(0).toUpperCase()
  }
  return ''
})
</script>

<style scoped>
/* Additional styling for avatar text */
.v-avatar span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.avatar-text-large {
  color: white;
  font-size: 1.25rem;
}
</style>
