<template>
  <ClientOnly>
    <AppUserMenu />
    <template #fallback>
      <!-- Server-side placeholder that matches initial client state -->
      <v-btn class="text-none" variant="text">
        <v-avatar size="32" color="deep-purple" class="mr-2">
          <span class="text-h6 text-white">G</span>
        </v-avatar>
        Guest
        <v-icon right>mdi-chevron-down</v-icon>
      </v-btn>
    </template>
  </ClientOnly>
</template>
