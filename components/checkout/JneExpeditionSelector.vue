<template>
  <div>
    <!-- Disabled State (No Address Selected) -->
    <div v-if="!shippingAddress" class="text-center py-6">
      <v-icon size="48" color="grey-lighten-2" class="mb-3">mdi-truck-delivery-outline</v-icon>
      <p class="text-grey-darken-1">Please select a shipping address first</p>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="text-center py-6">
      <v-progress-circular indeterminate color="deep-purple" class="mb-3"></v-progress-circular>
      <p class="text-body-2 text-grey-darken-1">Calculating JNE shipping options...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-6 px-2">
      <v-alert type="error" class="mb-4">
        {{ error }}
      </v-alert>
      <v-btn color="deep-purple" @click="calculateShipping">
        Try Again
      </v-btn>
    </div>

    <!-- Shipping Methods List -->
    <div v-else-if="shippingMethods.length > 0">
      <v-radio-group 
        v-model="selectedMethod" 
        @update:model-value="onMethodSelected"
      >
        <v-list class="pa-2 ma-2 rounded border">
          <v-list-item
            v-for="(method, index) in shippingMethods"
            :key="index"
            :class="{ 'selected-method': selectedMethod === method.id }"
            class="shipping-method-item pa-3"
          >
            <template v-slot:prepend>
              <v-radio :value="method.id" color="deep-purple"></v-radio>
            </template>

            <v-list-item-title>
              <div class="d-flex justify-space-between align-center">
                <div class="d-flex align-center">
                  <v-img
                    :src="method.logo"
                    height="30"
                    width="60"
                    contain
                    class="mr-3"
                    :alt="method.name"
                  >
                    <template v-slot:error>
                      <div class="d-flex align-center justify-center fill-height">
                        <v-icon color="grey">mdi-truck</v-icon>
                      </div>
                    </template>
                  </v-img>
                  <div>
                    <div class="font-weight-medium text-deep-purple-darken-1">
                      {{ method.name }}
                    </div>
                    <div class="text-caption text-grey-darken-1">
                      {{ method.description }}
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-subtitle-2 font-weight-bold text-deep-purple">
                    {{ formatRupiah(method.price) }}
                  </div>
                  <div v-if="method.etd" class="text-caption text-grey-darken-1">
                    {{ method.etd }} days
                  </div>
                </div>
              </div>
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-radio-group>
    </div>

    <!-- No Methods Available -->
    <div v-else class="text-center py-6">
      <v-icon size="48" color="grey-lighten-2" class="mb-3">mdi-truck-off</v-icon>
      <p class="text-grey-darken-1 mb-4">No JNE shipping options available for your location</p>
      <v-btn color="deep-purple" @click="calculateShipping">
        Generate
      </v-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useJneShipping } from '~/composables/useJneShipping'
import type { ShippingAddress } from '~/schemas/otoapi/shipping-address'

// Props & Emits
interface Props {
  shippingAddress?: ShippingAddress | null
  cartItems?: any[]
  modelValue?: string | null
}

interface Emits {
  (e: 'update:modelValue', value: string | null): void
  (e: 'method-selected', method: any): void
  (e: 'shipping-calculated', methods: any[]): void
  (e: 'auto-selected', method: any): void
}

const props = withDefaults(defineProps<Props>(), {
  shippingAddress: null,
  cartItems: () => [],
  modelValue: null
})

const emit = defineEmits<Emits>()

// Composables
const {
  origins,
  destinations,
  prices,
  loading,
  error,
  fetchOrigins,
  fetchDestinations,
  getPrices,
  transformJnePrices,
} = useJneShipping()

// State
const shippingMethods = ref<any[]>([])
const selectedMethod = ref<string | null>(props.modelValue)

// Computed
const totalWeight = computed(() => {
  const weight = props.cartItems?.reduce((total, item) => {
    return total + (item.weight || 0) * item.quantity
  }, 0) || 0
  // convert to kg and round up
  return Math.ceil(weight / 1000)
})

// Methods
const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const calculateShipping = async () => {
  if (!props.shippingAddress || totalWeight.value === 0) {
    return
  }

  await fetchOrigins()
  await fetchDestinations(props.shippingAddress)

  if (origins.value.length > 0 && destinations.value.length > 0) {
    await getPrices(origins.value[0].origin_code, destinations.value[0].tariff_code, totalWeight.value)
    
    const transformedPrices = transformJnePrices(prices.value)
    shippingMethods.value = transformedPrices
    emit('shipping-calculated', transformedPrices)

    if (transformedPrices.length > 0 && !selectedMethod.value) {
      const firstMethod = transformedPrices[0]
      selectedMethod.value = firstMethod.id
      onMethodSelected(firstMethod.id)
      emit('auto-selected', firstMethod)
    }
  }
}

const onMethodSelected = (methodId: string | null) => {
  if (!methodId) {
    emit('update:modelValue', null)
    return
  }
  
  const method = shippingMethods.value.find(m => m.id === methodId)
  if (method) {
    emit('update:modelValue', methodId)
    emit('method-selected', method)
  }
}

// Watchers
watch(() => props.shippingAddress, (newAddress, oldAddress) => {
  if (newAddress && newAddress.id !== oldAddress?.id) {
    selectedMethod.value = null
    shippingMethods.value = []
    calculateShipping()
  }
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  selectedMethod.value = newValue
})

watch(() => props.cartItems, () => {
  if (props.shippingAddress) {
    calculateShipping()
  }
}, { deep: true })

onMounted(() => {
  if (props.shippingAddress) {
    calculateShipping()
  }
})
</script>

<style scoped>
.shipping-method-item {
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.shipping-method-item:hover {
  background-color: #f5f0ff;
}

.selected-method {
  background-color: #f5f0ff;
  border-left: 4px solid #673ab7;
}
</style>