<template>
  <div>
    <v-radio-group
      v-model="selectedMethod"
      @update:model-value="onMethodSelected"
    >
      <v-list class="pa-2 ma-2 rounded border">
        <v-list-item
          v-for="method in methods"
          :key="method.code"
          :class="{ 'selected-method': selectedMethod === method.code }"
          class="shipping-method-item pa-3"
        >
          <template v-slot:prepend>
            <v-radio :value="method.code" color="deep-purple" @click.stop="onMethodSelected(method.code)"></v-radio>
          </template>

          <v-list-item-title>
            <div class="d-flex justify-space-between align-center">
              <div class="d-flex align-center">
                <v-img
                  :src="method.logo"
                  height="30"
                  width="60"
                  contain
                  class="mr-3"
                  :alt="method.name"
                >
                  <template v-slot:error>
                    <div class="d-flex align-center justify-center fill-height">
                      <v-icon color="grey">mdi-truck</v-icon>
                    </div>
                  </template>
                </v-img>
                <div>
                  <div class="font-weight-medium text-deep-purple-darken-1">
                    {{ method.name }}
                  </div>
                  <div class="text-caption text-grey-darken-1">
                    {{ method.description }}
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-subtitle-2 font-weight-bold text-deep-purple">
                  {{ formatRupiah(method.cost) }}
                </div>
              </div>
            </div>
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-radio-group>

    <div v-if="selectedMethod" class="pa-4">
      <v-text-field
        v-model="trackingNumber"
        label="Tracking Number / Booking Code"
        variant="outlined"
        density="compact"
        color="deep-purple"
        @input="onTrackingNumberInput"
        hint="Required for online recipient shipping"
        persistent-hint
      ></v-text-field>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  methods: {
    type: Array as () => any[],
    required: true
  },
  modelValue: {
    type: String as () => string | null,
    default: null
  }
})

const emit = defineEmits(['method-selected', 'tracking-number-updated', 'update:modelValue'])

const selectedMethod = ref<string | null>(props.modelValue)
const trackingNumber = ref('')

const onMethodSelected = (methodCode: string | null) => {
  const selected = props.methods.find(m => m.code === methodCode)
  if (selected) {
    emit('method-selected', selected)
    emit('update:modelValue', selected.code)
  } else {
    emit('update:modelValue', null)
  }
}

const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

const onTrackingNumberInput = () => {
  emit('tracking-number-updated', trackingNumber.value)
}

watch(() => props.methods, (newMethods) => {
  if (newMethods && newMethods.length > 0) {
    const currentSelectionValid = newMethods.some(m => m.code === selectedMethod.value);
    if (!currentSelectionValid) {
      selectedMethod.value = newMethods[0].code;
      onMethodSelected(newMethods[0].code);
    }
  } else {
    selectedMethod.value = null;
    onMethodSelected(null);
  }
}, { immediate: true, deep: true });
</script>

<style scoped>
.shipping-method-item {
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.shipping-method-item:hover {
  background-color: #f5f0ff;
}

.selected-method {
  background-color: #f5f0ff;
  border-left: 4px solid #673ab7;
}
</style>