<template>
  <div>
    <!-- Address Selection Button -->
    <div v-if="!selectedAddress" class="text-center py-6">
      <v-icon size="48" color="grey-lighten-1" class="mb-3">mdi-map-marker-outline</v-icon>
      <p class="text-grey-darken-1 mb-4">Please select a shipping address to continue</p>
      <v-btn 
        color="deep-purple" 
        size="large"
        @click="openAddressDialog"
        prepend-icon="mdi-map-marker-plus"
      >
        Select Shipping Address
      </v-btn>
    </div>

    <!-- Selected Address Display -->
    <div v-else class="d-flex align-center">
      <div class="flex-grow-1">
        <div class="d-flex align-center mb-1">
          <v-icon color="deep-purple" class="mr-2">mdi-map-marker</v-icon>
          <span class="font-weight-medium text-deep-purple-darken-1">
            {{ selectedAddress.shipping_full_name }}
          </span>
        </div>
        <div class="text-body-2 text-grey-darken-1 ml-8">
          {{ selectedAddress.shipping_phone }}
        </div>
        <div class="text-body-2 mt-2 text-deep-purple-lighten-2 ml-8">
          {{ selectedAddress.shipping_address }}
        </div>
        <div class="text-body-2 text-deep-purple-lighten-2 ml-8">
          {{ selectedAddress.shipping_subdistrict }}, {{ selectedAddress.shipping_district }}, 
          {{ selectedAddress.shipping_city }}, {{ selectedAddress.shipping_province }}
          <span v-if="selectedAddress.shipping_post_code">, {{ selectedAddress.shipping_post_code }}</span>
        </div>
      </div>
      <v-btn
        color="deep-purple-darken-3"
        variant="plain"
        size="small"
        @click="openAddressDialog"
      >
        Change
      </v-btn>
    </div>

    <!-- Address Selection Dialog -->
    <v-dialog v-model="showDialog" max-width="800px" persistent>
      <v-card>
        <v-card-title class="d-flex align-center pa-4 bg-deep-purple text-white">
          <v-icon class="mr-2">mdi-map-marker-multiple</v-icon>
          <span>Select Shipping Address</span>
          <v-spacer></v-spacer>
          <v-btn
            icon="mdi-close"
            variant="text"
            color="white"
            @click="closeDialog"
          ></v-btn>
        </v-card-title>

        <v-card-text class="pa-0">
          <!-- Search Bar with Add Button -->
          <div class="pa-4 border-b">
            <div class="d-flex ga-2">
              <v-text-field
                v-model="searchQuery"
                placeholder="Search addresses..."
                prepend-inner-icon="mdi-magnify"
                variant="outlined"
                density="compact"
                hide-details
                @input="debouncedSearch"
                class="flex-grow-1"
              ></v-text-field>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="shippingAddressStore.loading" class="text-center py-8">
            <v-progress-circular indeterminate color="deep-purple" class="mb-2"></v-progress-circular>
            <div class="text-body-2 text-grey-darken-1">Loading addresses...</div>
          </div>

          <!-- Error State -->
          <div v-else-if="shippingAddressStore.error" class="pa-4">
            <v-alert type="error" class="mb-3">
              {{ shippingAddressStore.error }}
            </v-alert>
            <v-btn color="deep-purple" @click="loadAddresses">
              Try Again
            </v-btn>
          </div>

          <!-- Address List -->
          <div v-else-if="shippingAddressStore.addresses.length > 0">
            <v-list class="ma-4 pa-2 rounded border">
              <v-list-item
                v-for="address in shippingAddressStore.addresses"
                :key="address.id"
                @click="selectAddress(address)"
                :class="{ 'selected-address': selectedAddress?.id === address.id }"
                class="address-item"
              >
                <template v-slot:prepend>
                  <v-radio
                    :model-value="selectedAddress?.id"
                    :value="address.id"
                    color="deep-purple"
                    @click.stop="selectAddress(address)"
                  ></v-radio>
                </template>

                <v-list-item-title>
                  <div class="d-flex align-center mb-1">
                    <span class="font-weight-medium text-deep-purple-darken-1">
                      {{ address.shipping_full_name }}
                    </span>
                  </div>
                  <div class="text-body-2 text-grey-darken-1">
                    {{ address.shipping_phone }}
                  </div>
                  <div class="text-body-2 mt-1 text-deep-purple-lighten-2">
                    {{ address.shipping_address }}
                  </div>
                  <div class="text-body-2 text-deep-purple-lighten-2">
                    {{ address.shipping_subdistrict }}, {{ address.shipping_district }}, 
                    {{ address.shipping_city }}, {{ address.shipping_province }}
                    <span v-if="address.shipping_post_code">, {{ address.shipping_post_code }}</span>
                  </div>
                </v-list-item-title>
              </v-list-item>
            </v-list>

            <!-- Pagination -->
            <div v-if="shippingAddressStore.totalCount > perPage" class="pa-4 border-t">
              <v-pagination
                v-model="currentPage"
                :length="Math.ceil(shippingAddressStore.totalCount / perPage)"
                color="deep-purple"
                @update:model-value="loadAddresses"
              ></v-pagination>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-8">
            <v-icon size="64" color="grey-lighten-2" class="mb-3">mdi-map-marker-off</v-icon>
            <p class="text-grey-darken-1 mb-4">No shipping addresses found</p>
            <v-btn color="deep-purple" @click="openAddressForm">
              Add New Address
            </v-btn>
          </div>
        </v-card-text>

        <!-- Updated Footer -->
        <v-card-actions class="pa-4 border-t">
          <v-btn
            color="deep-purple"
            variant="outlined"
            @click="openAddressForm"
            prepend-icon="mdi-plus"
          >
            Add New Address
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="closeDialog">
            Cancel
          </v-btn>
          <v-btn
            color="deep-purple"
            :disabled="!selectedAddress"
            @click="confirmSelection"
          >
            Confirm Selection
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Address Form Dialog -->
    <AddressForm
      v-model="showAddressForm"
      :editing-address="null"
      @address-saved="onAddressSaved"
      @address-canceled="onAddressCanceled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useShippingAddressStore } from '~/stores/shipping-address'
import { useAuthStore } from '~/stores/auth'
import AddressForm from '~/components/addresses/AddressForm.vue'
import type { ShippingAddress } from '~/schemas/otoapi/shipping-address'

// Props & Emits
interface Props {
  modelValue?: ShippingAddress | null
}

interface Emits {
  (e: 'update:modelValue', value: ShippingAddress | null): void
  (e: 'address-selected', address: ShippingAddress): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
})

const emit = defineEmits<Emits>()

// Stores
const shippingAddressStore = useShippingAddressStore()
const authStore = useAuthStore()

// State
const showDialog = ref(false)
const showAddressForm = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const perPage = ref(10)
const selectedAddress = ref<ShippingAddress | null>(props.modelValue)

// Computed
const debouncedSearch = (() => {
  let timeout: NodeJS.Timeout
  return () => {
    clearTimeout(timeout)
    timeout = setTimeout(() => {
      currentPage.value = 1
      loadAddresses()
    }, 500)
  }
})()

// Methods
const openAddressDialog = () => {
  showDialog.value = true
  loadAddresses()
}

const openAddressForm = () => {
  showAddressForm.value = true
}

const closeDialog = () => {
  showDialog.value = false
  searchQuery.value = ''
  currentPage.value = 1
}

const loadAddresses = async () => {
  if (!authStore.user?.id) return
  
  try {
    // Use fetchAddresses with search parameter instead of getAddressesByUser
    await shippingAddressStore.fetchAddresses({
      page: currentPage.value,
      per_page: perPage.value,
      search: searchQuery.value || undefined, // Only include search if not empty
      sort_by: 'created_at',
      sort_direction: 'desc'
    })
  } catch (error) {
    console.error('Failed to load addresses:', error)
  }
}

const selectAddress = (address: ShippingAddress) => {
  selectedAddress.value = address
}

const confirmSelection = () => {
  if (selectedAddress.value) {
    emit('update:modelValue', selectedAddress.value)
    emit('address-selected', selectedAddress.value)
    closeDialog()
  }
}

const onAddressSaved = async (result: { success: boolean; message: string; addressId?: number }) => {
  if (result.success) {
    // Refresh the address list
    await loadAddresses()
    
    // Show success message (you can use a toast/snackbar here)
    console.log(result.message)
    
    // Optionally auto-select the new address
    if (result.addressId) {
      const newAddress = shippingAddressStore.addresses.find(addr => addr.id === result.addressId)
      if (newAddress) {
        selectAddress(newAddress)
      }
    }
  } else {
    // Show error message
    console.error(result.message)
  }
}

const onAddressCanceled = () => {
  showAddressForm.value = false
}

// const createNewAddress = () => {
//   // Navigate to address creation page or open creation dialog
//   navigateTo('/settings/addresses/create')
// }

// Lifecycle
onMounted(() => {
  if (props.modelValue) {
    selectedAddress.value = props.modelValue
  }
})

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  selectedAddress.value = newValue
})
</script>

<style scoped>
.address-item {
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.address-item:hover {
  background-color: #f5f0ff;
}

.selected-address {
  background-color: #f5f0ff;
  border-left: 4px solid #673ab7;
}

.border-b {
  border-bottom: 1px solid #e0e0e0;
}

.border-t {
  border-top: 1px solid #e0e0e0;
}
</style>
