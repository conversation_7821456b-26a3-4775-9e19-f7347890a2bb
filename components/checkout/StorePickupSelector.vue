<template>
  <v-list class="pa-2 ma-2 rounded border">
    <v-list-item
      :key="pickupMethod.code"
      @click="selectMethod"
      :class="{ 'selected-method': modelValue === pickupMethod.code }"
      class="shipping-method-item pa-3"
    >
      <template v-slot:prepend>
        <v-radio
          :value="pickupMethod.code"
          :model-value="modelValue"
          color="deep-purple"
        ></v-radio>
      </template>
      <v-list-item-title class="d-flex justify-space-between align-center">
        <div class="d-flex align-center">
          <v-img
            v-if="pickupMethod.logo"
            :src="pickupMethod.logo"
            width="40"
            height="40"
            contain
            class="mr-4"
          ></v-img>
          <div>
            <div class="font-weight-medium text-deep-purple-darken-1">{{ pickupMethod.name }}</div>
            <div class="text-caption text-grey-darken-1">{{ pickupMethod.description }}</div>
          </div>
        </div>
        <div class="text-right">
          <div class="font-weight-bold text-subtitle-1">{{ formatRupiah(pickupMethod.cost) }}</div>
          <div class="text-caption text-grey">{{ pickupMethod.etd }}</div>
        </div>
      </v-list-item-title>
    </v-list-item>
  </v-list>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useCheckoutStore } from '~/stores/checkout'

const props = defineProps<{
  modelValue?: string | null
}>()

const emit = defineEmits(['update:modelValue', 'method-selected'])

const checkoutStore = useCheckoutStore()
const { storePickupShippingMethod } = storeToRefs(checkoutStore)

const pickupMethod = computed(() => storePickupShippingMethod.value)

const selectMethod = () => {
  emit('update:modelValue', pickupMethod.value.code)
  emit('method-selected', pickupMethod.value)
}

const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Auto-select the method on component mount if it's not already selected
onMounted(() => {
  if (props.modelValue !== pickupMethod.value.code) {
    selectMethod()
  }
})
</script>

<style scoped>
.shipping-method-item {
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.shipping-method-item:hover {
  background-color: #f5f0ff;
}

.selected-method {
  background-color: #f5f0ff;
  border-left: 4px solid #673ab7;
}
</style>