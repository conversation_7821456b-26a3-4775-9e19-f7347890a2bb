<template>
  <v-col cols="12" sm="6" md="3">
    <v-card :color="color" class="mx-auto" min-height="150px">
      <div v-if="loading" class="loading-container">
        <v-progress-circular indeterminate color="white" size="32"></v-progress-circular>
      </div>
      <template v-else>
        <v-card-text>
          <div class="text-h4 text-white">{{ value }}</div>
          <div class="text-subtitle-1 text-white">{{ title }}</div>
        </v-card-text>
        <v-card-actions>
          <v-btn variant="text" color="white" :to="to">
            View Details
            <v-icon end>mdi-arrow-right</v-icon>
          </v-btn>
        </v-card-actions>
      </template>
    </v-card>
  </v-col>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true,
  },
  value: {
    type: [String, Number],
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: 'primary',
  },
  to: {
    type: String,
    required: true,
  },
});
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}
</style>