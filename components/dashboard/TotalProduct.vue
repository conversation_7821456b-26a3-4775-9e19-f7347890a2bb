<template>
  <StatCard
    title="Total Products"
    :value="totalProducts"
    :loading="isLoading"
    color="success"
    to="/products"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useProductStore } from '~/stores/product';
import StatCard from '~/components/dashboard/StatCard.vue';

const productStore = useProductStore();
const isLoading = ref(true);

const totalProducts = computed(() => productStore.totalCount || 0);

onMounted(async () => {
  try {
    await productStore.fetchInventoryItems({
      page: 1,
      pageSize: 1,
    });
  } catch (error) {
    console.error('Error fetching product count:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>
