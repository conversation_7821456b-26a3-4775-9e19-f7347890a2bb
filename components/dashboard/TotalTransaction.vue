<template>
  <StatCard
    title="Total Orders"
    :value="totalTransaction"
    :loading="isLoading"
    color="primary"
    to="/transactions"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useTransactionStore } from '~/stores/transaction';
import StatCard from '~/components/dashboard/StatCard.vue';

const transactionStore = useTransactionStore();
const isLoading = ref(true);

const totalTransaction = computed(() => transactionStore.totalCount || 0);

onMounted(async () => {
  try {
    await transactionStore.fetchOrders({
      page: 1,
      per_page: 1,
    });
  } catch (error) {
    console.error('Error fetching total transactions:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>
