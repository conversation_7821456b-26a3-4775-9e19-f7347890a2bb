<template>
  <div class="page-title-container mb-6">
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 font-weight-medium">
          <v-icon v-if="icon" :icon="icon" color="deep-purple" class="mr-2"></v-icon>
          {{ title }}
        </h1>
        <p v-if="subtitle" class="text-subtitle-1 text-grey-darken-1 mt-1">{{ subtitle }}</p>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.page-title-container {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-bottom: 16px;
}
</style>