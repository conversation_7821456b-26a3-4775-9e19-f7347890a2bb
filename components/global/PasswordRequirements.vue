<template>
  <div class="password-requirements text-caption text-medium-emphasis ml-1">
    <p :class="{ 'text-success': passwordValidation.length }">
      <v-icon size="x-small" :color="passwordValidation.length ? 'success' : ''">{{ passwordValidation.length ? 'mdi-check' : 'mdi-circle-small' }}</v-icon>
      At least 8 characters
    </p>
    <p :class="{ 'text-success': passwordValidation.lower }">
      <v-icon size="x-small" :color="passwordValidation.lower ? 'success' : ''">{{ passwordValidation.lower ? 'mdi-check' : 'mdi-circle-small' }}</v-icon>
      A lowercase letter
    </p>
    <p :class="{ 'text-success': passwordValidation.upper }">
      <v-icon size="x-small" :color="passwordValidation.upper ? 'success' : ''">{{ passwordValidation.upper ? 'mdi-check' : 'mdi-circle-small' }}</v-icon>
      An uppercase letter
    </p>
    <p :class="{ 'text-success': passwordValidation.number }">
      <v-icon size="x-small" :color="passwordValidation.number ? 'success' : ''">{{ passwordValidation.number ? 'mdi-check' : 'mdi-circle-small' }}</v-icon>
      A number
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  password?: string
}>()

const passwordValidation = computed(() => {
  const password = props.password || '';
  return {
    length: password.length >= 8,
    lower: /[a-z]/.test(password),
    upper: /[A-Z]/.test(password),
    number: /[0-9]/.test(password),
  };
});
</script>