<template>
  <div class="d-flex align-start">
    <v-avatar :color="getNotificationColor(notification)" size="40" class="mr-4">
      <v-icon>{{ getNotificationIcon(notification) }}</v-icon>
    </v-avatar>
    <div>
      <p class="font-weight-bold" v-html="getNotificationTitle(notification)"></p>
      <p v-html="getNotificationMessage(notification)"></p>
      <div class="text-caption text-disabled mt-2">
        {{ dayjs(notification.created_at).fromNow() }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/schemas/otoapi/notification'
import { useNotificationUtils } from '~/composables/useNotificationUtils'
import { useDayjs } from '#dayjs'

const dayjs = useDayjs()
const {
  getNotificationIcon,
  getNotificationColor,
  getNotificationTitle,
  getNotificationMessage,
} = useNotificationUtils()

defineProps<{
  notification: Notification
}>()
</script>