<template>
  <div class="d-flex align-start">
    <v-avatar color="success" size="40" class="mr-4">
      <v-icon>mdi-plus-circle-outline</v-icon>
    </v-avatar>
    <div>
      <p class="font-weight-bold">{{ notification.data.title }}</p>
      <p>
        New order
        <strong>#{{ notification.data.order.jubelio_order_id }}</strong>
        for <strong>Rp {{ notification.data.order.grand_total }}</strong>
        was created by <strong>{{ notification.data.created_by.name }}</strong>.
      </p>
      <div class="text-caption text-disabled mt-2">
        {{ dayjs(notification.created_at).fromNow() }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/schemas/otoapi/notification'
import { useDayjs } from '#dayjs'

const dayjs = useDayjs()

defineProps<{
  notification: Notification & { data: { type: 'order', action: 'created' } }
}>()
</script>