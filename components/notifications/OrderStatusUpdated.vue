<template>
  <div class="d-flex align-start">
    <v-avatar :color="statusColor(notification.data.order.new_status)" size="40" class="mr-4">
      <v-icon>{{ statusIcon(notification.data.order.new_status) }}</v-icon>
    </v-avatar>
    <div>
      <p class="font-weight-bold">{{ notification.data.title }}</p>
      <p>
        Status for order <strong>#{{ notification.data.order.jubelio_order_id }}</strong>
        by <strong>{{ notification.data.order_owner.name }}</strong> was updated.
      </p>
      <div class="d-flex align-center my-1">
        <v-chip size="small" class="mr-2">{{ notification.data.order.old_status }}</v-chip>
        <v-icon>mdi-arrow-right</v-icon>
        <v-chip :color="statusColor(notification.data.order.new_status)" size="small" class="ml-2">
          {{ notification.data.order.new_status }}
        </v-chip>
      </div>
      <div class="text-caption text-disabled mt-2">
        {{ dayjs(notification.created_at).fromNow() }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/schemas/otoapi/notification'
import { useDayjs } from '#dayjs'

const dayjs = useDayjs()

defineProps<{
  notification: Notification & { data: { type: 'order', action: 'status_updated' } }
}>()

const statusColor = (status?: string) => {
  switch (status) {
    case 'paid':
    case 'delivered':
    case 'shipped':
      return 'success'
    case 'cancelled':
    case 'refunded':
      return 'error'
    case 'processing':
      return 'info'
    default:
      return 'grey'
  }
}

const statusIcon = (status?: string) => {
  switch (status) {
    case 'paid':
      return 'mdi-check-decagram-outline'
    case 'shipped':
      return 'mdi-truck-fast-outline'
    case 'delivered':
      return 'mdi-package-variant-closed-check'
    case 'cancelled':
      return 'mdi-close-circle-outline'
    default:
      return 'mdi-swap-horizontal'
  }
}
</script>