<template>
  <div class="d-flex align-start">
    <v-avatar color="primary" size="40" class="mr-4">
      <v-icon>mdi-hand-wave-outline</v-icon>
    </v-avatar>
    <div>
      <p class="font-weight-bold">{{ notification.data.title }}</p>
      <p>{{ notification.data.message }}</p>
      <div class="text-caption text-disabled mt-2">
        {{ dayjs(notification.created_at).fromNow() }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Notification } from '~/schemas/otoapi/notification'
import { useDayjs } from '#dayjs'

const dayjs = useDayjs()

defineProps<{
  notification: Notification & { data: { type: 'user', action: 'verified' } }
}>()
</script>