<template>
  <v-card elevation="0">
    <v-card-text class="pa-4 mt-4">
      <v-row justify="center">
        <v-col cols="12" md="8" lg="6">
          <h2 class="text-h5 mb-6">Change Password</h2>
          
          <v-form ref="passwordForm" @submit.prevent="handleChangePassword">
            <v-text-field
              v-model="formData.current_password"
              label="Current Password"
              :type="showCurrentPassword ? 'text' : 'password'"
              variant="outlined"
              class="mb-4"
              :error-messages="fieldErrors.current_password"
              :append-inner-icon="showCurrentPassword ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append-inner="showCurrentPassword = !showCurrentPassword"
            ></v-text-field>
            
            <v-text-field
              v-model="formData.new_password"
              label="New Password"
              :type="showNewPassword ? 'text' : 'password'"
              variant="outlined"
              class="mb-1"
              :error-messages="fieldErrors.new_password"
              :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append-inner="showNewPassword = !showNewPassword"
              hide-details="auto"
            ></v-text-field>
            <GlobalPasswordRequirements :password="formData.new_password" class="mb-4" />
            
            <v-text-field
              v-model="formData.new_password_confirmation"
              label="Confirm New Password"
              :type="showConfirmPassword ? 'text' : 'password'"
              variant="outlined"
              class="mb-6"
              :error-messages="fieldErrors.new_password_confirmation"
              :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append-inner="showConfirmPassword = !showConfirmPassword"
            ></v-text-field>
            
            <div class="d-flex justify-end ga-3">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="resetForm"
              >
                Cancel
              </v-btn>
              <v-btn
                color="deep-purple"
                type="submit"
                :loading="userStore.loading"
              >
                Change Password
              </v-btn>
            </div>
          </v-form>
        </v-col>
      </v-row>
    </v-card-text>
    
    <!-- Success/Error Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="4000"
      location="top"
    >
      {{ snackbarText }}
    </v-snackbar>
  </v-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { useUserStore } from '~/stores/user'
import { changePasswordSchema, type ChangePassword } from '~/schemas/otoapi/user'
import GlobalPasswordRequirements from '~/components/global/PasswordRequirements.vue'

const userStore = useUserStore()

// Form data
const formData = reactive<ChangePassword>({
  current_password: '',
  new_password: '',
  new_password_confirmation: '',
})
const passwordForm = ref<any>(null)

// Password visibility states
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

// UI state
const showSnackbar = ref(false)
const snackbarText = ref('')
const snackbarColor = ref('success')
const fieldErrors = ref<Partial<Record<keyof ChangePassword, string[]>>>({})

watch([() => formData.new_password, () => formData.new_password_confirmation], () => {
  if (formData.new_password_confirmation && formData.new_password !== formData.new_password_confirmation) {
    fieldErrors.value.new_password_confirmation = ["Passwords don't match"]
  }
  else {
    // Clear the error if it was previously set by this watcher
    if (fieldErrors.value.new_password_confirmation?.[0] === "Passwords don't match") {
      fieldErrors.value.new_password_confirmation = undefined
    }
  }
})


const handleChangePassword = async () => {
  fieldErrors.value = {}
  const result = changePasswordSchema.safeParse(formData);
  if (!result.success) {
    fieldErrors.value = result.error.flatten().fieldErrors;
    return;
  }
  
  try {
    await userStore.changePassword(result.data)
    
    // Success
    snackbarColor.value = 'success'
    snackbarText.value = 'Password changed successfully!'
    showSnackbar.value = true
    
    // Reset form
    resetForm()
    
  } catch (error: any) {
    console.error('Change password error:', error)
    
    let errorData = error.data || error.response?._data || {};

    if (error.statusCode === 422 && errorData.data) {
      // Handle validation errors for specific fields
      if (typeof errorData.data === 'object' && errorData.data !== null) {
        fieldErrors.value = Object.entries(errorData.data).reduce((acc, [key, value]) => {
          acc[key as keyof ChangePassword] = Array.isArray(value) ? value : [String(value)];
          return acc;
        }, {} as Record<keyof ChangePassword, string[]>);
      }
      snackbarText.value = errorData.message || 'Please fix the validation errors below';
    } else if (error.statusCode === 401) {
      snackbarText.value = 'Current password is incorrect';
    } else {
      snackbarText.value = error.message || 'Failed to change password. Please try again.';
    }
    snackbarColor.value = 'error'
    showSnackbar.value = true
  }
}

const resetForm = () => {
  formData.current_password = ''
  formData.new_password = ''
  formData.new_password_confirmation = ''
  fieldErrors.value = {}
  
  // Reset password visibility
  showCurrentPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
  
  if (passwordForm.value) {
    passwordForm.value.resetValidation()
  }
}
</script>