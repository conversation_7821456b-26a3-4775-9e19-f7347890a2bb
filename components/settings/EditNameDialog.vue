<template>
  <v-dialog v-model="dialogVisible" max-width="500px">
    <v-card>
      <v-card-title class="text-h6 bg-deep-purple text-white py-4">
        Edit Full Name
      </v-card-title>
      
      <v-card-text class="pt-4">
        <v-form ref="nameForm" @submit.prevent="updateName">
          <v-text-field
            v-model="editedName"
            label="Full Name"
            variant="outlined"
            :error-messages="nameErrors"
            autofocus
            @input="validateName"
          ></v-text-field>
        </v-form>
      </v-card-text>
      
      <v-card-actions class="pb-4 px-4">
        <v-spacer></v-spacer>
        <v-btn
          color="grey-darken-1"
          variant="text"
          @click="closeDialog"
        >
          Cancel
        </v-btn>
        <v-btn
          color="deep-purple"
          :loading="updating"
          @click="updateName"
        >
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { useUserStore } from '~/stores/user';
import { updateProfileSchema } from '~/schemas/otoapi/user';
import { z } from 'zod/v3';

const props = defineProps({
  modelValue: Boolean,
  initialValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'updated', 'error']);

const authStore = useAuthStore();
const userStore = useUserStore();

const dialogVisible = ref(props.modelValue);
const editedName = ref(props.initialValue);
const updating = ref(false);
const nameErrors = ref<string[]>([]);

// Sync dialog visibility with parent component
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue;
  if (newValue) {
    editedName.value = props.initialValue;
    nameErrors.value = []; // Reset errors when dialog opens
  }
});

watch(() => dialogVisible.value, (newValue) => {
  emit('update:modelValue', newValue);
});

const closeDialog = () => {
  dialogVisible.value = false;
}

const validateName = () => {
  try {
    updateProfileSchema.pick({ name: true }).parse({ name: editedName.value });
    nameErrors.value = [];
    return true;
  } catch (error) {
    if (error instanceof z.ZodError) {
      nameErrors.value = error.errors.map(e => e.message);
    }
    return false;
  }
}

const updateName = async () => {
  if (!validateName()) {
    return;
  }
  
  updating.value = true;
  
  try {
    await userStore.updateProfile({
      name: editedName.value
    });
    
    // The userStore.updateProfile action now handles the auth state update automatically.
    
    // Emit success event
    emit('updated', editedName.value);
    
    // Close the dialog
    closeDialog();
  } catch (error) {
    console.error('Failed to update name:', error);
    
    // Emit error event
    emit('error', 'Failed to update name. Please try again.');
  } finally {
    updating.value = false;
  }
}
</script>
