<template>
  <v-dialog v-model="dialogVisible" max-width="500px">
    <v-card>
      <v-card-title class="text-h6 bg-deep-purple text-white py-4">
        Edit No. Whatsapp
      </v-card-title>
      
      <v-card-text class="pt-4">
        <v-form ref="phoneForm" @submit.prevent="updatePhone">
          <v-text-field
            v-model="editedPhone"
            label="No. Whatsapp"
            variant="outlined"
            :error-messages="phoneErrors"
            autofocus
            @input="validatePhone"
            prepend-inner-icon="mdi-whatsapp"
          ></v-text-field>
        </v-form>
      </v-card-text>
      
      <v-card-actions class="pb-4 px-4">
        <v-spacer></v-spacer>
        <v-btn
          color="grey-darken-1"
          variant="text"
          @click="closeDialog"
        >
          Cancel
        </v-btn>
        <v-btn
          color="deep-purple"
          :loading="updating"
          @click="updatePhone"
        >
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { useUserStore } from '~/stores/user';
import { updateProfileSchema } from '~/schemas/otoapi/user';
import { z } from 'zod/v3';

const props = defineProps({
  modelValue: Boolean,
  initialValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'updated', 'error']);

const authStore = useAuthStore();
const userStore = useUserStore();

const dialogVisible = ref(props.modelValue);
const editedPhone = ref(props.initialValue);
const updating = ref(false);
const phoneErrors = ref<string[]>([]);

// Sync dialog visibility with parent component
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue;
  if (newValue) {
    editedPhone.value = props.initialValue;
    phoneErrors.value = []; // Reset errors when dialog opens
  }
});

watch(() => dialogVisible.value, (newValue) => {
  emit('update:modelValue', newValue);
});

const closeDialog = () => {
  dialogVisible.value = false;
}

const validatePhone = () => {
  try {
    updateProfileSchema.pick({ phone: true }).parse({ phone: editedPhone.value });
    phoneErrors.value = [];
    return true;
  } catch (error) {
    if (error instanceof z.ZodError) {
      phoneErrors.value = error.errors.map(e => e.message);
    }
    return false;
  }
}

const updatePhone = async () => {
  if (!validatePhone()) {
    return;
  }
  
  updating.value = true;
  
  try {
    await userStore.updateProfile({
      phone: editedPhone.value
    });
    
    // The userStore.updateProfile action now handles the auth state update automatically.
    // Emit success event
    emit('updated', editedPhone.value);
    
    // Close the dialog
    closeDialog();
  } catch (error) {
    console.error('Failed to update phone:', error);
    
    // Emit error event
    emit('error', 'Failed to update phone number. Please try again.');
  } finally {
    updating.value = false;
  }
}
</script>
