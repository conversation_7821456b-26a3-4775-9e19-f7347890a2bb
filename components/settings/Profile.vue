<template>
  <v-card elevation="0">
    <v-card-text class="pa-4 mt-4">
      <v-row align="center">
        <v-col v-if="authStore.loading" cols="12" class="text-center">
          <v-progress-circular indeterminate color="deep-purple"></v-progress-circular>
          <div class="mt-2">Loading profile information...</div>
        </v-col>
        <template v-else-if="authStore.user">
          <!-- Profile Photo -->
          <v-col cols="12" md="4" lg="3" class="text-center mb-6 mb-md-0">
            <v-sheet
              color="deep-purple"
              class="square-avatar d-inline-flex align-center justify-center mx-auto"
              rounded="0"
            >
              <span class="user-initials">{{ userInitials }}</span>
            </v-sheet>
          </v-col>
          
          <!-- Profile Information -->
          <v-col cols="12" md="8" lg="9">
            <v-list lines="two" class="pa-0">
              <v-list-item class="py-3">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-account</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">Full Name</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 d-flex align-center">
                  {{ userDetails.name }}
                  <v-btn
                    icon
                    variant="text"
                    density="comfortable"
                    size="small"
                    class="ms-2"
                    @click="openEditNameDialog"
                  >
                    <v-icon color="deep-purple" size="small">mdi-pencil</v-icon>
                  </v-btn>
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-divider></v-divider>
              
              <v-list-item class="py-3">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-at</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">Email</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 d-flex align-center">
                  <span>{{ userDetails.email }}</span>
                  <v-chip
                    v-if="authStore.user?.email_verified"
                    color="success"
                    size="small"
                    class="ms-2"
                    label
                    variant="tonal"
                  >
                    <v-icon start icon="mdi-check-circle"></v-icon>
                    Verified
                  </v-chip>
                  <v-chip
                    v-else
                    color="error"
                    size="small"
                    class="ms-2"
                    label
                    variant="tonal"
                  >
                    <v-icon start icon="mdi-alert-circle"></v-icon>
                    Unverified
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-divider></v-divider>
              
              <v-list-item class="py-3">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-whatsapp</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">No. Whatsapp</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 d-flex align-center">
                  {{ userDetails.phone }}
                  <v-btn
                    icon
                    variant="text"
                    density="comfortable"
                    size="small"
                    class="ms-2"
                    @click="openEditPhoneDialog"
                  >
                    <v-icon color="deep-purple" size="small">mdi-pencil</v-icon>
                  </v-btn>
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-divider></v-divider>
              
              <!-- Added Role Information -->
              <v-list-item class="py-3">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-shield-account</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">Role</v-list-item-title>
                <v-list-item-subtitle class="text-body-1">{{ userDetails.role }}</v-list-item-subtitle>
              </v-list-item>
              
              <v-divider></v-divider>

              <!-- Added Membership Information -->
              <v-list-item class="py-3" v-if="userDetails.membership_level">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-star-circle-outline</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">Membership Level</v-list-item-title>
                <v-list-item-subtitle class="text-body-1">
                  {{ userDetails.membership_level.name }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-divider></v-divider>

              <!-- Added Status Information -->
              <v-list-item class="py-3">
                <template v-slot:prepend>
                  <v-icon color="deep-purple" size="24" class="me-4">mdi-check-decagram</v-icon>
                </template>
                <v-list-item-title class="text-subtitle-1 font-weight-medium mb-1">Status</v-list-item-title>
                <v-list-item-subtitle class="text-body-1 d-flex align-center">
                  <v-chip
                    v-if="userDetails.is_active"
                    color="success"
                    size="small"
                    label
                    variant="tonal"
                  >
                    <v-icon start icon="mdi-check-circle"></v-icon>
                    Activated
                  </v-chip>
                  <v-chip
                    v-else
                    color="error"
                    size="small"
                    label
                    variant="tonal"
                  >
                    <v-icon start icon="mdi-alert-circle"></v-icon>
                    Not Activated
                  </v-chip>
                </v-list-item-subtitle>
              </v-list-item>

              <v-divider></v-divider>
            </v-list>
          </v-col>
        </template>
      </v-row>
    </v-card-text>
    
    <!-- Edit Name Dialog Component -->
    <EditNameDialog
      v-model="editNameDialog"
      :initial-value="userDetails.name"
      @updated="handleNameUpdated"
      @error="handleError"
    />
    
    <!-- Edit Phone Dialog Component -->
    <EditPhoneDialog
      v-model="editPhoneDialog"
      :initial-value="userDetails.phone"
      @updated="handlePhoneUpdated"
      @error="handleError"
    />

    
    <!-- Snackbar for showing success/error messages -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
      location="top"
    >
      {{ snackbarText }}
    </v-snackbar>
  </v-card>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useAuthStore } from '~/stores/auth';
import EditNameDialog from '~/components/settings/EditNameDialog.vue';
import EditPhoneDialog from '~/components/settings/EditPhoneDialog.vue';

const authStore = useAuthStore();
const editNameDialog = ref(false);
const editPhoneDialog = ref(false);
const showSnackbar = ref(false);
const snackbarText = ref('');
const snackbarColor = ref('success');

// Compute user initials from full name
const userInitials = computed(() => {
  const name = authStore.user?.name;
  if (!name) return '?';
  
  return name
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
});

// Get user details from the user store
const userDetails = computed(() => {
  return {
    name: authStore.user?.name || 'Not set',
    email: authStore.user?.email || 'Not set',
    phone: authStore.user?.phone || 'Not set',
    role: authStore.user?.role || 'Not set',
    is_active: authStore.user?.is_active || false,
    membership_level: authStore.user?.membership_level || null,
  };
});


const openEditNameDialog = () => {
  editNameDialog.value = true;
}

const openEditPhoneDialog = () => {
  editPhoneDialog.value = true;
}


const handleNameUpdated = (newName: string) => {
  snackbarColor.value = 'success';
  snackbarText.value = 'Name updated successfully';
  showSnackbar.value = true;
}

const handlePhoneUpdated = (newPhone: string) => {
  snackbarColor.value = 'success';
  snackbarText.value = 'Phone number updated successfully';
  showSnackbar.value = true;
}


const handleError = (errorMessage: any) => {
  snackbarColor.value = 'error';
  snackbarText.value = errorMessage;
  showSnackbar.value = true;
}
</script>

<style scoped>
.square-avatar {
  width: 160px;
  height: 160px;
  max-width: 100%;
}

.user-initials {
  font-size: 3rem;
  font-weight: 500;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .square-avatar {
    width: 140px;
    height: 140px;
  }
  
  .user-initials {
    font-size: 2.5rem;
  }
}

@media (max-width: 400px) {
  .square-avatar {
    width: 120px;
    height: 120px;
  }
  
  .user-initials {
    font-size: 2rem;
  }
}
</style>