<template>
  <div v-if="!isExpired" class="d-flex align-center text-caption text-warning">
    <v-icon size="small" class="mr-1">mdi-clock-alert-outline</v-icon>
    <span>Pay before: {{ countdown }}</span>
  </div>
  <div v-else class="d-flex align-center text-caption text-error">
    <v-icon size="small" class="mr-1">mdi-alert-circle-outline</v-icon>
    <span>Expired</span>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  expiryDate: {
    type: String,
    required: true,
  },
})

const countdown = ref('')
const isExpired = ref(false)
let intervalId: ReturnType<typeof setInterval> | null = null

const calculateCountdown = () => {
  if (!props.expiryDate) {
    countdown.value = ''
    isExpired.value = true
    return
  }

  let normalized = props.expiryDate.replace(' ', 'T');
  const dotIndex = normalized.lastIndexOf('.');
  if (dotIndex !== -1) {
    const zIndex = normalized.lastIndexOf('Z');
    if (zIndex > dotIndex) {
      const fraction = normalized.substring(dotIndex + 1, zIndex);
      if (fraction.length > 3) {
        normalized = normalized.substring(0, dotIndex + 4) + 'Z';
      }
    }
  }
  
  const utcDateString = normalized.endsWith('Z') ? normalized : normalized + 'Z';
  const expiryTime = new Date(utcDateString).getTime();
  const now = new Date().getTime();
  const distance = expiryTime - now;

  if (distance < 0 || isNaN(expiryTime)) {
    countdown.value = 'Expired';
    isExpired.value = true;
    if (intervalId) clearInterval(intervalId);
    return;
  }

  isExpired.value = false;
  const days = Math.floor(distance / (1000 * 60 * 60 * 24));
  const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((distance % (1000 * 60)) / 1000);

  let result = '';
  if (days > 0) result += `${days}d `;
  if (hours > 0 || days > 0) result += `${hours}h `;
  result += `${minutes}m ${seconds}s`;
  
  countdown.value = result.trim();
}

watch(() => props.expiryDate, () => {
  if (intervalId) clearInterval(intervalId);
  calculateCountdown();
  if (!isExpired.value) {
    intervalId = setInterval(calculateCountdown, 1000);
  }
}, { immediate: true });


onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>