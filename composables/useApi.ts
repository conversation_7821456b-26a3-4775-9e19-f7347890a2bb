import { useAuthStore } from '~/stores/auth'

export const useApi = () => {
  const authStore = useAuthStore()
  
  /**
   * Make an authenticated API call
   */
  const callApi = async <T = any>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
      body?: any,
      params?: Record<string, string | number | boolean | undefined | null>
    } = {}
  ): Promise<T> => {
    const { method = 'GET', body, params } = options
    
    // Build URL with query parameters
    let url = endpoint
    if (params && method === 'GET') {
      const queryParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
      const queryString = queryParams.toString()
      if (queryString) {
        url += `?${queryString}`
      }
    }
    
    // Prepare headers
    const headers: Record<string, string> = {}
    
    // Add Authorization header if we have a token
    if (authStore.token) {
      headers['Authorization'] = `Bearer ${authStore.token}`
    }
    
    // Add CSRF token for state-changing methods
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method) && authStore.csrfToken) {
      headers['X-CSRF-Token'] = authStore.csrfToken
    }
    
    try {
      // Make the API call
      const response = await $fetch<T>(url, {
        method,
        body: body || undefined,
        headers,
        credentials: 'include'
      })
      
      return response
    } catch (error: any) {
      // Handle authentication errors
      if (error.statusCode === 401) {
        authStore.logout()
      }
      
      throw error
    }
  }
  
  return {
    callApi,
    get: <T = any>(endpoint: string, params?: Record<string, any>) =>
      callApi<T>(endpoint, { method: 'GET', params }),
    post: <T = any>(endpoint: string, body?: any) =>
      callApi<T>(endpoint, { method: 'POST', body }),
    put: <T = any>(endpoint: string, body?: any) =>
      callApi<T>(endpoint, { method: 'PUT', body }),
    delete: <T = any>(endpoint: string, body?: any) =>
      callApi<T>(endpoint, { method: 'DELETE', body }),
    patch: <T = any>(endpoint: string, body?: any) =>
      callApi<T>(endpoint, { method: 'PATCH', body }),
    batchDelete: <T = any>(endpoint: string, body?: any) =>
      callApi<T>(endpoint, { method: 'DELETE', body: body })
  }
}
