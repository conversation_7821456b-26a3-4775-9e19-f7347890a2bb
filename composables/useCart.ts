import type { AddToCartPayload, UpdateCartPayload, CartProduct, cartItemSchema } from '~/schemas/otoapi/cart'
import type { SingleItemResponse, SuccessResponse } from '~/schemas/api-response'
import { useApi } from './useApi'
import { useAuthStore } from '~/stores/auth'

export const useCart = () => {
  const { get, post, put, delete: del } = useApi()
  const authStore = useAuthStore()

  const getCartItems = async (): Promise<CartProduct[]> => {
    if (!authStore.user?.id) {
      console.warn('getCartItems called without authenticated user.')
      return []
    }
    // The backend expects the user_id as a query parameter.
    // The API returns an object like { data: [...] }, so we type the response and extract .data
    const response = await get<{ data: CartProduct[] }>('/api/cart', { user_id: authStore.user.id })
    return response.data || []
  }

  const addToCart = async (product: AddToCartPayload): Promise<SingleItemResponse<typeof cartItemSchema>> => {
    return await post<SingleItemResponse<typeof cartItemSchema>>('/api/cart', product)
  }

  const updateCartItem = async (cartId: number, payload: UpdateCartPayload): Promise<SingleItemResponse<typeof cartItemSchema>> => {
    return await put<SingleItemResponse<typeof cartItemSchema>>(`/api/cart/${cartId}`, payload)
  }

  const removeFromCart = async (cartId: number): Promise<SuccessResponse> => {
    return await del<SuccessResponse>(`/api/cart/${cartId}`)
  }

  const removeMultipleFromCart = async (cartIds: number[]): Promise<SuccessResponse> => {
    return await del<SuccessResponse>('/api/cart/multiple', { ids: cartIds })
  }

  const clearCart = async (): Promise<SuccessResponse> => {
    return await del<SuccessResponse>('/api/cart/clear')
  }

  return {
    getCartItems,
    addToCart,
    updateCartItem,
    removeFromCart,
    removeMultipleFromCart,
    clearCart
  }
}
