export const useCartAnimation = () => {
  const getCartIconPosition = (): { x: number; y: number } | null => {
    if (!import.meta.client) return null
    
    const cartIcon = document.getElementById('header-cart-icon')
    if (!cartIcon) return null
    
    const rect = cartIcon.getBoundingClientRect()
    return {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    }
  }
  
  return {
    getCartIconPosition
  }
}