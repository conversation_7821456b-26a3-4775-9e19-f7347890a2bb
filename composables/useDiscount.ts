import { computed, type Ref } from 'vue';
import { useDiscountCalculator } from '~/composables/useDiscountCalculator';

export function useDiscount(
  productMaxDiscount?: Ref<number | string | null | undefined>
) {
  const { calculateDiscountPercentage, calculateDiscountedPrice: calcPrice } = useDiscountCalculator();

  const discountPercentage = computed(() => {
    return calculateDiscountPercentage(productMaxDiscount?.value);
  });

  const calculateDiscountedPrice = (price: number | string) => {
    return calcPrice(price, discountPercentage.value);
  };

  return {
    discountPercentage,
    calculateDiscountedPrice,
  };
}