import { useAuthStore } from '~/stores/auth';

export function useDiscountCalculator() {
  const authStore = useAuthStore();

  const calculateDiscountPercentage = (productMaxDiscount?: number | string | null) => {
    const memberDiscount = authStore.user?.membership_level?.discount_percentage || 0;

    if (productMaxDiscount === null || productMaxDiscount === undefined) {
      return memberDiscount;
    }

    const maxDiscount = typeof productMaxDiscount === 'string'
      ? parseFloat(productMaxDiscount)
      : productMaxDiscount;

    if (isNaN(maxDiscount)) {
      return memberDiscount;
    }

    return Math.min(memberDiscount, maxDiscount);
  };

  const calculateDiscountedPrice = (price: number | string, discountPercentage: number) => {
    if (!price) return 0;
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numericPrice) || discountPercentage <= 0) {
      return numericPrice;
    }
    const discount = numericPrice * (discountPercentage / 100);
    return numericPrice - discount;
  };

  return {
    calculateDiscountPercentage,
    calculateDiscountedPrice,
  };
}