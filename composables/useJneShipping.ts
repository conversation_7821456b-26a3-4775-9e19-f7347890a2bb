import { ref } from 'vue'
import { useJneOriginStore } from '~/stores/jne-origin'
import { useJneDestinationStore } from '~/stores/jne-destination'
import { useJneStore } from '~/stores/jne'
import { useOngkirStore } from '~/stores/ongkir'
import type { JnePriceDetail } from '~/schemas/jne'
import type { ShippingAddress } from '~/schemas/otoapi/shipping-address'
import type { JneOrigin, JneOriginFilter } from '~/schemas/otoapi/jne-origin'
import type { JneDestination, JneDestinationFilter } from '~/schemas/otoapi/jne-destination'

export const useJneShipping = () => {
  const jneOriginStore = useJneOriginStore()
  const jneDestinationStore = useJneDestinationStore()
  const jneStore = useJneStore()
  const ongkirStore = useOngkirStore()

  const origins = ref<JneOrigin[]>([])
  const destinations = ref<JneDestination[]>([])
  const prices = ref<any[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Normalize city names by removing KAB. or KOTA prefix
  const normalizeCity = (city: string = '') => {
    return city.replace(/^(KAB\.|KOTA\s)/i, '').trim()
  }


  const transformJnePrices = (prices: JnePriceDetail[]) => {
    return prices.map(price => {
      const etd = (price.etd_from === price.etd_thru)
        ? price.etd_from
        : `${price.etd_from}-${price.etd_thru}`

      return {
        id: price.service_code,
        name: `JNE - ${price.service_display}`,
        logo: ongkirStore.jneLogo,
        description: `Estimasi pengiriman: ${etd} hari`,
        price: price.price,
        etd: etd,
        courier: 'JNE',
        service: price.service_code,
        service_display: price.service_display,
      }
    })
  }

  const fetchOrigins = async () => {
    loading.value = true
    error.value = null
    try {
      const params: JneOriginFilter = {
        page: 1,
        per_page: 10,
        is_default: true,
      }
      const response = await jneOriginStore.fetchJneOrigins(params);
      if (response && response.data) {
        origins.value = response.data
      }
    } catch (err) {
      error.value = 'Failed to fetch JNE origins'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const fetchDestinations = async (address: ShippingAddress | null) => {
    if (!address) {
      destinations.value = []
      return
    }

    loading.value = true
    error.value = null
    try {
      const filterParams: Partial<JneDestinationFilter>[] = [
        {
          province_name: address.shipping_province,
          city_name: normalizeCity(address.shipping_city),
          district_name: address.shipping_district,
          subdistrict_name: address.shipping_subdistrict,
          zip_code: address.shipping_post_code || undefined,
        },
        {
          province_name: address.shipping_province,
          city_name: normalizeCity(address.shipping_city),
          district_name: address.shipping_district,
          subdistrict_name: address.shipping_subdistrict,
        },
        {
          province_name: address.shipping_province,
          city_name: normalizeCity(address.shipping_city),
          district_name: address.shipping_district,
        },
        {
          province_name: address.shipping_province,
          city_name: normalizeCity(address.shipping_city),
        },
        {
          province_name: address.shipping_province,
        },
      ]

      for (const params of filterParams) {
        const response = await jneDestinationStore.fetchJneDestinations({
          ...params,
          page: 1,
          per_page: 10,
        } as JneDestinationFilter)

        if (response && response.data && response.data.length > 0) {
          destinations.value = response.data
          // console.log('destinations', destinations.value)
          return
        }
      }

      // If no results found after all filters, set destinations to empty
      destinations.value = []
      error.value = 'Address not registered'

    } catch (err) {
      error.value = 'Failed to fetch JNE destinations'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const getPrices = async (origin: string, destination: string, weight: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await jneStore.getPrices({
        from: origin,
        thru: destination,
        weight: weight,
      })
      if (response && response.price) {
        prices.value = response.price
      }
    } catch (err) {
      error.value = 'Failed to get JNE prices'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  return {
    origins,
    destinations,
    prices,
    loading,
    error,
    normalizeCity,
    fetchOrigins,
    fetchDestinations,
    getPrices,
    transformJnePrices,
  }
}