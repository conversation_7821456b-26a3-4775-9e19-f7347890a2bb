import { useAuthStore } from '~/stores/auth'
import { ref } from 'vue'

export const useJubelio = () => {
  const authStore = useAuthStore()
  
  // Create refs for error handling
  const showSnackbar = ref(false)
  const snackbarMessage = ref('')
  
  /**
   * Function to make API calls to Jubelio through our proxy
   * @param endpoint - API endpoint path
   * @param method - HTTP method (GET, POST, PUT, DELETE, PATCH)
   * @param body - Request body for POST, PUT, PATCH methods
   * @param params - Query parameters for GET requests
   * @returns Promise with the API response
   */
  const callJubelioApi = async <T = any>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
    body?: any,
    params?: Record<string, string | number | boolean | undefined | null>
  ): Promise<T> => {
    try {
      // Check if authenticated
      if (!authStore.isAuthenticated || !authStore.token) {
        throw new Error('Not authenticated')
      }
      
      // Ensure endpoint has a trailing slash if it's a base path
      // This is important for some APIs like Jubelio
      if (endpoint.split('/').length === 2 && !endpoint.endsWith('/')) {
        endpoint = `${endpoint}/`;
      }
      
      // Prepare URL with query parameters (only for GET requests)
      let url = `/api/jubelio${endpoint}`
      if (method === 'GET' && params) {
        const queryParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value))
          }
        })
        const queryString = queryParams.toString()
        if (queryString) {
          url += `?${queryString}`
        }
      }
      
      // Prepare request options
      const options: any = {
        method,
        headers: {}
      }
      
      // Add auth token
      if (authStore.token) {
        options.headers['Authorization'] = `Bearer ${authStore.token}`
      }
      
      // Add CSRF token for non-GET requests
      if (method !== 'GET' && authStore.csrfToken) {
        options.headers['X-CSRF-Token'] = authStore.csrfToken
      }
      
      // Add body for non-GET methods
      if (method !== 'GET' && body !== undefined) {
        options.body = JSON.stringify(body)
        options.headers['Content-Type'] = 'application/json'
      }
      
      // Make the API call through our proxy
      const response = await $fetch<T>(url, options)
      
      return response
    } catch (error: any) {
      // If token expired, show message instead of logging out
      if (error.statusCode === 401) {
        console.error('Jubelio API authentication error:', error)
        
        // Show error message using snackbar
        snackbarMessage.value = 'Session expired. Please refresh the page or try again.'
        showSnackbar.value = true
        
        // Hide the message after 3 seconds
        setTimeout(() => {
          showSnackbar.value = false
        }, 3000)
      }
      
      throw error
    }
  }

  /**
   * Shorthand for GET requests
   */
  const get = async <T = any>(endpoint: string, params?: Record<string, string | number | boolean | undefined | null>): Promise<T> => {
    return callJubelioApi<T>(endpoint, 'GET', undefined, params)
  }

  /**
   * Shorthand for POST requests
   */
  const post = async <T = any>(endpoint: string, body?: any): Promise<T> => {
    return callJubelioApi<T>(endpoint, 'POST', body)
  }

  /**
   * Shorthand for DELETE requests
   */
  const remove = async <T = any>(endpoint: string, body?: any): Promise<T> => {
    return callJubelioApi<T>(endpoint, 'DELETE', body)
  }

  /**
   * Shorthand for PUT requests
   */
  const put = async <T = any>(endpoint: string, body?: any): Promise<T> => {
    return callJubelioApi<T>(endpoint, 'PUT', body)
  }

  /**
   * Shorthand for PATCH requests
   */
  const patch = async <T = any>(endpoint: string, body?: any): Promise<T> => {
    return callJubelioApi<T>(endpoint, 'PATCH', body)
  }

  /**
   * Manually refresh the Jubelio token
   */
  const refreshToken = async (): Promise<boolean> => {
    try {
      await $fetch('/api/jubelio/refresh-token', {
        method: 'POST'
      })
      return true
    } catch (error) {
      console.error('Failed to refresh Jubelio token:', error)
      return false
    }
  }

  return {
    callJubelioApi,
    get,
    post,
    delete: remove,
    put,
    patch,
    refreshToken,
    // Expose snackbar state for use in components
    showSnackbar,
    snackbarMessage
  }
}
