export const useNotificationPolling = (intervalMinutes: number = 2) => {
  const notificationStore = useNotificationAppStore()
  const intervalId = ref<NodeJS.Timeout | null>(null)
  const isPolling = ref(false)

  const startPolling = () => {
    if (isPolling.value) return
    
    isPolling.value = true
    intervalId.value = setInterval(async () => {
      try {
        // Only fetch unread count to avoid disrupting user's current view
        await notificationStore.fetchUnreadCount()
      } catch (error) {
        console.error('Failed to poll notifications:', error)
      }
    }, intervalMinutes * 60 * 1000) // Convert minutes to milliseconds
  }

  const stopPolling = () => {
    if (intervalId.value) {
      clearInterval(intervalId.value)
      intervalId.value = null
    }
    isPolling.value = false
  }

  // Auto cleanup on unmount
  onUnmounted(() => {
    stopPolling()
  })

  return {
    startPolling,
    stopPolling,
    isPolling
  }
}
