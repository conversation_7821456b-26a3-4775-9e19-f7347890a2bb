import type { Notification } from '~/schemas/otoapi/notification'

export const useNotificationUtils = () => {
  const getNotificationIcon = (notification: Notification): string => {
    const { type, action } = notification.data
    if (type === 'order') {
      if (action === 'created') return 'mdi-plus-circle-outline'
      if (action === 'status_updated') return 'mdi-swap-horizontal'
    }
    if (type === 'user') {
      if (action === 'created') return 'mdi-account-plus-outline'
      if (action === 'verified') return 'mdi-hand-wave-outline'
    }
    return 'mdi-bell'
  }

  const getNotificationColor = (notification: Notification): string => {
    const { type, action } = notification.data
    if (type === 'order') {
      if (action === 'created') return 'success'
      if (action === 'status_updated') return 'blue'
    }
    if (type === 'user') {
      if (action === 'created') return 'info'
      if (action === 'verified') return 'primary'
    }
    return 'grey'
  }

  const getNotificationTitle = (notification: Notification): string => {
    return notification.data.title
  }

  const getNotificationMessage = (notification: Notification): string => {
    const { data } = notification
    switch (data.type) {
      case 'order':
        if (data.action === 'created') {
          return `Order <strong>#${data.order.jubelio_order_id}</strong> by <strong>${data.created_by.name}</strong>`
        }
        if (data.action === 'status_updated') {
          return `Order <strong>#${data.order.jubelio_order_id}</strong> status: <strong>${data.order.new_status}</strong>`
        }
        break
      case 'user':
        if (data.action === 'created') {
          return `New user <strong>${data.user.name}</strong> was created`
        }
        if (data.action === 'verified') {
          return data.message
        }
        break
    }
    return 'You have a new notification.'
  }

  const getNotificationLink = (notification: Notification): string | null => {
    const { type, ...data } = notification.data

    if (type === 'user' && 'user' in data && data.user) {
      return `/users/${data.user.id}`
    }

    if (type === 'order' && 'order' in data && data.order) {
      return `/transactions/${data.order.id}`
    }

    return null
  }

  return {
    getNotificationIcon,
    getNotificationColor,
    getNotificationTitle,
    getNotificationMessage,
    getNotificationLink,
  }
}