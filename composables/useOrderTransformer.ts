import { useDayjs } from '#dayjs';
import { useDiscountCalculator } from '~/composables/useDiscountCalculator';
import type { CartProduct } from '~/schemas/otoapi/cart'
import type { CreateOrderRequest, OrderItem } from '~/types/checkout'
import type { ShippingAddress } from '~/schemas/otoapi/shipping-address'
import type { CreateOtoApiOrderRequest, OtoApiOrderItem } from '~/schemas/transaction/create'
import type { ShippingType } from '~/schemas/transaction/create'
import type { Expedition } from '~/schemas/transaction/order'

export const useOrderTransformer = () => {
  const dayjs = useDayjs()

  // Transform cart items to Jubelio order items
  const transformToJubelioItems = (
    cartItems: any[],
    locationId: number,
    productDiscountStore: any,
    membershipLevel: any
  ): OrderItem[] => {
    const { calculateDiscountPercentage } = useDiscountCalculator();

    return cartItems.map(item => {
      const originalAmount = item.price * item.quantity;

      const productDiscount = productDiscountStore.discounts.get(item.jubelio_item_group_id);
      const maxProductDiscount = calculateDiscountPercentage(productDiscount?.max_discount_percentage);
      const membershipDiscount = membershipLevel?.discount_percentage || 0;
      const finalDiscountPercentage = Math.min(maxProductDiscount, membershipDiscount);
      const discountAmount = (finalDiscountPercentage / 100) * originalAmount;

      return {
        salesorder_detail_id: 0,
        item_id: Number(item.jubelio_item_id),
        serial_no: item.sku || '',
        description: item.name,
        tax_id: item.tax_id || 1,
        price: item.price,
        unit: item.unit || 'buah',
        qty_in_base: item.quantity,
        disc: finalDiscountPercentage,
        disc_amount: discountAmount,
        tax_amount: (item.tax_rate_percent || 0) / 100 * (originalAmount - discountAmount),
        amount: originalAmount - discountAmount,
        location_id: locationId,
        shipper: '', // Will be set separately
        tracking_no: ''
      };
    });
  }

  // Transform cart items to OtoAPI order items
  const transformToOtoApiItems = (cartItems: CartProduct[]): OtoApiOrderItem[] => {
    return cartItems.map((item): OtoApiOrderItem => ({
      jubelio_item_id: Number(item.jubelio_item_id),
      jubelio_item_group_id: Number(item.jubelio_item_group_id),
      sku: item.sku || '',
      name: item.name,
      unit: item.unit || 'buah',
      original_price: Number(item.price), // this was correct, the error was a red herring
      quantity: Number(item.quantity),
      weight: Number(item.weight),
      variant: item.variant || null,
      image: item.image || null,
      tax_id: Number(item.tax_id) || 1,
      tax_rate_percent: Number(item.tax_rate_percent) || 0
    }))
  }

  // Transform shipping method to expedition data
  const transformToExpedition = (shippingMethod: any): Expedition => {
    const [courierCode, serviceType] = shippingMethod.id?.split('-') || ['', '']
    const etdMatch = shippingMethod.description.match(/(\d+-?\d*)\s*days?/)
    
    return {
      name: shippingMethod.name,
      code: courierCode.toUpperCase(),
      service: serviceType,
      description: shippingMethod.description,
      cost: shippingMethod.price,
      etd: etdMatch?.[1] || '2-3'
    }
  }

  // Create Jubelio order request
  const createJubelioOrder = (params: {
    contact: any // Full contact data from contactStore
    user: any
    items: OrderItem[]
    shippingAddress: ShippingAddress // Selected shipping address
    shippingMethod: any
    shippingType: ShippingType
    trackingNumber?: string
    paymentMethod: any
    totals: {
      subtotal: number
      discountedSubtotal: number
      shippingFee: number
      insuranceFee: number
      total: number
    }
    locationId: number
    storeId: string
    note?: string
  }): CreateOrderRequest => {
    const expedition = params.shippingType === 'expedition'
      ? transformToExpedition(params.shippingMethod)
      : params.shippingMethod;

    if (expedition.code === 'JNT') {
      expedition.code = 'J&T'
    }

    const itemsWithTracking = params.items.map(item => {
      let shipper = '';
      if (params.shippingType === 'expedition') {
        shipper = `${expedition.code} ${expedition.service}`;
      } else if (params.shippingType === 'online_recipient') {
        shipper = expedition.code;
      } else if (params.shippingType === 'store_pickup') {
        shipper = 'In-Store Pickup'; // The required value for Jubelio
      }

      return {
        ...item,
        shipper: shipper,
        tracking_no: params.shippingType === 'online_recipient' ? params.trackingNumber || '' : ''
      };
    });
    
    return {
      salesorder_id: 0,
      salesorder_no: '[auto]',
      contact_id: Number(params.contact.contact_id), // Reseller/customer ID(from data jubelio)
      customer_name: params.contact.contact_name || '', // Reseller/customer name(from data jubelio)
      transaction_date: dayjs().format(),
      is_tax_included: false,
      note: params.note || '',
      sub_total: params.totals.subtotal,
      total_disc: params.items.reduce((sum, item) => sum + item.disc_amount, 0),
      total_tax: params.items.reduce((sum, item) => sum + item.tax_amount, 0),
      grand_total: params.totals.total,
      location_id: params.locationId,
      source: 1,
      channel_status: 'PENDING',
      shipping_cost: params.totals.shippingFee,
      insurance_cost: params.totals.insuranceFee,
      is_paid: false,
      items: itemsWithTracking,
      // Shipping info - this is the delivery recipient (can be different from customer)
      shipping_full_name: params.shippingAddress.shipping_full_name, // Recipient name
      shipping_phone: params.shippingAddress.shipping_phone, // Recipient phone
      shipping_address: params.shippingAddress?.shipping_address || '',
      shipping_country: 'Indonesia',
      shipping_province_id: params.shippingAddress?.shipping_province_id?.toString() || '',
      shipping_province: params.shippingAddress?.shipping_province || '',
      shipping_city_id: params.shippingAddress?.shipping_city_id?.toString() || '',
      shipping_city: params.shippingAddress?.shipping_city || '',
      shipping_district_id: params.shippingAddress?.shipping_district_id?.toString() || '',
      shipping_area: params.shippingAddress?.shipping_district || '', // district name area on request ot jubelio
      shipping_subdistrict_id: params.shippingAddress?.shipping_subdistrict_id?.toString() || '',
      shipping_subdistrict: params.shippingAddress?.shipping_subdistrict || '',
      shipping_post_code: params.shippingAddress?.shipping_post_code || '',
      payment_method: params.paymentMethod.method,
      add_fee: 0,
      add_disc: 0,
      service_fee: 0,
      store_id: params.storeId
    }
  }

  // Create OtoAPI order request
  const createOtoApiOrder = (params: {
    contact: any
    user: any
    items: CreateOtoApiOrderRequest['items']
    shippingAddress: ShippingAddress
    shippingMethod: any
    shippingType: ShippingType
    trackingNumber?: string
    paymentMethod: any
    totals: {
      subtotal: number
      discountedSubtotal: number
      shippingFee: number
      insuranceFee: number
      total: number
    }
  }): CreateOtoApiOrderRequest => {
    const expedition = params.shippingType === 'expedition'
      ? transformToExpedition(params.shippingMethod)
      : params.shippingMethod;

    if (expedition.code === 'JNT') {
      expedition.code = 'J&T'
    }

    return {
      jubelio_order_id: 0, // This will be set later
      user_id: Number(params.user?.id || 0),
      shipping_address_id: params.shippingAddress.id,
      user_name: params.user?.name || '', // from data otoapi
      user_phone: params.user?.phone || '', // from data otoapi
      shipping_full_name: params.shippingAddress.shipping_full_name,
      shipping_phone: params.shippingAddress.shipping_phone,
      shipping_address: params.shippingAddress.shipping_address,
      shipping_province_id: params.shippingAddress.shipping_province_id,
      shipping_province: params.shippingAddress.shipping_province,
      shipping_city_id: params.shippingAddress.shipping_city_id,
      shipping_city: params.shippingAddress.shipping_city,
      shipping_district_id: params.shippingAddress.shipping_district_id,
      shipping_district: params.shippingAddress.shipping_district,
      shipping_subdistrict_id: params.shippingAddress.shipping_subdistrict_id,
      shipping_subdistrict: params.shippingAddress.shipping_subdistrict,
      shipping_post_code: params.shippingAddress.shipping_post_code || '',
      status: 'pending',
      shipping_type: params.shippingType,
      tracking_number: params.trackingNumber || null,
      sub_total: params.totals.subtotal,
      shipping_cost: params.totals.shippingFee,
      insurance_cost: params.totals.insuranceFee,
      grand_total: params.totals.total,
      payment_method: params.paymentMethod.method,
      expedition: expedition,
      etc: null,
      items: params.items
    }
  }

  return {
    transformToJubelioItems,
    transformToOtoApiItems,
    transformToExpedition,
    createJubelioOrder,
    createOtoApiOrder
  }
}
