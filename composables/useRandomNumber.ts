/**
 * Generates a random number with a specified number of digits
 * @param digits The number of digits for the random number (between 4 and 9)
 * @returns A string representation of the random number with proper padding
 */
export const useRandomNumber = (digits: number = 5): string => {
  // Validate input
  if (digits < 4 || digits > 9) {
    console.warn('Number of digits should be between 4 and 9. Defaulting to 5.');
    digits = 5;
  }
  
  // Calculate the range for the random number
  const min = digits > 1 ? Math.pow(10, digits - 1) : 0; // Minimum value (e.g., 1000 for 4 digits)
  const max = Math.pow(10, digits) - 1; // Maximum value (e.g., 9999 for 4 digits)
  
  // Generate a random number within the range
  const randomNumber = Math.floor(min + Math.random() * (max - min + 1));
  
  // Convert to string and pad with leading zeros if needed
  return randomNumber.toString().padStart(digits, '0');
}
