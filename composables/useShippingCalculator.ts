import type { RajaOngkirDestination, RajaOngkirShippingCost } from '~/schemas/raja-ongkir'
import { logger } from '~/utils/logger';

export const useShippingCalculator = () => {
  const rajaOngkir = useRajaOngkirStore()
  const ongkirStore = useOngkirStore()

  // Normalize city names by removing KAB. or KOTA prefix
  const normalizeCity = (city: string = '') => {
    return city.replace(/^(KAB\.|KOTA\s)/i, '').trim()
  }

  // Find best matching location with progressive fallback
  const findBestMatch = (results: RajaOngkirDestination[], locationData: any) => {
    if (!results || results.length === 0) return null

    // Try exact match first
    let match = results.find(item => {
      const itemCity = normalizeCity(item.city_name)
      return (
        item.subdistrict_name.toLowerCase() === locationData.subdistrictName.toLowerCase() &&
        item.district_name.toLowerCase() === locationData.districtName.toLowerCase() &&
        itemCity.toLowerCase() === locationData.cityName.toLowerCase() &&
        item.province_name.toLowerCase() === locationData.provinceName.toLowerCase()
      )
    })

    // If no exact match, try with district and city
    if (!match) {
      match = results.find(item => {
        const itemCity = normalizeCity(item.city_name)
        return (
          item.district_name.toLowerCase() === locationData.districtName.toLowerCase() &&
          itemCity.toLowerCase() === locationData.cityName.toLowerCase() &&
          item.province_name.toLowerCase() === locationData.provinceName.toLowerCase()
        )
      })
    }

    // If still no match, try with just city and province
    if (!match) {
      match = results.find(item => {
        const itemCity = normalizeCity(item.city_name)
        return (
          itemCity.toLowerCase() === locationData.cityName.toLowerCase() &&
          item.province_name.toLowerCase() === locationData.provinceName.toLowerCase()
        )
      })
    }

    // Last resort: return first result
    return match || results[0]
  }

  // Transform shipping costs to shipping methods
  const transformShippingMethods = (shippingCosts: RajaOngkirShippingCost[]) => {
    return shippingCosts.map(option => {
      let logo = ''
      let hasInsurance = false

      switch (option.code.toLowerCase()) {
        case 'jne':
          logo = ongkirStore.jneLogo
          break
        case 'jnt':
        case 'j&t':
          logo = ongkirStore.jntLogo
          break
        case 'pos':
          logo = ongkirStore.posLogo
          hasInsurance = true
          break
        case 'ide':
          logo = ongkirStore.ideLogo
          break
        default:
          logo = ongkirStore.defaultLogo
      }

      return {
        id: `${option.code}-${option.service}`,
        name: `${option.name} - ${option.service}`,
        logo: logo,
        description: `${option.description} • Est. delivery: ${option.etd || '2-3'} days${hasInsurance ? ' • Includes Rp 500 insurance' : ''}`,
        price: option.cost,
        hasInsurance: hasInsurance,
        courier: option.code,
        service: option.service
      }
    })
  }

  // Calculate shipping options
  const calculateShippingOptions = async (
    originLocation: any,
    destinationLocation: any,
    totalWeight: number
  ) => {
    if (!originLocation || !destinationLocation) {
      throw new Error('Missing origin or destination location data')
    }

    // Use at least 1000g (1kg) or the calculated weight
    const weight = Math.max(1000, totalWeight)

    // Normalize location data
    const originData = {
      subdistrictName: originLocation.subdistrictName || '',
      districtName: originLocation.districtName || '',
      cityName: normalizeCity(originLocation.cityName || ''),
      provinceName: originLocation.provinceName || ''
    }

    const destinationData = {
      subdistrictName: destinationLocation.subdistrictName || '',
      districtName: destinationLocation.districtName || '',
      cityName: normalizeCity(destinationLocation.cityName || ''),
      provinceName: destinationLocation.provinceName || ''
    }

    // Get search queries
    const originSearchQuery = `${originData.subdistrictName}, ${originData.districtName}`
    const destinationSearchQuery = `${destinationData.subdistrictName}, ${destinationData.districtName}`

    logger.info('Search queries:', { origin: originSearchQuery, destination: destinationSearchQuery })

    // Search for both locations in parallel
    const [originResults, destinationResults] = await Promise.all([
      rajaOngkir.searchDomesticDestinations({
        search: originSearchQuery,
        limit: 10
      }),
      rajaOngkir.searchDomesticDestinations({
        search: destinationSearchQuery,
        limit: 10
      })
    ])

    const originMatch = findBestMatch(originResults.data, originData)
    const destinationMatch = findBestMatch(destinationResults.data, destinationData)

    if (!originMatch || !destinationMatch) {
      throw new Error('Could not find matching locations for shipping calculation')
    }

    logger.info('Matched locations:', {
      origin: `${originMatch.subdistrict_name}, ${originMatch.district_name}, ${originMatch.city_name}, ${originMatch.province_name}`,
      destination: `${destinationMatch.subdistrict_name}, ${destinationMatch.district_name}, ${destinationMatch.city_name}, ${destinationMatch.province_name}`
    })

    // Calculate shipping using Raja Ongkir
    const shippingResult = await rajaOngkir.calculateDomesticShipping({
      origin: originMatch.id.toString(),
      destination: destinationMatch.id.toString(),
      weight: weight,
      courier: 'jne:jnt:pos:ide'
    })

    if (!shippingResult.data || shippingResult.data.length === 0) {
      throw new Error('No shipping options available for your location')
    }

    return transformShippingMethods(shippingResult.data)
  }

  return {
    calculateShippingOptions,
    transformShippingMethods,
    normalizeCity,
    findBestMatch
  }
}
