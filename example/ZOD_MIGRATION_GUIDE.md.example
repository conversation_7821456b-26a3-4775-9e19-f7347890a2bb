# Zod Migration & Best Practices Guide

This document provides a comprehensive guide for migrating from a manual `types` directory to using Zod for schema definition, validation, and type inference in this Nuxt 3 application.

## 1. Core Principles

- **Single Source of Truth**: Zod schemas are the ultimate authority for the shape of your data. We will eliminate the `/types` directory and infer all TypeScript types directly from schemas. This guarantees that validation rules and types never go out of sync.
- **Validate at the Boundaries**: We will validate data whenever it enters or leaves a system boundary. This includes:
    - Incoming API requests on the server.
    - Responses from external APIs (like Jubelio).
    - User input from frontend forms.

---

## 2. Directory Structure

To keep schemas organized and maintainable, we will use the following structure within the `/schemas` directory:

```
/schemas
├── otoapi/         # Schemas for your own backend (OtoApi) data models
│   ├── user.ts
│   ├── product.ts
│   └── ...
├── jubelio/        # Schemas for data from the external Jubelio API
│   ├── product.ts
│   └── order.ts
├── frontend/       # Schemas used ONLY for frontend forms (e.g., login)
│   └── loginForm.ts
└── apiResponse.ts  # Reusable schemas for handling API response envelopes
```

- **`otoapi/`**: Defines the contract for your internal data. This is the single source of truth for models like `User`, `Product`, etc.
- **`jubelio/`**: Defines the shape of data you consume from the Jubelio API.
- **`frontend/`**: For UI-specific data shapes that don't map directly to a backend model (e.g., a login form).

---

## 3. Implementation Steps & Patterns

### Step 1: Define Base Schemas and Infer Types

For each data model, create a schema file. Use `z.infer` to generate the TypeScript type.

**Example**: `schemas/otoapi/user.ts`

```typescript
import { z } from 'zod';

// Define the schema for the full User object
export const userSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(2),
  email: z.string().email(),
  role: z.enum(['ADMIN', 'RESELLER']),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Infer the TypeScript type directly from the schema
export type User = z.infer<typeof userSchema>;
```
*You can now delete `types/user.ts` and import `User` from this file instead.*

### Step 2: Handle Create/Update Variations with "Base & Extend"

To avoid repetition, define a base schema with common fields and extend it for specific operations.

**Example**: `schemas/otoapi/user.ts` (continued)

```typescript
import { z } from 'zod';

// 1. Define a BASE schema with fields common to create and update
const userBaseSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number seems too short"),
});

// 2. Extend the base for CREATING a user (e.g., add password)
export const createUserSchema = userBaseSchema.extend({
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// 3. Extend the base for the FULL user object (from GET requests)
export const userSchema = userBaseSchema.extend({
  id: z.string().uuid(),
  role: z.enum(['ADMIN', 'RESELLER']),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// 4. Use .partial() for UPDATING a user (makes all fields optional)
export const updateUserSchema = userBaseSchema.partial();

// 5. Infer all necessary types
export type CreateUser = z.infer<typeof createUserSchema>;
export type User = z.infer<typeof userSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;
```

### Step 3: Validate on the Server

In your Nuxt server routes, validate all incoming data and external API responses.

**Example**: `/server/api/users/index.post.ts`

```typescript
import { createUserSchema } from '~/schemas/otoapi/user';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  // .parse() throws an error if validation fails, which Nuxt will handle
  const validatedData = createUserSchema.parse(body);

  // Proceed with creating the user using the clean, validatedData
  // ...
  return { success: true, user: validatedData };
});
```

### Step 4: Validate on the Frontend

Use the exact same schemas for frontend form validation to provide instant user feedback.

**Example**: A create user form with `vee-validate`.

```vue
<script setup lang="ts">
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import { createUserSchema } from '~/schemas/otoapi/user'; // Re-using the schema

const { handleSubmit } = useForm({
  validationSchema: toTypedSchema(createUserSchema),
});

const onSubmit = handleSubmit(async (values) => {
  // 'values' is guaranteed to be type-safe and valid
  await $fetch('/api/users', { method: 'POST', body: values });
});
</script>
```

### Step 5: Handle Inconsistent API Response Envelopes (Zod 4 Pattern)

APIs rarely return data consistently. To handle this, we create a set of specialized, reusable schema generators in `schemas/apiResponse.ts`. These will parse different "envelopes" (like paginated lists or single items) and use `.transform()` to return only the clean, core data.

**Example**: `schemas/apiResponse.ts`

```typescript
import { z, type ZodType } from 'zod';

// Schema for paginated responses
export function createPaginatedResponseSchema<T extends ZodType>(itemSchema: T) {
  const schema = z.object({
    data: z.array(itemSchema),
    meta: z.object({
      total: z.number(),
      per_page: z.number(),
      current_page: z.number(),
      last_page: z.number(),
    }),
  }).transform(response => ({
    data: response.data,
    meta: response.meta,
  }));
  return schema;
}

// Schema for single item responses
export function createSingleItemResponseSchema<T extends ZodType>(dataSchema: T) {
  const schema = z.object({
    data: dataSchema,
  }).transform(response => response.data);
  return schema;
}

// Schema for a custom auth response
export function createAuthResponseSchema<T extends ZodType>(userSchema: T) {
  return z.object({
    user: userSchema,
    token: z.string(),
    message: z.string().optional(),
  });
}
```

**Usage in a composable:**

```typescript
import { userSchema } from '~/schemas/otoapi/user';
import { createPaginatedResponseSchema } from '~/schemas/apiResponse';

async function fetchUsers() {
  // Create a schema that expects the paginated envelope
  const usersResponseSchema = createPaginatedResponseSchema(userSchema);

  const rawResponse = await $fetch('/api/users');

  // Zod validates the envelope and .transform() returns the clean { data, meta } object
  const { data: users, meta } = usersResponseSchema.parse(rawResponse);

  console.log(users); // `users` is perfectly typed as `User[]`
  console.log(meta); // `meta` is a clean pagination object
  return { users, meta };
}
```

### Step 6: Handling Large External Payloads (Jubelio)

For external APIs with many fields, only define the fields you need and use `.passthrough()` to ignore the rest.

**Example**: `schemas/jubelio/product.ts`

```typescript
import { z } from 'zod';

// Define a schema for ONLY the fields you care about
export const jubelioProductSchema = z.object({
  product_id: z.number(),
  product_name: z.string(),
  sku: z.string(),
  stock_available: z.number(),
}).passthrough(); // <-- Allows other fields to be ignored but kept

export type JubelioProduct = z.infer<typeof jubelioProductSchema>;

---

### Step 7: Practical Usage Examples

Here is how to use the schemas and parsers from `schemas/apiResponse.ts` in your application, for instance, inside a composable or store.

#### Example 1: Fetching a Paginated List of Users

This applies when your API returns a response with `data`, `links`, and `meta` keys.

```typescript
// file: composables/useUsers.ts
import { userSchema, type User } from '~/schemas/otoapi/user';
import { parsePaginatedResponse } from '~/schemas/apiResponse';

export async function fetchUsers() {
  // Fetch the raw, unknown data from the API
  const rawResponse = await $fetch('/api/users');

  // Use the parser to validate the response and extract the clean data
  const { data: users, meta } = parsePaginatedResponse(rawResponse, userSchema);

  // `users` is now a fully-typed `User[]` array.
  // `meta` is the clean pagination object.
  console.log(users[0].name);
  console.log(meta.total);

  return { users, meta };
}
```

#### Example 2: Fetching a Single User

This applies when your API returns a single item wrapped in a `data` object: `{ "data": { ... } }`.

```typescript
// file: composables/useUsers.ts
import { userSchema, type User } from '~/schemas/otoapi/user';
import { parseSingleItemResponse } from '~/schemas/apiResponse';

export async function fetchUserById(userId: string): Promise<User> {
  // Fetch the raw, unknown data
  const rawResponse = await $fetch(`/api/users/${userId}`);

  // Use the parser to validate the response and extract the inner user object
  const user = parseSingleItemResponse(rawResponse, userSchema);

  // `user` is now a fully-typed `User` object, not the wrapper.
  console.log(user.email);

  return user;
}
```

#### Example 3: Handling the Login/Auth Response

This applies to custom response structures, like the one from your login endpoint. Since it doesn't have a separate parser function, we use the schema's `.parse()` method directly.

```typescript
// file: stores/auth.ts
import { userSchema, type User } from '~/schemas/otoapi/user';
import { createAuthResponseSchema } from '~/schemas/apiResponse';

export async function login(credentials: { email: string; password: string }) {
  // Create the specific schema for the auth response
  const authResponseSchema = createAuthResponseSchema(userSchema);

  // Fetch the raw data
  const rawResponse = await $fetch('/api/auth/login', {
    method: 'POST',
    body: credentials,
  });

  // Parse the response against the auth schema
  const { user, token, message } = authResponseSchema.parse(rawResponse);

  // You now have clean, validated, and typed data to work with.
  console.log(user.role);
  console.log(token);

  // ... save user and token to state ...
}
```