base header:
Content-Type: application/x-www-form-urlencoded
Accept:	application/json
User-Agent	(Fiiled with framework request, Ex: Java-Request)

it have 3 endpoint:
1. PRICES: POST /tracing/api/pricedev
body (value only example):
username:TESTAPI
api_key:25c898a9faea1a100859ecd9ef674548
from:KDR10000
thru:MJK10112
weight:1


response success:
{
    "price": [
        {
            "origin_name": "JAKARTA",
            "destination_name": "BUNGURSARI , TASIKMALAYA",
            "service_display": "J<PERSON>",
            "service_code": "JTR18",
            "goods_type": "Paket",
            "currency": "IDR",
            "price": "40000",
            "etd_from": "3",
            "etd_thru": "4",
            "times": "D"
        }
    ]
}

response error:
{
    "error": "Price Not Found.",
    "status": false
}
{
    "error": "Username Or API KEY Not Found.",
    "status": false
}



2. AIRWAYBILL: POST /tracing/api/generatecnote
body:
username:TES<PERSON>PI
api_key:25c898a9faea1a100859ecd9ef674548
OLSHOP_BRANCH:CGK000
OLSHOP_CUST:10950700
OLSHOP_ORDERID:2345556
OLSHOP_SHIPPER_NAME:ALI
OLSHOP_SHIPPER_ADDR1:JAKARTA NO 44
OLSHOP_SHIPPER_ADDR2:KALIBATA
OLSHOP_SHIPPER_ADDR3:KALIBATA
OLSHOP_SHIPPER_CITY:JAKARTA
OLSHOP_SHIPPER_REGION:JAKARTA
OLSHOP_SHIPPER_ZIP:12345
OLSHOP_SHIPPER_PHONE:+6289876543212
OLSHOP_RECEIVER_NAME:ANA
OLSHOP_RECEIVER_ADDR1:BANDUNG NO 12
OLSHOP_RECEIVER_ADDR2:CIBIRU
OLSHOP_RECEIVER_ADDR3:BANDUNG
OLSHOP_RECEIVER_CITY:BANDUNG
OLSHOP_RECEIVER_REGION:JAWA BARAT
OLSHOP_RECEIVER_ZIP:12365
OLSHOP_RECEIVER_PHONE:+6285789065432
OLSHOP_QTY:1
OLSHOP_WEIGHT:1
OLSHOP_GOODSDESC:TEST
OLSHOP_GOODSVALUE:1000
OLSHOP_GOODSTYPE:1
OLSHOP_INST:TEST
OLSHOP_INS_FLAG:N
OLSHOP_ORIG:CGK10000
OLSHOP_DEST:BDO10000
OLSHOP_SERVICE:REG
OLSHOP_COD_FLAG:N
OLSHOP_COD_AMOUNT:0

response success:
{
    "detail": [
        {
            "status": "sukses",
            "cnote_no": "0109401900003724"
        }
    ]
}

response error:
{
    "detail": [
        {
            "status": "Error",
            "reason": "Transaction no. already exists, please try with another",
            "cnote_no": "0109401900003724"
        }
    ]
}

{
    "error": "Username Or API KEY Not Found.",
    "status": "false"
}


3. TRACKING: POST /tracing/api/list/v1/cnote/{AWB}
body:
username:TESTAPI
api_key:25c898a9faea1a100859ecd9ef674548

response success:
{
    "cnote": {
        "cnote_no": "XXXXXXXXXXXXX",
        "reference_number": "932323",
        "cnote_origin": "BDO21200",
        "cnote_destination": "BDO21216",
        "cnote_services_code": "REG",
        "servicetype": "REG19",
        "cnote_cust_no": "80540008",
        "cnote_date": "2022-04-02T17:45:08.000+07:00",
        "cnote_pod_receiver": "WAWAN",
        "cnote_receiver_name": "WAWAN",
        "city_name": "PAGELARAN, CIANJUR",
        "cnote_pod_date": "2022-04-03T13:31:00.000+07:00",
        "pod_status": "DELIVERED",
        "last_status": "DELIVERED TO [WAWAN | 03-04-2022 13:31 | BANDUNG ]",
        "cust_type": "060",
        "cnote_amount": "9000",
        "cnote_weight": "1",
        "pod_code": "D01",
        "keterangan": "YANG BERSANGKUTAN",
        "cnote_goods_descr": "MESIN CUCI SHARP EST 80MW 8KG 2TABUNG",
        "freight_charge": "9000",
        "shippingcost": "9000",
        "insuranceamount": "0",
        "priceperkg": "9000",
        "signature": "https://s3-ap-southeast-1.amazonaws.com/pod.paket.id/1648967500876S||XXXXX.jpeg",
        "photo": "https://s3-ap-southeast-1.amazonaws.com/pod.paket.id/1648967469777P||xXXXXX.jpeg",
        "long": "107.175963",
        "lat": "-7.165699",
        "estimate_delivery": "6 Days"
    },
    "detail": [
        {
            "cnote_no": "0226022200068098",
            "cnote_date": "2022-04-02T17:45:08.000+07:00",
            "cnote_weight": "1",
            "cnote_origin": "BDO21200",
            "cnote_shipper_name": "IRENE ROSSELLINI ATMADJA",
            "cnote_shipper_addr1": "JL. MANGUNSARKORO NO 6 CIANJUR",
            "cnote_shipper_addr2": " ",
            "cnote_shipper_addr3": null,
            "cnote_shipper_city": "CIANJUR",
            "cnote_receiver_name": "WAWAN",
            "cnote_receiver_addr1": "KP LEMBUR SAWAH RT 01/01",
            "cnote_receiver_addr2": null,
            "cnote_receiver_addr3": null,
            "cnote_receiver_city": "PAGELARAN, CIANJUR"
        }
    ],
    "history": [
        {
            "date": "02-04-2022 17:45",
            "desc": "SHIPMENT RECEIVED BY JNE COUNTER OFFICER AT [CIANJUR, CIANJUR]",
            "code": "RC1"
        },
        {
            "date": "02-04-2022 18:02",
            "desc": "PICKED UP BY COURIER [CIANJUR, CIANJUR]",
            "code": "PU1"
        }
    ]
}


response error:
{
    "error": "Cnote No. Not Found.",
    "status": false
}

{
    "error": "Sorry This is not your cnote.",
    "status": false
}

{
    "error": "Username Or API KEY Not Found.",
    "status": false
}