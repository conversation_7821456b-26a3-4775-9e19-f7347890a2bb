{"message": "Notifications retrieved successfully", "data": {"current_page": 1, "data": [{"id": "8e4a8891-5576-4f93-ad60-49f94299b65d", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Cancelled", "message": "Status for order #11 (1135) by <PERSON><PERSON><PERSON> was updated from 'pending' to 'cancelled' by System.", "type": "order", "action": "status_updated", "order": {"id": 11, "jubelio_order_id": 1135, "old_status": "pending", "new_status": "cancelled", "grand_total": "69500.00", "updated_at": "2025-08-05T01:21:20.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-08-05T01:21:20.000000Z", "updated_at": "2025-08-05T01:21:20.000000Z"}, {"id": "8b405086-7ab5-43c9-8fe8-165a2f65ab0c", "type": "App\\Notifications\\OrderCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New Order Created", "message": "A new order #1135 has been created by <PERSON><PERSON><PERSON>", "type": "order", "action": "created", "order": {"id": 11, "jubelio_order_id": 1135, "status": "pending", "grand_total": "69500.00", "created_at": "2025-08-05T00:50:47.000000Z"}, "created_by": {"id": 2, "name": "<PERSON><PERSON><PERSON>", "role": "reseller"}}, "read_at": "2025-08-05T00:59:24.000000Z", "created_at": "2025-08-05T00:50:48.000000Z", "updated_at": "2025-08-05T00:59:24.000000Z"}, {"id": "42798fe8-8a14-47ed-b9f8-c9760601feea", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Status Updated", "message": "Order #1134 (created by <PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'paid' by System", "type": "order", "action": "status_updated", "order": {"id": 10, "jubelio_order_id": 1134, "old_status": "pending", "new_status": "paid", "grand_total": "82500.00", "updated_at": "2025-08-05T00:47:36.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-08-05T00:47:36.000000Z", "updated_at": "2025-08-05T00:47:36.000000Z"}, {"id": "feaedab4-026d-4821-bccf-a82d975bb470", "type": "App\\Notifications\\OrderCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New Order Created", "message": "A new order #1134 has been created by <PERSON><PERSON><PERSON>", "type": "order", "action": "created", "order": {"id": 10, "jubelio_order_id": 1134, "status": "pending", "grand_total": "82500.00", "created_at": "2025-08-05T00:46:26.000000Z"}, "created_by": {"id": 2, "name": "<PERSON><PERSON><PERSON>", "role": "reseller"}}, "read_at": null, "created_at": "2025-08-05T00:46:27.000000Z", "updated_at": "2025-08-05T00:46:27.000000Z"}, {"id": "8bdb2523-c428-45ff-8c9c-12859734f193", "type": "App\\Notifications\\UserCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New User Created", "message": "A new user 'admin 3' has been created by Admin", "type": "user", "action": "created", "user": {"id": 10, "name": "admin 3", "email": "<EMAIL>", "role": "admin", "created_at": "2025-08-04T00:53:13.000000Z"}, "created_by": {"id": 1, "name": "Admin"}}, "read_at": null, "created_at": "2025-08-04T00:53:13.000000Z", "updated_at": "2025-08-04T00:53:13.000000Z"}, {"id": "0e685143-30a7-42f4-b685-0da4f2728766", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Cancelled", "message": "Order #1089 (created by <PERSON><PERSON><PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'cancelled' by System", "type": "order", "action": "status_updated", "order": {"id": 9, "jubelio_order_id": 1089, "old_status": "pending", "new_status": "cancelled", "grand_total": "87000.00", "updated_at": "2025-08-01T11:30:35.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-08-01T11:30:35.000000Z", "updated_at": "2025-08-01T11:30:35.000000Z"}, {"id": "c07716c0-277f-44c0-88c9-97b984a71f94", "type": "App\\Notifications\\OrderCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New Order Created", "message": "A new order #1089 has been created by <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "order", "action": "created", "order": {"id": 9, "jubelio_order_id": 1089, "status": "pending", "grand_total": "87000.00", "created_at": "2025-08-01T11:00:16.000000Z"}, "created_by": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "reseller"}}, "read_at": null, "created_at": "2025-08-01T11:00:17.000000Z", "updated_at": "2025-08-01T11:00:17.000000Z"}, {"id": "2323382b-7d17-4632-8afd-8d5405ce54c2", "type": "App\\Notifications\\UserCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New User Created", "message": "A new user 'Test Contact' has been created by Ad<PERSON>", "type": "user", "action": "created", "user": {"id": 4, "name": "Test Contact", "email": "<EMAIL>", "role": "reseller", "created_at": "2025-07-27T01:48:42.000000Z"}, "created_by": {"id": 1, "name": "Admin"}}, "read_at": null, "created_at": "2025-07-27T01:48:42.000000Z", "updated_at": "2025-07-27T01:48:42.000000Z"}, {"id": "c2eaddb0-b7d0-4596-b6e7-e49584ee4d28", "type": "App\\Notifications\\UserCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New User Created", "message": "A new user 'Admin 2' has been created by Admin", "type": "user", "action": "created", "user": {"id": 3, "name": "Admin 2", "email": "<EMAIL>", "role": "admin", "created_at": "2025-07-27T01:46:02.000000Z"}, "created_by": {"id": 1, "name": "Admin"}}, "read_at": null, "created_at": "2025-07-27T01:46:03.000000Z", "updated_at": "2025-07-27T01:46:03.000000Z"}, {"id": "fc5bb0ad-529e-4fc9-b235-aed328433732", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Cancelled", "message": "Order #952 (created by <PERSON><PERSON><PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'cancelled' by System", "type": "order", "action": "status_updated", "order": {"id": 5, "jubelio_order_id": 952, "old_status": "pending", "new_status": "cancelled", "grand_total": "776500.00", "updated_at": "2025-07-24T06:58:20.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-07-24T06:58:20.000000Z", "updated_at": "2025-07-24T06:58:20.000000Z"}, {"id": "1b8f8d15-d494-45ad-85a1-d85cac35fa31", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Status Updated", "message": "Order #955 (created by <PERSON><PERSON><PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'paid' by System", "type": "order", "action": "status_updated", "order": {"id": 8, "jubelio_order_id": 955, "old_status": "pending", "new_status": "paid", "grand_total": "72500.00", "updated_at": "2025-07-24T06:52:55.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-07-24T06:52:55.000000Z", "updated_at": "2025-07-24T06:52:55.000000Z"}, {"id": "3cad4f5b-25fb-4cce-adb0-7544428ab01c", "type": "App\\Notifications\\OrderCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New Order Created", "message": "A new order #955 has been created by <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "order", "action": "created", "order": {"id": 8, "jubelio_order_id": 955, "status": "pending", "grand_total": "72500.00", "created_at": "2025-07-24T06:52:43.000000Z"}, "created_by": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "reseller"}}, "read_at": null, "created_at": "2025-07-24T06:52:43.000000Z", "updated_at": "2025-07-24T06:52:43.000000Z"}, {"id": "0124d8e6-615e-42e1-bb41-b53dbd696ddb", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Status Updated", "message": "Order #954 (created by <PERSON><PERSON><PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'paid' by System", "type": "order", "action": "status_updated", "order": {"id": 7, "jubelio_order_id": 954, "old_status": "pending", "new_status": "paid", "grand_total": "69500.00", "updated_at": "2025-07-24T06:36:01.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-07-24T06:36:01.000000Z", "updated_at": "2025-07-24T06:36:01.000000Z"}, {"id": "247b46d9-369e-4f66-8982-79be5a19eb32", "type": "App\\Notifications\\OrderCreatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "New Order Created", "message": "A new order #954 has been created by <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "order", "action": "created", "order": {"id": 7, "jubelio_order_id": 954, "status": "pending", "grand_total": "69500.00", "created_at": "2025-07-24T06:35:23.000000Z"}, "created_by": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "reseller"}}, "read_at": null, "created_at": "2025-07-24T06:35:24.000000Z", "updated_at": "2025-07-24T06:35:24.000000Z"}, {"id": "4ec3dca5-b794-40cc-9e3e-03a8233b8f62", "type": "App\\Notifications\\OrderStatusUpdatedNotification", "notifiable_type": "App\\Models\\User", "notifiable_id": 1, "data": {"title": "Order Status Updated", "message": "Order #953 (created by <PERSON><PERSON><PERSON><PERSON><PERSON>) status has been updated from 'pending' to 'paid' by System", "type": "order", "action": "status_updated", "order": {"id": 6, "jubelio_order_id": 953, "old_status": "pending", "new_status": "paid", "grand_total": "82500.00", "updated_at": "2025-07-24T06:33:48.000000Z"}, "updated_by": null, "order_owner": {"id": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "read_at": null, "created_at": "2025-07-24T06:33:48.000000Z", "updated_at": "2025-07-24T06:33:48.000000Z"}], "first_page_url": "http://otoapi.local/api/notifications?page=1", "from": 1, "last_page": 2, "last_page_url": "http://otoapi.local/api/notifications?page=2", "links": [{"url": null, "label": "&laquo; Previous", "active": false}, {"url": "http://otoapi.local/api/notifications?page=1", "label": "1", "active": true}, {"url": "http://otoapi.local/api/notifications?page=2", "label": "2", "active": false}, {"url": "http://otoapi.local/api/notifications?page=2", "label": "Next &raquo;", "active": false}], "next_page_url": "http://otoapi.local/api/notifications?page=2", "path": "http://otoapi.local/api/notifications", "per_page": 15, "prev_page_url": null, "to": 15, "total": 25}}