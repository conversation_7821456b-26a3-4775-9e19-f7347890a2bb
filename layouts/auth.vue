<template>
  <v-app>
    <v-main class="auth-layout">
      <slot />
    </v-main>
  </v-app>
</template>

<script setup>
// No auth check needed in this layout since it's for public pages
</script>

<style scoped>
.auth-layout {
  /* Option 1: Professional Deep Purple (Recommended for reseller) */
  background: linear-gradient(135deg, #faf8ff 0%, #f3e8ff 25%, #e9d5ff 50%, #7c3aed 100%);
  
  /* Option 2: Modern Purple-Violet */
  /* background: linear-gradient(135deg, #fdfcff 0%, #f5f3ff 30%, #ddd6fe 60%, #8b5cf6 100%); */
  
  /* Option 3: <PERSON> Purple */
  /* background: linear-gradient(135deg, #faf8ff 0%, #ede9fe 25%, #c4b5fd 70%, #6d28d9 100%); */
  
  /* Option 4: Elegant Plum */
  /* background: linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 20%, #c084fc 60%, #581c87 100%); */
  
  /* Option 5: Premium Purple with subtle pink */
  /* background: linear-gradient(135deg, #fefcff 0%, #fdf2f8 20%, #f3e8ff 50%, #8b5cf6 85%, #7c2d92 100%); */
  
  min-height: 100vh;
  position: relative;
}

/* Add subtle pattern overlay */
.auth-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 50px 50px, 50px 50px;
  background-position: 0 0, 25px 25px;
  opacity: 0.4;
  pointer-events: none;
}
</style>
