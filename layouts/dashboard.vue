<template>
  <div>
    <client-only>
      <!-- Splash Screen -->
      <Transition name="fade">
        <AppSplashScreen v-if="isLoading || isAuthChecking" />
      </Transition>
      
      <!-- Main App -->
      <v-app v-if="!isLoading && !isAuthChecking">
        <!-- App Header -->
        <AppHeader />
        
        <!-- Cart Sidebar -->
        <AppCartSidebar />
        
        <!-- Sidebar Navigation -->
        <AppSidebar />
        
        <!-- Main Content -->
        <v-main>
          <v-container fluid>
            <slot />
          </v-container>
        </v-main>
        
        <!-- Footer -->
        <AppFooter />
        
        <!-- Session Expiry Warning -->
        <v-dialog v-model="showSessionWarning" persistent max-width="400">
          <v-card>
            <v-card-title class="text-h5">
              Session Expiring Soon
            </v-card-title>
            <v-card-text>
              Your session will expire soon. Would you like to stay logged in?
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="error" variant="text" @click="logout()">
                Logout
              </v-btn>
              <v-btn color="purple" variant="elevated" @click="refreshSession">
                Stay Logged In
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-app>
    </client-only>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'
import { useAuthStore } from '~/stores/auth'

// Get layout state from store
const dashboardStore = useDashboardLayoutStore()
const { finishLoading } = dashboardStore
const { isLoading } = storeToRefs(dashboardStore)

// Auth store
const authStore = useAuthStore()
const { logout } = authStore
const { loading: isAuthChecking } = storeToRefs(authStore)

// Session warning state
const showSessionWarning = ref(false)
let sessionCheckInterval: NodeJS.Timeout | null = null

onMounted(async () => {

  // logout()
  // Check authentication first if needed
  if (!authStore.isAuthenticated) {
    await authStore.checkAuth()
  }
  
  // Set up session warning monitoring
  setupSessionWarning()
  
  // Simulate loading time or wait for resources to load
  setTimeout(() => {
    finishLoading()
  }, 1000)
})

// Clean up intervals on component unmount
onBeforeUnmount(() => {
  if (sessionCheckInterval) clearInterval(sessionCheckInterval)
})

// Setup session warning with interval-based checking
const setupSessionWarning = () => {
  // Clear any existing interval
  if (sessionCheckInterval) clearInterval(sessionCheckInterval)
  
  // Check token expiry every 30 minutes
  sessionCheckInterval = setInterval(() => {
    const tokenExpiry = authStore.getTokenExpiry()
    
    if (tokenExpiry === 0) {
      console.warn('No valid token found')
      return
    }
    
    const now = Date.now()
    const timeUntilExpiry = tokenExpiry - now
    const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60))
    
    console.log(`Token expires in ${minutesUntilExpiry} minutes`)
    
    // If less than 30 minutes remaining, show warning
    if (timeUntilExpiry <= 30 * 60 * 1000 && timeUntilExpiry > 0) {
      console.log('Showing session expiry warning')
      showSessionWarning.value = true
      // Stop checking once warning is shown
      if (sessionCheckInterval) clearInterval(sessionCheckInterval)
    }
    
    // If token already expired, logout immediately
    if (timeUntilExpiry <= 0) {
      console.log('Token expired, logging out')
      if (sessionCheckInterval) clearInterval(sessionCheckInterval)
      logout()
    }
  }, 30 * 60 * 1000) // Check every 30 minutes
  
  // Log initial setup
  const expiry = authStore.getTokenExpiry()
  if (expiry > 0) {
    const expiryDate = new Date(expiry)
    console.log(`Session monitoring started. Token expires at: ${expiryDate.toLocaleString()}`)
  }
}

// Refresh the user's session
const refreshSession = async () => {
  const success = await authStore.refreshToken()
  
  if (success) {
    showSessionWarning.value = false
    // Reset the warning monitoring after successful refresh
    setupSessionWarning()
  } else {
    // If refresh fails, force logout
    logout()
  }
}
</script>


<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
