<template>
  <div>
    <!-- Global loading overlay -->
    <v-overlay v-if="loading" :model-value="true" color="primary" opacity="0.8">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    
    <!-- Page content -->
    <slot />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '~/stores/auth'

const loading = ref(false)
const authStore = useAuthStore()

// Set up a global navigation hook to show loading during auth checks
const router = useRouter()

router.beforeEach(() => {
  loading.value = true
})

router.afterEach(() => {
  // Add a small delay to make the transition smoother
  setTimeout(() => {
    loading.value = false
  }, 200)
})
</script>
