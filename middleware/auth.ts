import type { AuthCheckResponse } from '~/schemas/api/auth'

export default defineNuxtRouteMiddleware(async (to) => {
  // Skip for API routes
  if (to.path.startsWith('/api/')) {
    return
  }
  
  // For server-side rendering, don't redirect - just let the page render
  if (import.meta.server) {
    return
  }
  
  try {
    // Direct API call to check authentication
    const response = await $fetch<AuthCheckResponse>('/api/auth/check')
    
    if (!response.authenticated) {
      // Save the current path for redirect after login
      const authStore = useAuthStore()
      authStore.saveRedirectPath(to.fullPath)
      return navigateTo('/auth/login')
    }
    
    // Update auth store with user data if authenticated
    if (response.authenticated && response.user) {
      const authStore = useAuthStore()
      authStore.isAuthenticated = true
      authStore.user = response.user
      authStore.token = response.token ?? null
      
      // Store the CSRF token if provided
      if (response.csrfToken) {
        authStore.csrfToken = response.csrfToken
      }
    }
  } catch (error) {
    console.error('Auth check error:', error)
    return navigateTo('/auth/login')
  }
})
