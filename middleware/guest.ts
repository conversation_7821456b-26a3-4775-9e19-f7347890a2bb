import type { AuthCheckResponse } from '~/schemas/api/auth'

export default defineNuxtRouteMiddleware(async (to) => {
  try {
    // Direct API call to check authentication with proper typing
    const response = await $fetch<AuthCheckResponse>('/api/auth/check')
    
    if (response.authenticated && response.user) {
      // Update auth store with user data if authenticated
      const authStore = useAuthStore()
      authStore.isAuthenticated = true
      authStore.user = response.user
      
      // Store the token if provided
      if (response.token) {
        authStore.token = response.token
      }
      
      // Store the CSRF token if provided
      if (response.csrfToken) {
        authStore.csrfToken = response.csrfToken
      }
      
      // Redirect to home if already authenticated
      return navigateTo('/')
    }
  } catch (error) {
    console.error('Auth check error:', error)
    // If there's an error checking auth, we assume not authenticated
    // and allow access to guest pages
  }
})
