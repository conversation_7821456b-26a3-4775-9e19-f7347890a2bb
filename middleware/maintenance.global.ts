import { useAuthStore } from '~/stores/auth'

export default defineNuxtRouteMiddleware(async (to) => {
  const isMaintenancePage = to.path === '/maintenance';
  const authStore = useAuthStore();

  try {
    const statusResponse = await $fetch<{ isMaintenance: boolean }>('/api/status');
    const isMaintenanceMode = statusResponse.isMaintenance;

    if (isMaintenanceMode) {
      // --- Maintenance is ON ---
      if (!isMaintenancePage) {
        // If user is not on the maintenance page, force them there.
        if (authStore.isAuthenticated) {
          await authStore.logout({ redirect: false });
        }
        return navigateTo('/maintenance', { replace: true });
      }
      // If user is already on the maintenance page, do nothing.
    } else {
      // --- Maintenance is OFF ---
      if (isMaintenancePage) {
        // If user is on the maintenance page, redirect them to the homepage.
        return navigateTo('/', { replace: true });
      }
      // If user is not on the maintenance page, do nothing.
    }
  } catch (error) {
    // If the status check itself fails, log it but don't block the user.
    console.error('Maintenance check failed:', error);
  }
});