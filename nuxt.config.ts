// https://nuxt.com/docs/api/configuration/nuxt-config
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'
import { version as appVersion } from './package.json'

export default defineNuxtConfig({
  ssr: true,
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  
  build: {
    transpile: ['vuetify'],
  },
  
  modules: [
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    (_options, nuxt) => {
      nuxt.hooks.hook('vite:extendConfig', (config) => {
        // @ts-expect-error
        config.plugins.push(vuetify({ autoImport: true }))
      })
    },
    'dayjs-nuxt',
    '@nuxtjs/turnstile',
    //...
  ],
  
  vite: {
    vue: {
      template: {
        transformAssetUrls,
      },
    },
    esbuild: {
      pure: process.env.NODE_ENV === 'production' ? ['console.log', 'console.debug'] : [],
    },
  },

  dayjs: {
    locales: ['id'],
    plugins: ['relativeTime', 'utc', 'timezone'],
    defaultLocale: 'id',
    defaultTimezone: 'Asia/Jakarta',
  },
  
  runtimeConfig: {
    jubelio: {
      baseurl: process.env.NUXT_JUBELIO_BASE_URL,
      email: process.env.NUXT_JUBELIO_EMAIL || '<EMAIL>',
      password: process.env.NUXT_JUBELIO_PASSWORD || 'ZWtWmvmu9djJC@4',
    },
    ongkir: {
      baseurl: process.env.NUXT_ONGKIR_BASE_URL || 'https://ongkir.zafrada.com/api',
    },
    rajaongkir: {
      baseurl: process.env.NUXT_RAJAONGKIR_BASE_URL || 'https://rajaongkir.komerce.id/api/v1',
      apikey: process.env.NUXT_RAJAONGKIR_API_KEY,
    },
    otoapi: {
      baseurl: process.env.NUXT_OTOAPI_BASE_URL,
    },
    jwt: {
      secret: process.env.NUXT_JWT_SECRET || '9d366d480d7942675dba3412d443509cd1c4a40b411e38bf84108556ce767157d58fdee7b56391f3031eda2a44ef74024297bac64bb7754c7af9348575f7b9d2',
      expiry: process.env.NUXT_JWT_EXPIRY || '12h',
    },
    cookies: {
      secret: process.env.NUXT_COOKIE_SECRET || '730b12ada6ad01bcd90f7729d2298ff97a78dc01cc34779ce1291e8cf24993b55d44f1773821230d4e38846bc9f0c7ed5fd97434c1947d087b252279d670ff6a',
    },
    // Add CORS configuration
    cors: {
      allowedOrigins: process.env.NUXT_ALLOWED_ORIGINS,
    },
    turnstile: {
      secretKey: process.env.NUXT_TURNSTILE_SECRET_KEY,
    },
    jne: {
      baseurl: process.env.NUXT_JNE_BASE_URL,
      username: process.env.NUXT_JNE_USERNAME,
      apiKey: process.env.NUXT_JNE_API_KEY,
    },
    public: {
      useSecureCookies: process.env.NUXT_USE_SECURE_COOKIES === 'true',
      app: {
         headerAppIdentifier: process.env.NUXT_HEADER_APP_IDENTIFIER || 'otoadmin-web',
         version: appVersion
      },
      turnstile: {
        // This becomes available as useRuntimeConfig().public.turnstile.siteKey
        siteKey: process.env.NUXT_PUBLIC_TURNSTILE_SITE_KEY,
      }
    }
  }
})
