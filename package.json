{"name": "nuxt-app", "version": "0.1.16", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/scripts": "^0.11.10", "@nuxtjs/turnstile": "^1.0.0", "@pinia/nuxt": "^0.11.0", "dayjs-nuxt": "^2.1.11", "jose": "^6.0.10", "nuxt": "^3.16.2", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "roboto-fontface": "^0.10.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^4.0.5"}, "devDependencies": {"@mdi/font": "^7.4.47", "@types/node": "^22.14.1", "vite-plugin-vuetify": "^2.1.1", "vuetify": "^3.8.1"}}