<template>
  <div>
    <!-- <PERSON>er with Add Button -->
    <div class="d-flex flex-column flex-sm-row justify-space-between align-start align-sm-center mb-4 ga-4">
      <div>
        <PageTitle 
          title="Shipping Addresses"
          icon="mdi-map-marker-multiple-outline"
          subtitle="Manage your shipping addresses for faster checkout."
        />
      </div>
    </div>

    <div class="mb-4">
      <v-btn
        color="deep-purple"
        prepend-icon="mdi-plus"
        size="large"
        elevation="2"
        class="flex-shrink-0"
        @click="showAddressForm = true"
      >
        Add New Address
      </v-btn>
    </div>

    <!-- Search and Filter Section -->
    <!-- Filter & Sort Section -->
    <v-card class="mb-6 rounded-lg" elevation="0" border>
      <v-card-text class="pa-4">
        <v-text-field
          v-model="searchQuery"
          prepend-inner-icon="mdi-magnify"
          label="Search addresses by name, whatsapp, or location..."
          density="comfortable"
          hide-details
          variant="outlined"
          rounded="pill"
          single-line
          color="deep-purple"
          @keyup.enter="handleSearch"
        >
          <template v-slot:append-inner>
            <v-btn
              v-if="searchQuery"
              icon="mdi-close-circle"
              variant="text"
              size="small"
              @click="clearSearch"
              class="mr-2"
              aria-label="Clear search"
            ></v-btn>
            <v-btn
              color="deep-purple"
              variant="flat"
              @click="handleSearch"
              :loading="addressStore.loading"
              icon="mdi-magnify"
              size="small"
              aria-label="Search"
              class="rounded-pill"
            >
            </v-btn>
          </template>
        </v-text-field>
      </v-card-text>
      <v-expansion-panels flat>
        <v-expansion-panel>
          <v-expansion-panel-title>
            <template v-slot:default="{ expanded }">
              <v-icon size="small" class="mr-3">mdi-filter-variant</v-icon>
              <span class="text-subtitle-1 font-weight-medium">Filter & Sort</span>
              <v-spacer></v-spacer>
              <v-chip v-if="!expanded && activeFilterCount > 0" size="small" color="deep-purple" class="mr-4">
                {{ activeFilterCount }} Active Filter{{ activeFilterCount > 1 ? 's' : '' }}
              </v-chip>
            </template>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-divider class="mb-4"></v-divider>
            <v-row dense>
              <!-- Sort By -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortBy"
                  label="Sort by"
                  :items="sortOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
              
              <!-- Sort Direction -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortDirection"
                  label="Order"
                  :items="sortDirectionOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
              
              <!-- Per Page -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="perPage"
                  label="Items per page"
                  :items="perPageOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
            </v-row>
            
            <div class="d-flex justify-end ga-2 mt-4">
              <v-btn
                color="grey"
                variant="text"
                @click="clearFilters"
              >
                Clear All Filters
              </v-btn>
              <v-btn
                color="deep-purple"
                variant="outlined"
                prepend-icon="mdi-refresh"
                @click="loadAddresses"
                :loading="addressStore.loading"
              >
                Refresh
              </v-btn>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card>


    <!-- Results Summary -->
    <v-card 
      v-if="!addressStore.loading && addressStore.addresses.length > 0" 
      class="mb-6 rounded-lg" 
      elevation="1"
      color="grey-lighten-5"
    >
      <v-card-text class="py-3">
        <div class="d-flex flex-column flex-sm-row justify-space-between align-start align-sm-center ga-2">
          <div class="text-body-2 text-grey-darken-1">
            <v-icon size="small" class="mr-1">mdi-information-outline</v-icon>
            Showing <span class="font-weight-bold">{{ addressStore.addresses.length }}</span> 
            of <span class="font-weight-bold">{{ addressStore.totalCount }}</span> addresses
            <span v-if="searchQuery" class="text-deep-purple">
              (filtered by "{{ searchQuery }}")
            </span>
          </div>
          <div class="text-body-2 text-grey-darken-1">
            Page <span class="font-weight-bold">{{ currentPage }}</span> 
            of <span class="font-weight-bold">{{ totalPages }}</span>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Loading State -->
    <v-card v-if="addressStore.loading" class="pa-8 mb-6 rounded-xl" elevation="1">
      <div class="d-flex flex-column justify-center align-center py-8">
        <v-progress-circular 
          indeterminate 
          color="deep-purple" 
          size="48"
          width="4"
        ></v-progress-circular>
        <span class="mt-4 text-body-1 text-grey-darken-1">Loading shipping addresses...</span>
      </div>
    </v-card>

    <!-- Error State -->
    <v-card 
      v-else-if="addressStore.error" 
      class="pa-6 mb-6 rounded-xl" 
      elevation="1"
      color="error-lighten-5"
      border="error-lighten-2"
    >
      <div class="d-flex align-start">
        <v-icon color="error" class="mr-4 mt-1" size="28">mdi-alert-circle</v-icon>
        <div class="flex-grow-1">
          <div class="text-h6 font-weight-medium text-error mb-2">Unable to load addresses</div>
          <div class="text-body-2 text-grey-darken-1 mb-4">{{ addressStore.error }}</div>
          <v-btn 
            color="error" 
            variant="outlined"
            prepend-icon="mdi-refresh"
            @click="loadAddresses"
          >
            Try Again
          </v-btn>
        </div>
      </div>
    </v-card>

    <!-- Addresses List -->
    <template v-else-if="addressStore.addresses.length > 0">
      <div class="addresses-grid">
        <v-card 
          v-for="address in addressStore.addresses"
          :key="address.id"
          class="address-card rounded-xl"
          elevation="2"
        >
          <!-- Address Header -->
          <v-card-item class="pb-2">
            <div class="d-flex justify-space-between align-start">
              <div class="flex-grow-1">
                <div class="d-flex align-center mb-2">
                  <v-avatar color="deep-purple-lighten-4" size="32" class="mr-3">
                    <v-icon color="deep-purple" size="18">mdi-account</v-icon>
                  </v-avatar>
                  <div>
                    <div class="text-subtitle-1 font-weight-bold">{{ address.shipping_full_name }}</div>
                    <div class="text-body-2 text-grey-darken-1 d-flex align-center">
                      <v-icon size="14" class="mr-1" color="green">mdi-whatsapp</v-icon>
                      <span>{{ address.shipping_phone }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <v-chip 
                size="small" 
                color="deep-purple" 
                variant="outlined"
                prepend-icon="mdi-identifier"
              >
                #{{ address.id }}
              </v-chip>
            </div>
          </v-card-item>

          <!-- Address Content -->
          <v-card-text class="pt-0">
            <!-- Full Address -->
            <div class="address-section mb-4">
              <div class="d-flex align-start">
                <v-icon color="deep-purple" class="mr-3 mt-1" size="20">mdi-map-marker</v-icon>
                <div class="flex-grow-1">
                  <div class="text-body-1 font-weight-medium mb-1">{{ address.shipping_address }}</div>
                  <div class="text-body-2 text-grey-darken-1">
                    {{ address.shipping_district }}, {{ address.shipping_city }}
                  </div>
                  <div class="text-body-2 text-grey-darken-1">
                    {{ address.shipping_province }}
                    <span v-if="address.shipping_post_code"> • {{ address.shipping_post_code }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Location Details Grid -->
            <div class="location-grid mb-4">
              <div class="location-item">
                <div class="text-caption text-grey font-weight-medium">PROVINCE</div>
                <div class="text-body-2">{{ address.shipping_province }}</div>
              </div>
              <div class="location-item">
                <div class="text-caption text-grey font-weight-medium">CITY</div>
                <div class="text-body-2">{{ address.shipping_city }}</div>
              </div>
              <div class="location-item">
                <div class="text-caption text-grey font-weight-medium">DISTRICT</div>
                <div class="text-body-2">{{ address.shipping_district }}</div>
              </div>
              <div class="location-item">
                <div class="text-caption text-grey font-weight-medium">SUBDISTRICT</div>
                <div class="text-body-2">{{ address.shipping_subdistrict }}</div>
              </div>
            </div>

            <!-- User Info -->
            <!-- <div v-if="address.user" class="user-info mb-4">
              <v-chip 
                size="small" 
                color="deep-purple-lighten-2" 
                prepend-icon="mdi-account-circle"
                class="text-caption"
              >
                {{ address.user.name }}
              </v-chip>
            </div> -->

            <!-- Timestamps -->
            <div class="timestamps">
              <div class="d-flex justify-space-between text-caption text-grey">
                <span>
                  <v-icon size="12" class="mr-1">mdi-calendar-plus</v-icon>
                  {{ formatDate(address.created_at) }}
                </span>
                <span>
                  <v-icon size="12" class="mr-1">mdi-calendar-edit</v-icon>
                  {{ formatDate(address.updated_at) }}
                </span>
              </div>
            </div>
          </v-card-text>

          <!-- Address Actions -->
          <v-card-actions class="pt-0 px-4 pb-4">
            <div class="d-flex justify-end w-100 ga-2">
              <v-btn
                variant="outlined"
                color="deep-purple"
                size="small"
                prepend-icon="mdi-eye"
                @click="viewAddress(address)"
              >
                View
              </v-btn>
              <v-btn
                variant="outlined"
                color="deep-purple"
                size="small"
                prepend-icon="mdi-pencil"
                @click="editAddress(address)"
              >
                Edit
              </v-btn>
              <v-btn
                variant="outlined"
                color="error"
                size="small"
                prepend-icon="mdi-delete"
                @click="confirmDeleteAddress(address.id)"
              >
                Delete
              </v-btn>
            </div>
          </v-card-actions>
        </v-card>
      </div>

      <!-- Pagination -->
      <v-card class="mt-6 pa-4 rounded-xl" elevation="1">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="7"
          color="deep-purple"
          @update:model-value="handlePageChange"
          class="justify-center"
        ></v-pagination>
      </v-card>
    </template>

    <!-- Empty State -->
    <v-card v-else class="pa-12 text-center rounded-xl" elevation="1">
      <div class="empty-state">
        <v-avatar size="80" color="grey-lighten-3" class="mb-4">
          <v-icon size="40" color="grey-lighten-1">mdi-map-marker-off</v-icon>
        </v-avatar>
        <h2 class="text-h5 mb-3 text-grey-darken-2">{{ getEmptyStateTitle() }}</h2>
        <p class="text-body-1 text-grey-darken-1 mb-6 max-width-400 mx-auto">
          {{ getEmptyStateMessage() }}
        </p>
        <v-btn 
          v-if="!searchQuery"
          color="deep-purple" 
          size="large" 
          prepend-icon="mdi-plus" 
          elevation="2"
          @click="showAddressForm = true"
        >
          Add Your First Address
        </v-btn>
        <v-btn 
          v-else
          color="grey" 
          variant="outlined"
          prepend-icon="mdi-filter-remove" 
          @click="clearFilters"
        >
          Clear Search
        </v-btn>
      </div>
    </v-card>

    <!-- Address Detail Dialog -->
    <v-dialog v-model="showDetailDialog" max-width="600">
      <v-card v-if="selectedAddress" class="rounded-xl">
        <v-card-title class="text-h5 d-flex align-center pa-6 bg-deep-purple-lighten-5">
          <v-icon icon="mdi-map-marker" class="mr-3" color="deep-purple" size="28"></v-icon>
          Address Details
        </v-card-title>
        
        <v-card-text class="pa-6">
          <div class="mb-6">
            <div class="text-h6 font-weight-bold mb-2">{{ selectedAddress.shipping_full_name }}</div>
            <div class="text-body-1 mb-2 d-flex align-center">
              <v-icon size="16" class="mr-2" color="green">mdi-whatsapp</v-icon>
              {{ selectedAddress.shipping_phone }}
            </div>
            <div class="text-body-1 mb-3">{{ selectedAddress.shipping_address }}</div>
            <div class="text-body-1 text-grey-darken-1">
              {{ selectedAddress.shipping_district }}, {{ selectedAddress.shipping_city }}, {{ selectedAddress.shipping_province }}
              <span v-if="selectedAddress.shipping_post_code"> - {{ selectedAddress.shipping_post_code }}</span>
            </div>
          </div>
          
          <v-divider class="my-3"></v-divider>
          
          <v-row>
            <v-col class="col-6">
              <div class="text-caption text-grey font-weight-medium">CREATED</div>
              <div class="text-body-2">{{ formatDate(selectedAddress.created_at) }}</div>
            </v-col>
            <v-col class="col-6">
              <div class="text-caption text-grey font-weight-medium">LAST UPDATED</div>
              <div class="text-body-2">{{ formatDate(selectedAddress.updated_at) }}</div>
            </v-col>
          </v-row>
        </v-card-text>
        
        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn 
            color="grey" 
            variant="text" 
            @click="showDetailDialog = false"
          >
            Close
          </v-btn>
          <v-btn 
            color="deep-purple" 
            variant="elevated"
            prepend-icon="mdi-pencil"
            @click="editAddress(selectedAddress)"
          >
            Edit Address
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="450">
      <v-card class="rounded-xl">
        <v-card-title class="text-h5 d-flex align-center pa-6">
          <v-icon color="error" class="mr-3" size="28">mdi-delete-alert</v-icon>
          Delete Address
        </v-card-title>
        
        <v-card-text class="pa-6 pt-0">
          <p class="text-body-1">
            Are you sure you want to delete this shipping address? This action cannot be undone.
          </p>
        </v-card-text>
        
        <v-card-actions class="pa-6 pt-0">
          <v-spacer></v-spacer>
          <v-btn 
            color="grey" 
            variant="text" 
            @click="showDeleteDialog = false"
            :disabled="addressStore.loadingDelete"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="elevated"
            prepend-icon="mdi-delete"
            @click="deleteAddress"
            :loading="addressStore.loadingDelete"
          >
            Delete Address
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Success Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :timeout="4000"
      :color="snackbarColor"
      location="top"
      elevation="6"
    >
      <div class="d-flex align-center">
        <v-icon 
          :icon="snackbarColor === 'success' ? 'mdi-check-circle' : 'mdi-alert-circle'" 
          class="mr-2"
        ></v-icon>
        {{ snackbarMessage }}
      </div>
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          size="small"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>

    <!-- Add Address Form Dialog -->
    <AddressForm
      v-model="showAddressForm"
      :editing-address="editingAddress"
      @address-saved="onAddressSaved"
      @address-canceled="onAddressCanceled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useShippingAddressStore } from '~/stores/shipping-address';
import AddressForm from '~/components/addresses/AddressForm.vue';
import type { ShippingAddress, ShippingAddressFilter } from '~/schemas/otoapi/shipping-address';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Shipping Addresses | Otoresell'
});

// Stores
const addressStore = useShippingAddressStore();
const { addresses, totalCount, loading, error } = storeToRefs(addressStore);

// State
const searchQuery = ref('');
const sortBy = ref<ShippingAddressFilter['sort_by']>('created_at');
const sortDirection = ref<ShippingAddressFilter['sort_direction']>('desc');
const perPage = ref(15);
const currentPage = ref(1);

const showAddressForm = ref(false);
const showDetailDialog = ref(false);
const showDeleteDialog = ref(false);
const showSnackbar = ref(false);
const snackbarMessage = ref('');
const snackbarColor = ref('success');

const selectedAddress = ref<ShippingAddress | null>(null);
const editingAddress = ref<ShippingAddress | null>(null);
const addressToDeleteId = ref<number | null>(null);

// Options
const sortOptions = [
  { title: 'Created Date', value: 'created_at' },
  { title: 'Full Name', value: 'shipping_full_name' },
  { title: 'Whatsapp', value: 'shipping_phone' },
  { title: 'Province', value: 'shipping_province' },
  { title: 'City', value: 'shipping_city' },
  { title: 'ID', value: 'id' }
];

const sortDirectionOptions = [
  { title: 'Newest First', value: 'desc' },
  { title: 'Oldest First', value: 'asc' }
];

const perPageOptions = [
  { title: '10', value: 10 },
  { title: '15', value: 15 },
  { title: '25', value: 25 },
  { title: '50', value: 50 }
];

// Computed
const totalPages = computed(() => {
  return Math.ceil(totalCount.value / perPage.value);
});

const activeFilterCount = computed(() => {
  let count = 0;
  if (searchQuery.value.trim()) {
    count++;
  }
  if (sortBy.value !== 'created_at') {
    count++;
  }
  if (sortDirection.value !== 'desc') {
    count++;
  }
  if (perPage.value !== 15) {
    count++;
  }
  return count;
});

// Methods
const loadAddresses = async () => {
  try {
    const params: Partial<ShippingAddressFilter> = {
      page: currentPage.value,
      per_page: perPage.value,
      sort_by: sortBy.value,
      sort_direction: sortDirection.value
    };

    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim();
    }

    await addressStore.fetchAddresses(params);
    
    await nextTick();
    if (import.meta.client) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  } catch (error) {
    console.error('Error loading addresses:', error);
    showNotification('Failed to load addresses', 'error');
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadAddresses();
};

const clearSearch = () => {
  searchQuery.value = '';
};

const handleFilterChange = () => {
  currentPage.value = 1;
  loadAddresses();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadAddresses();
};

const clearFilters = () => {
  searchQuery.value = '';
  sortBy.value = 'created_at';
  sortDirection.value = 'desc';
  perPage.value = 15;
  currentPage.value = 1;
  loadAddresses();
};

const viewAddress = (address: ShippingAddress) => {
  selectedAddress.value = address;
  showDetailDialog.value = true;
};

const editAddress = (address: ShippingAddress) => {
  editingAddress.value = address;
  showAddressForm.value = true;
  showDetailDialog.value = false;
};

const confirmDeleteAddress = (id: number) => {
  addressToDeleteId.value = id;
  showDeleteDialog.value = true;
};

const deleteAddress = async () => {
  if (!addressToDeleteId.value) return;

  try {
    const success = await addressStore.deleteAddress(addressToDeleteId.value);
    if (success) {
      showNotification('Address deleted successfully', 'success');
      showDeleteDialog.value = false;
      
      // Reload if current page is empty
      if (addresses.value.length === 0 && currentPage.value > 1) {
        currentPage.value = currentPage.value - 1;
        loadAddresses();
      }
    } else {
      throw new Error('Failed to delete address');
    }
  } catch (error) {
    console.error('Error deleting address:', error);
    showNotification('Failed to delete address', 'error');
  } finally {
    addressToDeleteId.value = null;
  }
};

const showNotification = (message: string, color: string = 'success') => {
  snackbarMessage.value = message;
  snackbarColor.value = color;
  showSnackbar.value = true;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getEmptyStateTitle = () => {
  return searchQuery.value ? 'No addresses found' : 'No shipping addresses';
};

const getEmptyStateMessage = () => {
  if (searchQuery.value) {
    return `No addresses found matching "${searchQuery.value}". Try adjusting your search terms.`;
  }
  return "No shipping addresses have been added yet. Add your first address to get started.";
};

const onAddressSaved = (result: { success: boolean; message: string; addressId?: number }) => {
  if (result.success) {
    showNotification(result.message, 'success');
    // Reload addresses to show the new/updated address
    loadAddresses();
  } else {
    showNotification(result.message, 'error');
  }
  
  // Reset editing state
  editingAddress.value = null;
  showAddressForm.value = false;
};

const onAddressCanceled = () => {
  editingAddress.value = null;
  showAddressForm.value = false;
};

// Lifecycle
onMounted(async () => {
  await loadAddresses();
});

// Watchers
watch(searchQuery, (newValue) => {
  if (!newValue) {
    handleSearch();
  }
});
</script>

<style scoped>
/* Search Field */
.search-field {
  max-width: 100%;
}

/* Filter Section */

/* Address Cards Grid */
.addresses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

@media (max-width: 768px) {
  .addresses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Address Card */
.address-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.address-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #673AB7, #9C27B0);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.address-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(103, 58, 183, 0.15) !important;
  border-color: rgba(103, 58, 183, 0.2);
}

.address-card:hover::before {
  transform: scaleX(1);
}

/* Address Sections */
.address-section {
  background: rgba(103, 58, 183, 0.02);
  border-radius: 8px;
  padding: 16px;
  border-left: 3px solid #673AB7;
}

/* Location Grid */
.location-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 16px;
}

.location-item {
  text-align: center;
}

/* User Info */
.user-info {
  display: flex;
  justify-content: center;
}

/* Timestamps */
.timestamps {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding-top: 12px;
}

/* Empty State */
.empty-state {
  max-width: 500px;
  margin: 0 auto;
}

.max-width-400 {
  max-width: 400px;
}

/* Enhanced focus states for accessibility */
.address-card:focus-within {
  outline: 2px solid rgba(103, 58, 183, 0.5);
  outline-offset: 2px;
}

/* Button spacing */
.v-card-actions .v-btn {
  margin-left: 8px;
}

.v-card-actions .v-btn:first-child {
  margin-left: 0;
}

/* Loading animation */
.v-progress-circular {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Card animations */
.address-card {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .filters-section {
    padding: 16px;
  }
  
  .filter-group {
    min-width: 120px;
  }
  
  .address-card:hover {
    transform: none;
  }
  
  .location-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease;
}
</style>
