<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="6">
        <v-card class="mx-auto" elevation="8" rounded="lg" max-width="450">
          <v-card-text class="text-center pa-8">
            <v-icon size="64" color="deep-purple-lighten-1" class="mb-4">
              mdi-lock-question
            </v-icon>
            <h1 class="text-h5 font-weight-bold text-deep-purple-darken-3 mb-2">
              Forgot Your Password?
            </h1>
            <p class="text-medium-emphasis mb-6">
              Enter your email address and we will send you a link to reset your password.
            </p>

            <v-form @submit.prevent="handleForgotPassword">
              <v-text-field
                v-model="form.email"
                label="Email"
                type="email"
                required
                :error-messages="emailError"
                variant="outlined"
                prepend-inner-icon="mdi-email-outline"
                class="mb-4"
                hide-details="auto"
                color="purple-darken-3"
              ></v-text-field>

             <NuxtTurnstile v-model="form.turnstileToken" class="mb-4" />

              <v-alert
                v-if="status"
                :type="status.success ? 'success' : 'error'"
                class="mb-4"
                variant="tonal"
                closable
              >
                {{ status.message }}
              </v-alert>

              <v-btn
                type="submit"
                color="deep-purple-darken-3"
                block
                :loading="loading"
                :disabled="!canResend || loading || !form.turnstileToken"
                size="large"
                class="mb-6 py-6"
                elevation="2"
              >
                <span v-if="canResend">Send Reset Link</span>
                <span v-else>Resend again in {{ countdownDisplay }}</span>
              </v-btn>
            </v-form>

            <div class="text-center text-body-2 text-medium-emphasis">
              Remember your password?
              <v-btn
                variant="text"
                color="purple-darken-3"
                size="small"
                class="font-weight-bold"
                @click="navigateTo('/auth/login')"
              >
                Sign In
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { forgotPasswordSchema, type ForgotPasswordForm } from '~/schemas/frontend/reset-password'
import { ZodError } from 'zod/v3'

definePageMeta({
  layout: 'auth',
  middleware: ['guest'],
});

useHead({
  title: 'Forgot Password | Otoresell',
});

const authStore = useAuthStore()
const { forgotPasswordLoading: loading, forgotPasswordStatus: status, forgotPasswordCooldownEndTime, validationErrors } = storeToRefs(authStore)

const form = reactive<ForgotPasswordForm>({
  email: '',
  turnstileToken: '',
})

const emailError = ref('')
const canResend = ref(true)
const countdown = ref(120)
let intervalId: NodeJS.Timeout | null = null

const countdownDisplay = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
  const seconds = countdown.value % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

const startCountdown = (duration: number) => {
  canResend.value = false
  countdown.value = duration
  forgotPasswordCooldownEndTime.value = Date.now() + duration * 1000

  intervalId = setInterval(() => {
    const endTime = forgotPasswordCooldownEndTime.value
    if (!endTime) {
      if (intervalId) clearInterval(intervalId)
      canResend.value = true
      countdown.value = 120
      return
    }

    const now = Date.now()
    const remainingTime = Math.round((endTime - now) / 1000)

    if (remainingTime <= 0) {
      if (intervalId) clearInterval(intervalId)
      canResend.value = true
      forgotPasswordCooldownEndTime.value = null
      countdown.value = 120 // Reset for next time
    } else {
      countdown.value = remainingTime
    }
  }, 1000)
}

onMounted(() => {
  const endTime = forgotPasswordCooldownEndTime.value
  if (endTime) {
    const remainingTime = Math.round((endTime - Date.now()) / 1000)
    if (remainingTime > 0) {
      startCountdown(remainingTime)
    } else {
      forgotPasswordCooldownEndTime.value = null
    }
  }
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})

const validateForm = () => {
  emailError.value = ''
  try {
    forgotPasswordSchema.parse(form)
    return true
  } catch (err) {
    if (err instanceof ZodError) {
      err.errors.forEach((error) => {
        if (error.path.includes('email')) {
          emailError.value = error.message
        }
      })
    }
    return false
  }
}

const handleForgotPassword = async () => {
  if (validateForm()) {
    const success = await authStore.forgotPassword(form)
    if (success) {
      startCountdown(120) // 2-minute cooldown
    }
  }
}

// Watch for backend validation errors and populate local error refs
watch(validationErrors, (newErrors) => {
  emailError.value = newErrors?.email?.[0] || ''
}, { deep: true });

// Clear validation error when user starts typing
watch(() => form.email, () => {
  if (emailError.value) emailError.value = ''
});
</script>