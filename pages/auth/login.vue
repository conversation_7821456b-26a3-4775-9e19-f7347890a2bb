<template>
  <div class="login-page">
    <v-container fluid class="fill-height pa-0">
      <v-row class="fill-height ma-0">
        <!-- Brand Section (Left on desktop, Top on mobile) -->
        <v-col
          cols="12"
          md="6"
          class="brand-section d-flex flex-column justify-center align-center pa-8"
        >
          <div class="text-center brand-content">
            <client-only>
              <v-img
                :src="otoresellLogoPath"
                alt="OTORESELL Logo"
                :width="logoWidth"
                :height="logoHeight"
                class="mx-auto mb-4"
                contain
              />
            </client-only>
            <p class="text-subtitle-1 text-medium-emphasis max-width-sm">
              Reseller Platform for Zafrada
            </p>
          </div>
        </v-col>
        <!-- Login Form Section (Right on desktop, Bottom on mobile) -->
        <v-col
          cols="12"
          md="6"
          class="form-section d-flex align-center justify-center pa-4"
        >
          <v-card
            class="login-card mx-auto"
            max-width="450"
            width="100%"
            elevation="8"
            rounded="lg"
          >
            <v-card-text class="pa-8">
              <h2 class="text-h5 font-weight-bold mb-6 text-center text-purple-darken-4">
                Welcome Back
              </h2>
              <v-form @submit.prevent="handleLogin" class="login-form">
                <v-text-field
                  v-model="form.email"
                  label="Email"
                  type="email"
                  required
                  :error-messages="emailError"
                  variant="outlined"
                  prepend-inner-icon="mdi-email"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                ></v-text-field>
                <v-text-field
                  v-model="form.password"
                  label="Password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  :error-messages="passwordError"
                  variant="outlined"
                  prepend-inner-icon="mdi-lock"
                  :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                  @click:append-inner="showPassword = !showPassword"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                ></v-text-field>
                <div class="d-flex justify-space-between align-center mb-6">
                  <v-checkbox
                    v-model="form.rememberMe"
                    label="Remember me"
                    hide-details
                    density="compact"
                    color="purple-darken-3"
                  ></v-checkbox>
                  <v-btn
                    variant="text"
                    color="purple-darken-3"
                    size="small"
                    class="text-caption"
                    @click="forgotPassword"
                  >
                    Forgot Password?
                  </v-btn>
                </div>

                <NuxtTurnstile v-model="form.turnstileToken" class="mb-4" />
                
                <v-alert
                    v-if="error && !unverifiedEmail"
                    type="error"
                    class="mb-5"
                    density="compact"
                    variant="tonal"
                    closable
                  >
                    {{ error }}
                  </v-alert>

                  <v-alert
                    v-if="unverifiedEmail"
                    type="warning"
                    class="mb-5"
                    density="compact"
                    variant="tonal"
                    icon="mdi-email-alert-outline"
                  >
                    <div>{{ error }}</div>
                    <v-btn
                      v-if="!resendStatus?.success"
                      variant="tonal"
                      color="purple-darken-3"
                      size="small"
                      @click="handleResendVerification"
                      :loading="resendLoading"
                      class="mt-2"
                    >
                      Resend Verification Email
                    </v-btn>
                    <div v-if="resendStatus"
                        :class="resendStatus.success ? 'text-green-darken-2' : 'text-red-darken-2'"
                        class="mt-2 font-weight-bold">
                      {{ resendStatus.message }}
                    </div>
                  </v-alert>
                <v-btn
                  type="submit"
                  color="purple-darken-3"
                  block
                  :loading="loading"
                  :disabled="!form.turnstileToken"
                  size="large"
                  class="mb-6 py-6"
                  elevation="2"
                >
                  <span class="font-weight-bold">Login</span>
                </v-btn>
                <div class="text-center text-body-2 text-medium-emphasis">
                  Don't have an account?
                  <v-btn
                    variant="text"
                    color="purple-darken-3"
                    size="small"
                    class="font-weight-bold"
                    @click="handleSignUp"
                  >
                    Sign Up
                  </v-btn>
                </div>
                <div class="d-flex justify-space-between align-center text-body-2 text-medium-emphasis mt-5">
                  <span>&copy; {{ (new Date()).getFullYear() }} - Otoresell | Zafrada</span>
                  <span>v{{ version }}</span>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, reactive } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'
import { loginSchema, type Login } from '~/schemas/frontend/login'
import { ZodError } from 'zod/v3'

// Define page metadata
definePageMeta({
  layout: 'auth',
  middleware: ['guest']
});

useHead({
  title: 'Login | Otoresell'
});

const config = useRuntimeConfig()
const version = config.public.app.version

// Store references
const authStore = useAuthStore()
const dashboardStore = useDashboardLayoutStore()
const { error, unverifiedEmail, resendStatus, resendLoading, loading, isAuthenticated } = storeToRefs(authStore)

// Form state
const form = reactive<Login>({
  email: '',
  password: '',
  rememberMe: false,
  turnstileToken: '',
})
const showPassword = ref(false)
const emailError = ref('')
const passwordError = ref('')

// Screen size state
const isSmallScreen = ref(false)


// Function to update screen size
const updateScreenSize = () => {
  if (import.meta.client) {
    isSmallScreen.value = window.innerWidth < 600
  }
}

// Responsive logo sizing based on screen size state
const logoWidth = computed(() => isSmallScreen.value ? 200 : 400)
const logoHeight = computed(() => isSmallScreen.value ? 50 : 60)

// Get otoresellLogoPath from dashboard store
const { otoresellLogoPath } = storeToRefs(dashboardStore)

const validateForm = () => {
  emailError.value = ''
  passwordError.value = ''

  try {
    loginSchema.parse(form)
    return true
  } catch (err) {
    if (err instanceof ZodError) {
      err.errors.forEach((error) => {
        if (error.path.includes('email')) {
          emailError.value = error.message
        } else if (error.path.includes('password')) {
          passwordError.value = error.message
        }
      })
    }
    return false
  }
}

const handleLogin = async () => {
  if (validateForm()) {
    await authStore.login(form)
    
    if (isAuthenticated.value) {
      navigateTo('/')
    }
  }
}

const handleResendVerification = async () => {
  if (form.email) {
    const token = form.turnstileToken;
    if (token) {
      await authStore.resendVerificationEmail(form.email, token);
    }
  }
}

const forgotPassword = () => {
  // Implement forgot password functionality
  navigateTo('/auth/forgot-password')
}

const handleSignUp = () => {
authStore.clearAuthErrors()
navigateTo('/auth/register')
}

// Update screen size on client side only
onMounted(() => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
  
  // Check if there's a remembered email
  const rememberedEmail = authStore.getRememberedEmail()
  if (rememberedEmail) {
    form.email = rememberedEmail
    form.rememberMe = true
  }
  
  // No need to check auth here - the guest middleware handles this
})

// Clean up event listener
onBeforeUnmount(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', updateScreenSize)
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-section {
  position: relative;
  z-index: 1;
}

.brand-content {
  max-width: 600px;
}

.max-width-sm {
  max-width: 400px;
  margin: 0 auto;
}

.form-section {
  position: relative;
  z-index: 2;
}

.login-card {
  border-radius: 16px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

@media (max-width: 960px) {
  .brand-section {
    padding: 3rem 1rem !important;
  }
  
  .login-card {
    margin-top: -2rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 600px) {
  .brand-section {
    padding: 2rem 1rem !important;
  }
  
  .login-card {
    margin-top: -1rem;
  }
  
  .login-card .pa-8 {
    padding: 1.5rem !important;
  }
}
</style>
