<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="10" md="8" lg="6">
        <v-card class="mx-auto" elevation="2" rounded="xl">
          <v-card-text class="text-center pa-8 pa-md-12">
            <v-avatar color="deep-purple-lighten-5" size="90" class="mb-5">
              <v-icon size="45" color="deep-purple">
                mdi-account-clock-outline
              </v-icon>
            </v-avatar>
            <h1 class="text-h4 font-weight-bold text-grey-darken-4 mb-3">
              Just One More Step!
            </h1>
            <p class="text-body-1 text-medium-emphasis mb-6 mx-auto" style="max-width: 500px;">
              Your account is ready, but needs to be activated by our team to ensure the security of our community. Please contact Customer Service to get started.
            </p>

            <v-divider class="my-8"></v-divider>

            <h2 class="text-h5 font-weight-bold text-grey-darken-4 mb-6">
              Contact Customer Service
            </h2>

            <!-- Loading State -->
            <div v-if="loading" class="text-center my-8">
              <v-progress-circular indeterminate color="deep-purple" size="48" width="4"></v-progress-circular>
              <p class="mt-4 text-body-1 text-grey-darken-1">Loading contact information...</p>
            </div>

            <!-- Error State -->
            <v-alert v-if="error" type="error" variant="tonal" class="mb-4 text-left" border="start" prominent>
              We couldn't load contact information at the moment. Please try again later or check our main website.
            </v-alert>

            <!-- Contact Cards -->
            <div v-if="!loading && !error && Object.keys(groupedContacts).length > 0" class="contacts-grid">
              <div v-for="(contacts, category) in groupedContacts" :key="category" class="category-group">
                <h3 class="text-subtitle-1 font-weight-bold text-left mb-3 text-grey-darken-3">{{ formatCategory(category) }}</h3>
                <v-card
                  v-for="contact in contacts"
                  :key="contact.id"
                  class="contact-card text-left rounded-lg"
                  elevation="1"
                  border
                  :href="getContactLink(contact)"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <v-card-item>
                    <div class="d-flex align-center">
                      <v-avatar color="deep-purple-lighten-5" size="40" class="mr-4">
                        <v-icon :icon="getIcon(contact.type)" color="deep-purple"></v-icon>
                      </v-avatar>
                      <div class="flex-grow-1">
                        <div class="text-subtitle-1 font-weight-bold text-grey-darken-4">{{ contact.name }}</div>
                        <div class="text-body-2 text-medium-emphasis">{{ contact.value }}</div>
                      </div>
                      <v-icon color="grey-lighten-1">mdi-open-in-new</v-icon>
                    </div>
                  </v-card-item>
                </v-card>
              </div>
            </div>
            
            <!-- Empty State -->
            <div v-if="!loading && !error && Object.keys(groupedContacts).length === 0" class="text-center my-12">
              <v-avatar size="80" color="grey-lighten-4" class="mb-4">
                <v-icon size="40" color="grey-lighten-2">mdi-card-account-phone-outline</v-icon>
              </v-avatar>
              <p class="text-h6 text-grey-darken-2">No contact information available</p>
              <p class="text-body-1 text-medium-emphasis mt-2">Please check back later.</p>
            </div>

            <v-btn
              color="deep-purple"
              variant="text"
              class="mt-10"
              @click="navigateTo('/auth/login')"
              prepend-icon="mdi-arrow-left"
              size="large"
            >
              Back to Login
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

definePageMeta({
  layout: 'auth',
  middleware: ['guest'],
});

useHead({
  title: 'Account Pending Activation | Otoresell',
});

interface Contact {
  id: number;
  name: string;
  category: string;
  type: string;
  value: string;
  is_active: boolean;
}

interface ApiResponse {
  data: Contact[];
}

const { data: contactsData, pending: loading, error } = useFetch<ApiResponse>('/api/auth/support-contacts', {
  query: {
    category: 'activation',
    type: 'whatsapp',
    limit: 5,
  },
  lazy: true,
  server: false,
});

const groupedContacts = computed(() => {
  if (!contactsData.value?.data) {
    return {};
  }

  return contactsData.value.data.reduce((acc, contact) => {
    if (!acc[contact.category]) {
      acc[contact.category] = [];
    }
    acc[contact.category].push(contact);
    return acc;
  }, {} as Record<string, Contact[]>);
});

const formatCategory = (category: string) => {
  return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getIcon = (type: string) => {
  const icons: Record<string, string> = {
    whatsapp: 'mdi-whatsapp',
    email: 'mdi-email-outline',
    phone: 'mdi-phone-outline',
    telegram: 'mdi-telegram',
  };
  return icons[type] || 'mdi-account-box-outline';
};

const getContactLink = (contact: Contact) => {
  switch (contact.type) {
    case 'whatsapp':
      // Removes non-numeric characters
      const phone = contact.value.replace(/\D/g, '');
      return `https://wa.me/${phone}`;
    case 'email':
      return `mailto:${contact.value}`;
    case 'phone':
      return `tel:${contact.value}`;
    case 'telegram':
      // Assumes value is a username
      return `https://t.me/${contact.value}`;
    default:
      return '#';
  }
};
</script>

<style scoped>
.contacts-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr;
}

@media (min-width: 600px) {
  .contacts-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

.category-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-card {
  transition: all 0.2s ease-in-out;
  border: 1px solid rgba(0,0,0,0.1) !important;
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px rgba(103, 58, 183, 0.1) !important;
  border-color: rgba(103, 58, 183, 0.3) !important;
}

.contact-card:hover .v-icon:last-child {
  color: #673AB7 !important; /* deep-purple */
  transform: scale(1.1);
}

.v-icon:last-child {
  transition: all 0.2s ease-in-out;
}
</style>