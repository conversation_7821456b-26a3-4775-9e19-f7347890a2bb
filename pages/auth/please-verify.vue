<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="mx-auto" elevation="8" rounded="lg">
          <v-card-text class="text-center pa-8">
            <v-icon size="64" color="deep-purple-lighten-1" class="mb-4">
              mdi-email-fast-outline
            </v-icon>
            <h1 class="text-h5 font-weight-bold text-deep-purple-darken-3 mb-2">
              Please Verify Your Email
            </h1>
            <p class="text-medium-emphasis mb-6">
              A verification link has been sent to your email address. Please
              check your inbox and follow the link to activate your account.
            </p>

            <v-form @submit.prevent="resend">
              <v-text-field
                v-model="email"
                label="Email"
                variant="outlined"
                prepend-inner-icon="mdi-email-outline"
                :rules="[() => !!email || 'Email is required']"
                required
              />

             <NuxtTurnstile v-model="turnstileToken" class="mb-4" />

              <v-alert
                v-if="message"
                :type="alertType"
                class="mb-4"
                variant="tonal"
              >
                {{ message }}
              </v-alert>

              <v-btn
                type="submit"
                color="deep-purple-darken-3"
                block
                :loading="authStore.resendLoading"
                :disabled="!canResend || authStore.resendLoading || !turnstileToken"
              >
                <span v-if="canResend">Resend Verification Email</span>
                <span v-else>Resend again in {{ countdownDisplay }}</span>
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ['guest'],
  layout: 'auth',
})

const authStore = useAuthStore()
const email = ref('')
const turnstileToken = ref('')
const message = ref<string | null>(null)
const alertType = ref<'success' | 'error'>('success')
const canResend = ref(true)
const countdown = ref(60)
let intervalId: NodeJS.Timeout | null = null

const countdownDisplay = computed(() => {
  const minutes = Math.floor(countdown.value / 60)
  const seconds = countdown.value % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

function startCountdown(duration: number) {
  canResend.value = false
  countdown.value = duration
  authStore.setResendCooldown(duration)

  intervalId = setInterval(() => {
    const endTime = authStore.resendCooldownEndTime
    if (!endTime) {
      if (intervalId) clearInterval(intervalId)
      canResend.value = true
      countdown.value = 60
      return
    }

    const now = Date.now()
    const remainingTime = Math.round((endTime - now) / 1000)

    if (remainingTime <= 0) {
      if (intervalId) clearInterval(intervalId)
      canResend.value = true
      authStore.clearResendCooldown()
      countdown.value = 60 // Reset for next time
    } else {
      countdown.value = remainingTime
    }
  }, 1000)
}

onMounted(() => {
  // Set email value on client-side only to prevent hydration mismatch
  email.value = authStore.emailForVerification || ''

  const endTime = authStore.resendCooldownEndTime
  if (endTime) {
    const remainingTime = Math.round((endTime - Date.now()) / 1000)
    if (remainingTime > 0) {
      startCountdown(remainingTime)
    } else {
      authStore.clearResendCooldown()
    }
  } else {
    // Start a 60-second countdown on initial page load
    startCountdown(60)
  }
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})

async function resend() {
  message.value = null

  if (!email.value) {
    message.value = 'Please enter your email address.'
    alertType.value = 'error'
    return
  }

  await authStore.resendVerificationEmail(email.value, turnstileToken.value)
  turnstileToken.value = ''

  if (authStore.resendStatus) {
    message.value = authStore.resendStatus.message
    alertType.value = authStore.resendStatus.success ? 'success' : 'error'

    if (authStore.resendStatus.success) {
      startCountdown(300) // 5-minute cooldown on manual resend
    }
  }
}
</script>
