<template>
  <div class="register-page">
    <v-container fluid class="fill-height pa-0">
      <v-row class="fill-height ma-0">
        <!-- Brand Section (Left on desktop, Top on mobile) -->
        <v-col
          cols="12"
          md="6"
          class="brand-section d-flex flex-column justify-center align-center pa-8"
        >
          <div class="text-center brand-content">
            <client-only>
              <v-img
                :src="otoresellLogoPath"
                alt="OTORESELL Logo"
                :width="logoWidth"
                :height="logoHeight"
                class="mx-auto mb-4"
                contain
              />
            </client-only>
            <p class="text-subtitle-1 text-medium-emphasis max-width-sm">
              Reseller Platform for Zafrada
            </p>
          </div>
        </v-col>
        <!-- Register Form Section (Right on desktop, Bottom on mobile) -->
        <v-col
          cols="12"
          md="6"
          class="form-section d-flex align-center justify-center pa-4"
        >
          <v-card
            class="register-card mx-auto"
            max-width="450"
            width="100%"
            elevation="8"
            rounded="lg"
          >
            <v-card-text class="pa-8">
              <h2 class="text-h5 font-weight-bold mb-6 text-center text-purple-darken-4">
                Create an Account
              </h2>
              <v-form @submit.prevent="handleRegister" class="register-form">
                <v-text-field
                  v-model="form.name"
                  label="Full Name"
                  type="text"
                  required
                  :error-messages="nameError"
                  variant="outlined"
                  prepend-inner-icon="mdi-account"
                  :disabled="authStore.loading"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                ></v-text-field>
                <v-text-field
                  v-model="form.phone"
                  label="No. Whatsapp"
                  type="text"
                  required
                  :error-messages="phoneError"
                  variant="outlined"
                  prepend-inner-icon="mdi-whatsapp"
                  :disabled="authStore.loading"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                ></v-text-field>
                <v-text-field
                  v-model="form.email"
                  label="Email"
                  type="email"
                  required
                  :error-messages="emailError"
                  variant="outlined"
                  prepend-inner-icon="mdi-email"
                  :disabled="authStore.loading"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                  autocomplete="off"
                ></v-text-field>
                <v-text-field
                  v-model="form.password"
                  label="Password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  :error-messages="passwordError"
                  variant="outlined"
                  prepend-inner-icon="mdi-lock"
                  :disabled="authStore.loading"
                  :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                  @click:append-inner="showPassword = !showPassword"
                  class="mb-1"
                  hide-details="auto"
                  color="purple-darken-3"
                  autocomplete="new-password"
                ></v-text-field>
                <GlobalPasswordRequirements :password="form.password" class="mb-4" />
                <v-text-field
                  v-model="form.password_confirmation"
                  label="Confirm Password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  :error-messages="passwordConfirmationError"
                  variant="outlined"
                  prepend-inner-icon="mdi-lock-check"
                  :disabled="authStore.loading"
                  class="mb-4"
                  hide-details="auto"
                  color="purple-darken-3"
                  autocomplete="new-password"
                ></v-text-field>

               <NuxtTurnstile v-model="form.turnstileToken" class="mb-4" />

               <v-alert
                   v-if="authStore.error"
                   :type="Object.keys(authStore.validationErrors).length > 0 ? 'warning' : 'error'"
                    class="mb-5"
                    density="compact"
                    variant="tonal"
                    closable
                  >
                    <template v-if="Object.keys(authStore.validationErrors).length > 0">
                      Please correct the errors above.
                    </template>
                    <template v-else>
                      {{ authStore.error }}
                    </template>
                  </v-alert>
                <v-btn
                  type="submit"
                  color="purple-darken-3"
                  block
                  :loading="authStore.loading"
                  :disabled="!form.turnstileToken"
                  size="large"
                  class="mb-6 py-6"
                  elevation="2"
                >
                  <span class="font-weight-bold">Register</span>
                </v-btn>
                <div class="text-center text-body-2 text-medium-emphasis">
                  Already have an account?
                  <v-btn
                    variant="text"
                    color="purple-darken-3"
                    size="small"
                    class="font-weight-bold"
                    @click="handleSignIn"
                  >
                    Sign In
                  </v-btn>
                </div>
                <div class="d-flex justify-space-between align-center text-body-2 text-medium-emphasis mt-5">
                  <span>&copy; {{ (new Date()).getFullYear() }} - Otoresell | Zafrada</span>
                  <span>v{{ version }}</span>
                </div>
              </v-form>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import GlobalPasswordRequirements from '~/components/global/PasswordRequirements.vue'
import { useAuthStore } from '~/stores/auth'
import { useDashboardLayoutStore } from '~/stores/dashboard-layout'
import { storeToRefs } from 'pinia'
import { registerSchema, baseRegisterSchema, type RegisterForm } from '~/schemas/frontend/register'

// Define page metadata
definePageMeta({
  layout: 'auth',
  middleware: ['guest']
});

useHead({
  title: 'Register | Otoresell'
});

const config = useRuntimeConfig()
const version = config.public.app.version

// Store references
const authStore = useAuthStore()
const dashboardStore = useDashboardLayoutStore()

// Form state
const form = ref<RegisterForm>({
  name: '',
  phone: '',
  email: '',
  password: '',
  password_confirmation: '',
  turnstileToken: '',
})
const showPassword = ref(false)
const nameError = ref('')
const phoneError = ref('')
const emailError = ref('')
const passwordError = ref('')
const passwordConfirmationError = ref('')

// Screen size state
const isSmallScreen = ref(false)

// Function to update screen size
const updateScreenSize = () => {
  if (import.meta.client) {
    isSmallScreen.value = window.innerWidth < 600
  }
}

// Responsive logo sizing based on screen size state
const logoWidth = computed(() => isSmallScreen.value ? 200 : 400)
const logoHeight = computed(() => isSmallScreen.value ? 50 : 60)

// Get otoresellLogoPath from dashboard store
const { otoresellLogoPath } = storeToRefs(dashboardStore)


const validateForm = () => {
  nameError.value = ''
  phoneError.value = ''
  emailError.value = ''
  passwordError.value = ''
  passwordConfirmationError.value = ''

  const result = registerSchema.safeParse(form.value)

  if (!result.success) {
    result.error.errors.forEach(error => {
      if (error.path.includes('name')) {
        nameError.value = error.message
      }
      if (error.path.includes('phone')) {
        phoneError.value = error.message
      }
      if (error.path.includes('email')) {
        emailError.value = error.message
      }
      if (error.path.includes('password')) {
        passwordError.value = error.message
      }
      if (error.path.includes('password_confirmation')) {
        passwordConfirmationError.value = error.message
      }
    })
  }

  return result.success
}

const handleRegister = async () => {
  if (validateForm()) {
    const success = await authStore.register(form.value);
    if (success) {
      await navigateTo('/auth/please-verify');
    }
  }
};

const handleSignIn = () => {
  authStore.clearAuthErrors()
  navigateTo('/auth/login')
}

// Watch for backend validation errors and populate local error refs
watch(() => authStore.validationErrors, (newErrors) => {
  nameError.value = newErrors?.name?.[0] || ''
  phoneError.value = newErrors?.phone?.[0] || ''
  emailError.value = newErrors?.email?.[0] || ''
  passwordError.value = newErrors?.password?.[0] || ''
}, { deep: true });

// Clear validation error when user starts typing
watch(() => form.value.name, () => {
  if (nameError.value) nameError.value = ''
});
watch(() => form.value.phone, () => {
  if (phoneError.value) phoneError.value = ''
});
watch(() => form.value.email, () => {
  if (emailError.value) emailError.value = ''
});

watch(() => form.value.password, (newPassword) => {
  if (passwordError.value) passwordError.value = ''
  const result = baseRegisterSchema.shape.password.safeParse(newPassword);
  if (result.success) {
    passwordError.value = '';
  } else {
    passwordError.value = result.error.errors[0].message;
  }

  // Also validate confirmation if it has a value
  if (form.value.password_confirmation) {
    const confirmResult = registerSchema.safeParse(form.value);
    const confirmError = confirmResult.success
      ? undefined
      : confirmResult.error.errors.find(e => e.path.includes('password_confirmation'));
      
    passwordConfirmationError.value = confirmError ? confirmError.message : '';
  }
});

watch(() => form.value.password_confirmation, () => {
  const result = registerSchema.safeParse(form.value);
  const confirmError = result.success
    ? undefined
    : result.error.errors.find(e => e.path.includes('password_confirmation'));

  passwordConfirmationError.value = confirmError ? confirmError.message : '';
});

// Update screen size on client side only
onMounted(() => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
})

// Clean up event listener
onBeforeUnmount(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', updateScreenSize)
  }
})
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-section {
  position: relative;
  z-index: 1;
}

.brand-content {
  max-width: 600px;
}

.max-width-sm {
  max-width: 400px;
  margin: 0 auto;
}

.form-section {
  position: relative;
  z-index: 2;
}

.register-card {
  border-radius: 16px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.register-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

@media (max-width: 960px) {
  .brand-section {
    padding: 3rem 1rem !important;
  }
  
  .register-card {
    margin-top: -2rem;
    margin-bottom: 2rem;
  }
}

@media (max-width: 600px) {
  .brand-section {
    padding: 2rem 1rem !important;
  }
  
  .register-card {
    margin-top: -1rem;
  }
  
  .register-card .pa-8 {
    padding: 1.5rem !important;
  }
}
</style>