<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="6">
        <v-card class="mx-auto" elevation="8" rounded="lg" max-width="450">
          <v-card-text class="text-center pa-8">
            <v-icon size="64" color="deep-purple-lighten-1" class="mb-4">
              mdi-lock-reset
            </v-icon>
            <h1 class="text-h5 font-weight-bold text-deep-purple-darken-3 mb-2">
              Reset Your Password
            </h1>
            <p class="text-medium-emphasis mb-6">
              Please enter your new password below.
            </p>

            <v-form @submit.prevent="handleResetPassword">
              <v-text-field
                v-model="form.email"
                label="Email"
                type="email"
                required
                :error-messages="emailError"
                variant="outlined"
                prepend-inner-icon="mdi-email-outline"
                class="mb-4"
                hide-details="auto"
                color="purple-darken-3"
                disabled
              ></v-text-field>

              <v-text-field
                v-model="form.password"
                label="New Password"
                :type="showPassword ? 'text' : 'password'"
                required
                :error-messages="passwordError"
                variant="outlined"
                prepend-inner-icon="mdi-lock"
                :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                @click:append-inner="showPassword = !showPassword"
                class="mb-1"
                hide-details="auto"
                color="purple-darken-3"
              ></v-text-field>
              <div class="text-left">
                <GlobalPasswordRequirements :password="form.password" class="mb-4" />
              </div>

              <v-text-field
                v-model="form.password_confirmation"
                label="Confirm New Password"
                :type="showPassword ? 'text' : 'password'"
                required
                :error-messages="passwordConfirmationError"
                variant="outlined"
                prepend-inner-icon="mdi-lock-check"
                class="mb-4"
                hide-details="auto"
                color="purple-darken-3"
              ></v-text-field>

             <NuxtTurnstile v-model="form.turnstileToken" class="mb-4" />

              <v-alert
                v-if="status"
                :type="status.success ? 'success' : 'error'"
                class="mb-4"
                variant="tonal"
                closable
              >
                {{ status.message }}
              </v-alert>

              <v-btn
                type="submit"
                color="deep-purple-darken-3"
                block
                :loading="loading"
                :disabled="!form.turnstileToken"
                size="large"
                class="mb-6 py-6"
                elevation="2"
              >
                <span class="font-weight-bold">Reset Password</span>
              </v-btn>
            </v-form>
            <div v-if="status && status.success" class="text-center">
                <v-btn
                    variant="text"
                    color="purple-darken-3"
                    size="small"
                    class="font-weight-bold"
                    @click="navigateTo('/auth/login')"
                >
                    Proceed to Login
                </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'
import { resetPasswordSchema, type ResetPasswordForm } from '~/schemas/frontend/reset-password'
import { ZodError } from 'zod/v3'
import GlobalPasswordRequirements from '~/components/global/PasswordRequirements.vue'

definePageMeta({
  layout: 'auth',
  middleware: ['guest'],
});

useHead({
  title: 'Reset Password | Otoresell',
});

const authStore = useAuthStore()
const { resetPasswordLoading: loading, resetPasswordStatus: status, validationErrors } = storeToRefs(authStore)
const route = useRoute()

const form = reactive<ResetPasswordForm>({
  token: '',
  email: '',
  password: '',
  password_confirmation: '',
  turnstileToken: '',
})

const showPassword = ref(false)
const emailError = ref('')
const passwordError = ref('')
const passwordConfirmationError = ref('')

onMounted(() => {
  form.token = route.query.token as string || ''
  form.email = route.query.email as string || ''
  if (!form.token || !form.email) {
    status.value = { success: false, message: 'Invalid password reset link.' }
  }
})

const validateForm = () => {
  emailError.value = ''
  passwordError.value = ''
  passwordConfirmationError.value = ''
  try {
    resetPasswordSchema.parse(form)
    return true
  } catch (err) {
    if (err instanceof ZodError) {
      err.errors.forEach((error) => {
        if (error.path.includes('email')) {
          emailError.value = error.message
        } else if (error.path.includes('password')) {
          passwordError.value = error.message
        } else if (error.path.includes('password_confirmation')) {
          passwordConfirmationError.value = error.message
        }
      })
    }
    return false
  }
}

const handleResetPassword = async () => {
  if (validateForm()) {
    await authStore.resetPassword(form)
  }
}

// Watch for backend validation errors and populate local error refs
watch(validationErrors, (newErrors) => {
  emailError.value = newErrors?.email?.[0] || ''
  passwordError.value = newErrors?.password?.[0] || ''
  passwordConfirmationError.value = newErrors?.password_confirmation?.[0] || ''
}, { deep: true });

// Clear validation error when user starts typing
watch(() => form.password, () => {
  if (passwordError.value) passwordError.value = ''
});

watch(() => form.password_confirmation, () => {
  if (passwordConfirmationError.value) passwordConfirmationError.value = ''
});
</script>