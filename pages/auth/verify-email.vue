<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="mx-auto" elevation="8" rounded="lg">
          <v-card-text class="text-center pa-8">
            <!-- Loading State -->
            <div v-if="status === 'pending'">
              <v-icon size="64" color="deep-purple-lighten-1" class="mb-4">
                mdi-email-sync-outline
              </v-icon>
              <h1 class="text-h5 font-weight-bold text-deep-purple-darken-3 mb-2">
                Verifying Your Email
              </h1>
              <p class="text-medium-emphasis mb-6">
                This may take a moment while we set up your account.
              </p>
              <v-progress-linear
                indeterminate
                color="deep-purple-lighten-1"
                rounded
              ></v-progress-linear>
            </div>

            <!-- Error State -->
            <div v-else-if="status === 'error'">
              <v-icon size="64" color="error" class="mb-4">
                mdi-alert-circle-outline
              </v-icon>
              <h1 class="text-h5 font-weight-bold text-error mb-2">
                Verification Failed
              </h1>
              <p class="text-medium-emphasis mb-4">
                {{ authStore.error }}
              </p>
              <v-btn
                color="deep-purple-darken-3"
                @click="navigateTo('/auth/please-verify')"
              >
                Resend Verification Link
              </v-btn>
            </div>

            <!-- Success State -->
            <div v-else-if="status === 'success'">
              <v-icon size="64" color="success" class="mb-4">
                mdi-check-decagram-outline
              </v-icon>
              <h1 class="text-h5 font-weight-bold text-success mb-2">
                Verification Successful!
              </h1>
              <p class="text-medium-emphasis">
                Redirecting you to the account activation page...
              </p>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: ['guest'],
  layout: 'auth',
})

const authStore = useAuthStore()
const route = useRoute()
const status = ref<'pending' | 'success' | 'error'>('pending')

onMounted(async () => {
  const verifyUrl = route.query.verify_url

  if (typeof verifyUrl !== 'string' || !verifyUrl) {
    authStore.error = 'Invalid verification link.'
    status.value = 'error'
    return
  }

  await authStore.verifyEmail(verifyUrl)

  if (authStore.error) {
    status.value = 'error'
  } else {
    status.value = 'success'
    setTimeout(() => {
      navigateTo('/auth/pending-activation')
    }, 2000)
  }
})
</script>