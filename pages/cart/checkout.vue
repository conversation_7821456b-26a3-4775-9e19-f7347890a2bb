<template>
  <div>
    <div class="d-flex flex-wrap align-center mb-4">
      <v-btn 
        color="deep-purple" 
        variant="text" 
        to="/cart" 
        prepend-icon="mdi-arrow-left" 
        class="px-1 py-1"
        density="comfortable"
      >
        Back to Cart
      </v-btn>
    </div>
    <!-- Page Title -->
    <PageTitle 
      title="Checkout" 
      icon="mdi-cart-check" 
      subtitle="Complete your purchase by providing shipping and payment details."
    />
    

    <v-alert v-if="!hasValidItems" type="warning" class="mb-4">
      <div v-if="selectedItems.length === 0">
        No items selected for checkout. Please return to cart and select items.
      </div>
      <div v-else>
        Some cart items appear to be invalid. Please return to cart to review your items.
      </div>
      <v-btn 
        color="deep-purple" 
        variant="outlined" 
        class="mt-2" 
        to="/cart"
      >
        Return to Cart
      </v-btn>
    </v-alert>
    <template v-else>
      <v-row>
        <!-- Left Column: Shipping & Payment -->
        <v-col cols="12" md="8">
          <!-- Order Items Section -->
          <v-card class="mb-4">
            <v-card-title
              class="py-3 px-4"
              :class="selectedItems.length > 0 ? 'bg-deep-purple-lighten-5' : 'bg-grey-lighten-4'"
            >
              <v-icon class="mr-2" :color="selectedItems.length > 0 ? 'deep-purple' : 'grey-darken-1'">mdi-package-variant</v-icon>
              <span :class="selectedItems.length > 0 ? 'text-deep-purple-darken-2' : ''">Order Items</span>
            </v-card-title>
            
            <v-list>
              <div v-for="item in itemsForCheckout" :key="item.jubelio_item_id" class="checkout-item">
                <v-list-item>
                  <template v-slot:prepend>
                    <v-img
                      :src="item.image || ''"
                      :alt="item.name"
                      width="60"
                      height="60"
                      cover
                      class="rounded mr-3"
                    ></v-img>
                  </template>
                  
                  <v-list-item-title class="text-subtitle-2 font-weight-medium">
                    <router-link 
                      :to="`/products/${item.jubelio_item_id}`" 
                      class="text-decoration-none text-deep-purple-darken-1 hover-underline"
                    >
                      {{ item.name }}
                    </router-link>
                  </v-list-item-title>

                  <v-list-item-subtitle class="mt-1 text-caption text-grey-darken-1">
                    <span v-if="item.variant && Array.isArray(item.variant)">
                      <span v-for="(variantItem, index) in item.variant" :key="index" class="d-inline">
                        <span>{{ variantItem.value }}</span>
                        <span v-if="index < item.variant.length - 1" class="mx-1">/</span>
                      </span>
                    </span>
                    <span v-if="item.sku" class="ml-2"> (SKU: {{ item.sku }})</span>
                  </v-list-item-subtitle>

                  <template v-slot:append>
                    <div class="text-right" style="min-width: 150px;">
                      <div class="text-body-2 font-weight-medium">
                        <span :class="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0 ? 'text-red' : 'text-deep-purple-darken-2'">
                          {{ formatRupiah(calculateDiscountedPrice(item.price, calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage))) }}
                        </span>
                        <span class="text-grey-darken-1"> x {{ item.quantity }}</span>
                      </div>

                      <div v-if="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0" class="text-caption text-grey text-decoration-line-through">
                        {{ formatRupiah(item.price * item.quantity) }}
                      </div>

                      <div class="text-subtitle-1 font-weight-bold" :class="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0 ? 'text-red' : 'text-grey-darken-3'">
                        {{ formatRupiah(calculateDiscountedPrice(item.price, calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage)) * item.quantity) }}
                      </div>
                    </div>
                  </template>
                </v-list-item>
                <v-divider v-if="itemsForCheckout.indexOf(item) < itemsForCheckout.length - 1"></v-divider>
              </div>
            </v-list>
          </v-card>          

          <!-- Shipping Address Section - UPDATED -->
          <v-card class="mb-4">
            <v-card-title
              class="py-3 px-4 d-flex align-center"
              :class="selectedShippingAddress ? 'bg-deep-purple-lighten-5' : 'bg-grey-lighten-4'"
            >
              <v-icon class="mr-2" :color="selectedShippingAddress ? 'deep-purple' : 'grey-darken-1'">mdi-map-marker</v-icon>
              <span :class="selectedShippingAddress ? 'text-deep-purple-darken-2' : ''">Shipping Address</span>
            </v-card-title>
            
            <v-card-text class="pa-4">
              <ShippingAddressSelector
                v-model="selectedShippingAddress"
                @address-selected="onAddressSelected"
              />
            </v-card-text>
          </v-card>

          <!-- Shipping Type Selection -->
          <v-card v-if="selectedShippingAddress" class="mb-4">
            <v-card-title
              class="py-3 px-4"
              :class="selectedShippingType ? 'bg-deep-purple-darken-2 text-white' : 'bg-grey-lighten-4'"
            >
              <v-icon class="mr-2" :color="selectedShippingType ? 'white' : 'grey-darken-1'">mdi-truck-fast</v-icon>
              <span :class="selectedShippingType ? 'text-white' : ''">Shipping Type</span>
            </v-card-title>
            <v-card-text class="pa-0">
              <v-radio-group v-model="selectedShippingType" class="payment-options">
                <v-list class="pa-2 ma-2 rounded border">
                  <v-list-item
                    v-for="type in shippingTypes"
                    :key="type.value"
                    @click="selectedShippingType = type.value"
                    :class="{ 'selected-method': selectedShippingType === type.value }"
                    class="shipping-method-item pa-3"
                  >
                    <template v-slot:prepend>
                      <v-radio
                        :value="type.value"
                        :model-value="selectedShippingType"
                        color="deep-purple"
                      ></v-radio>
                    </template>
                    <v-list-item-title class="d-flex justify-space-between align-center">
                      <div class="d-flex align-center">
                        <v-icon size="x-large" class="mr-3" :color="selectedShippingType === type.value ? 'deep-purple' : 'grey-darken-1'">
                          {{ type.icon }}
                        </v-icon>
                        <div>
                          <div class="font-weight-medium text-deep-purple-darken-1">{{ type.title }}</div>
                          <div class="text-caption text-grey-darken-1">{{ type.description }}</div>
                        </div>
                      </div>
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-radio-group>
            </v-card-text>
          </v-card>

          <!-- Shipping Method Section -->
          <v-card v-if="selectedShippingAddress" class="mb-4">
            <v-card-title
              class="py-3 px-4"
              :class="selectedShippingMethodNew ? 'bg-deep-purple-lighten-5' : 'bg-grey-lighten-4'"
            >
              <v-icon class="mr-2" :color="selectedShippingMethodNew ? 'deep-purple' : 'grey-darken-1'">mdi-truck-delivery</v-icon>
              <span :class="selectedShippingMethodNew ? 'text-deep-purple-darken-2' : ''">Shipping Method</span>
            </v-card-title>
            <v-card-text class="pa-0">
              <!-- Expedition Selector -->
              <ShippingMethodSelector
                v-if="selectedShippingType === 'expedition'"
                v-model="selectedShippingMethodNew"
                :shipping-address="selectedShippingAddress"
                :cart-items="itemsForCheckout"
                :origin-location="originLocation"
                @method-selected="onShippingMethodSelected"
                @shipping-calculated="onShippingCalculated"
                @auto-selected="onShippingMethodSelected"
              />
              <!-- Online Recipient Selector -->
              <OnlineRecipientSelector
                v-if="selectedShippingType === 'online_recipient'"
                v-model="selectedShippingMethodNew"
                :methods="onlineRecipientShippingMethods"
                @method-selected="onOnlineRecipientMethodSelected"
                @tracking-number-updated="onTrackingNumberUpdated"
              />
              <!-- Store Pickup Selector -->
              <StorePickupSelector
                v-if="selectedShippingType === 'store_pickup'"
                v-model="selectedShippingMethodNew"
                @method-selected="onStorePickupMethodSelected"
              />
              <!-- JNE Expedition Selector -->
              <JneExpeditionSelector
                v-if="selectedShippingType === 'jne_expedition'"
                v-model="selectedShippingMethodNew"
                :shipping-address="selectedShippingAddress"
                :cart-items="itemsForCheckout"
                @method-selected="onShippingMethodSelected"
                @shipping-calculated="onShippingCalculated"
                @auto-selected="onShippingMethodSelected"
              />
            </v-card-text>
          </v-card>
          
          <!-- Payment Method Section -->
          <v-card v-if="selectedShippingAddress && selectedShippingMethodNew" class="mb-4">
            <v-card-title
              class="py-3 px-4"
              :class="selectedPaymentMethod ? 'bg-deep-purple-darken-2 text-white' : 'bg-grey-lighten-4'"
            >
              <v-icon class="mr-2" :color="selectedPaymentMethod ? 'white' : 'grey-darken-1'">mdi-credit-card</v-icon>
              <span :class="selectedPaymentMethod ? 'text-white' : ''">Payment Method</span>
            </v-card-title>
            <v-card-text class="pa-0">
              <v-radio-group v-model="selectedPaymentMethod" class="payment-options">
                <v-list class="pa-2 ma-2 rounded border">
                  <v-list-item
                    v-for="method in paymentMethods"
                    :key="method.id"
                    @click="selectedPaymentMethod = method.id"
                    :class="{ 'selected-method': selectedPaymentMethod === method.id }"
                    class="shipping-method-item pa-3"
                  >
                    <template v-slot:prepend>
                      <v-radio :value="method.id" color="deep-purple"></v-radio>
                    </template>
                    <v-list-item-title class="d-flex justify-space-between align-center">
                      <div class="d-flex align-center">
                        <v-icon size="x-large" class="mr-3" :color="selectedPaymentMethod === method.id ? 'deep-purple' : 'grey-darken-1'">
                          {{ method.icon }}
                        </v-icon>
                        <div>
                          <div class="font-weight-medium text-deep-purple-darken-1">{{ method.name }}</div>
                          <div class="text-caption text-grey-darken-1">{{ method.description }}</div>
                        </div>
                      </div>
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-radio-group>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Right Column: Order Summary - UPDATED -->
        <v-col cols="12" md="4">
          <v-card class="pa-4 order-summary-card">
            <h3 class="text-h6 mb-4">Order Summary</h3>
            
            <div class="d-flex justify-space-between mb-2">
              <span class="text-body-1">Subtotal ({{ itemsForCheckout.length }} items)</span>
              <span class="text-body-1">{{ formatRupiah(subtotal) }}</span>
            </div>

            <div v-if="totalDiscount > 0" class="d-flex justify-space-between mb-2 text-success">
              <span class="text-body-1">Discount</span>
              <span class="text-body-1">-{{ formatRupiah(totalDiscount) }}</span>
            </div>
            
            <div class="d-flex justify-space-between mb-2">
              <span class="text-body-1">Shipping Fee</span>
              <span class="text-body-1">{{ formatRupiah(newShippingFee) }}</span>
            </div>
            
            <div v-if="newInsuranceFee > 0" class="d-flex justify-space-between mb-2">
              <span class="text-body-1">Insurance Fee</span>
              <span class="text-body-1">{{ formatRupiah(newInsuranceFee) }}</span>
            </div>
            
            <v-divider class="my-4"></v-divider>
            
            <div class="d-flex justify-space-between mb-4">
              <span class="text-h6">Total</span>
              <span class="text-h6 font-weight-bold">{{ formatRupiah(newOrderTotal) }}</span>
            </div>
            
            <div class="mt-6 mb-4">
              <v-textarea
                v-model="orderNote"
                label="Order Notes (Optional)"
                hint="Add special instructions for your order"
                rows="2"
                variant="outlined"
                density="compact"
                color="deep-purple"
                counter
                maxlength="200"
              ></v-textarea>
            </div>
            
            <v-btn
              color="deep-purple"
              size="large"
              block
              :disabled="!newCanPlaceOrder || isPlacingOrder"
              :loading="isPlacingOrder"
              @click="placeOrderNew"
              class="mb-2"
            >
              Place Order
            </v-btn>
            
            <div class="mt-4 text-caption text-grey text-center">
              By placing your order, you agree to our 
              <a href="#" class="text-decoration-none">Terms of Service</a> and 
              <a href="#" class="text-decoration-none">Privacy Policy</a>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </template>

    <!-- Success Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :timeout="3000"
      :color="snackbarColor"
      location="top"
    >
      {{ snackbarMessage }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>


<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useCartStore } from '~/stores/cart';
import { useContactStore } from '~/stores/contact';
import { useAuthStore } from '~/stores/auth';
import { useAddressStore } from '~/stores/address';
import { useCheckoutStore } from '~/stores/checkout';
import { useProductDiscountStore } from '~/stores/product-discount';
import { useDiscountCalculator } from '~/composables/useDiscountCalculator';
import { useOrderTransformer } from '~/composables/useOrderTransformer';
import { useJneStore } from '~/stores/jne';
import { logger } from '~/utils/logger';
import type { JnePriceRequest } from '~/schemas/jne';
import type { PaymentMethod } from '~/types/checkout';
import type { ShippingAddress } from '~/schemas/otoapi/shipping-address';
import ShippingAddressSelector from '~/components/checkout/ShippingAddressSelector.vue'
import ShippingMethodSelector from '~/components/checkout/ShippingMethodSelector.vue'
import OnlineRecipientSelector from '~/components/checkout/OnlineRecipientSelector.vue'
import StorePickupSelector from '~/components/checkout/StorePickupSelector.vue'
import JneExpeditionSelector from '~/components/checkout/JneExpeditionSelector.vue'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Checkout | Otoresell'
});

// ==================== COMPOSABLES ====================
const orderTransformer = useOrderTransformer();
const { calculateDiscountedPrice, calculateDiscountPercentage } = useDiscountCalculator();

// ==================== STORES ====================
const cartStore = useCartStore();
const contactStore = useContactStore();
const authStore = useAuthStore();
const addressStore = useAddressStore();
const checkoutStore = useCheckoutStore();
const productDiscountStore = useProductDiscountStore();
const jneStore = useJneStore()
const { selectedShippingType, shippingTypes, onlineRecipientShippingMethods, storePickupShippingMethod } = storeToRefs(checkoutStore)
// const transactionStore = useTransactionStore();

// ==================== STATE ====================
// Location data
const originLocation = ref<any>(null);
const orderNote = ref('');
// Address dialog
const paymentTab = ref('bank_transfer');
const selectedPaymentMethod = ref<string | null>(null);

// Notifications
const showSnackbar = ref(false);
const snackbarMessage = ref('');
const snackbarColor = ref('success');
// Order
const isPlacingOrder = ref(false);


// NEW STATE for new components
const selectedShippingAddress = ref<ShippingAddress | null>(null)
const selectedShippingMethodNew = ref<string | null>(null) // Holds the selected method's ID/code
const selectedShippingMethodData = ref<any>(null) // Holds the full data of the selected method
const newShippingMethods = ref<any[]>([]) // For expedition methods from API
const trackingNumber = ref('')

// ==================== COMPUTED ====================
const hasValidItems = computed(() => {
  return selectedItems.value.length > 0 && selectedItems.value.every(item =>
    item.jubelio_item_id && item.name && item.price > 0
  );
});

const selectedItems = computed(() => cartStore.selectedItems);

const itemsForCheckout = computed(() => {
  return selectedItems.value.map(item => ({
    ...item,
    variant: item.variant || [],
    image: item.image || '',
  }));
});
const bankTransferMethods = computed(() => checkoutStore.bankTransferMethods);
const paymentMethods = computed(() => checkoutStore.paymentMethods);
const subtotal = computed(() => cartStore.selectedTotal);

const discountedSubtotal = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    const productDiscount = productDiscountStore.discounts.get(item.jubelio_item_group_id);
    const discountPercentage = calculateDiscountPercentage(productDiscount?.max_discount_percentage);
    return total + (calculateDiscountedPrice(item.price, discountPercentage) * item.quantity);
  }, 0);
});

const totalDiscount = computed(() => {
  return subtotal.value - discountedSubtotal.value;
});

// NEW COMPUTED for new flow
const newShippingFee = computed(() => {
  if (!selectedShippingMethodData.value) return 0
  // Expedition provides 'price', online recipient provides 'cost'
  return selectedShippingMethodData.value.price ?? selectedShippingMethodData.value.cost ?? 0
})

const newInsuranceFee = computed(() => {
  if (!selectedShippingMethodNew.value || selectedShippingType.value === 'online_recipient') return 0
  const isPosShipping = selectedShippingMethodNew.value.toLowerCase().includes('pos')
  return isPosShipping ? 500 : 0
})

const newOrderTotal = computed(() => discountedSubtotal.value + newShippingFee.value + newInsuranceFee.value)

const newCanPlaceOrder = computed(() => {
  const isExpeditionValid = selectedShippingType.value === 'expedition' && selectedShippingMethodNew.value !== null;
  const isJneExpeditionValid = selectedShippingType.value === 'jne_expedition' && selectedShippingMethodNew.value !== null;
  const isOnlineRecipientValid = selectedShippingType.value === 'online_recipient' && selectedShippingMethodNew.value !== null && trackingNumber.value.trim() !== '';
  const isStorePickupValid = selectedShippingType.value === 'store_pickup' && selectedShippingMethodNew.value !== null;

  return (
    hasValidItems.value &&
    selectedShippingAddress.value !== null &&
    (isExpeditionValid || isJneExpeditionValid || isOnlineRecipientValid || isStorePickupValid) &&
    selectedPaymentMethod.value !== null
  );
})

// ==================== METHODS ====================
const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

// Initialization
const initializeOriginLocation = async () => {
  if (addressStore.locations.length > 0) {
    const pusatWarehouse = addressStore.locations.find(loc =>
      loc.location_name?.toLowerCase() === 'pusat'
    );
    
    const warehouse = pusatWarehouse || addressStore.locations[0];
    originLocation.value = {
      locationId: warehouse.location_id,
      provinceName: warehouse.province,
      provinceId: warehouse.province_id,
      cityName: warehouse.city,
      cityId: warehouse.city_id,
      districtName: warehouse.area,
      districtId: warehouse.district_id,
      subdistrictName: warehouse.subdistrict,
      subdistrictId: warehouse.subdistrict_id,
      postalCode: warehouse.post_code
    };
  }
};

// NEW METHODS for new components
const onAddressSelected = (address: ShippingAddress) => {
  console.log('Address selected:', address);
  selectedShippingAddress.value = address;
  // Reset shipping type and method when address changes
  selectedShippingType.value = 'expedition'; // Default back to expedition
  selectedShippingMethodNew.value = null;
  selectedShippingMethodData.value = null;
  trackingNumber.value = '';
}

const onShippingMethodSelected = (method: any) => {
  console.log('Expedition method selected:', method);
  selectedShippingMethodData.value = method;
}

const onOnlineRecipientMethodSelected = (method: any) => {
  console.log('Online recipient method selected:', method);
  selectedShippingMethodData.value = method;
}

const onStorePickupMethodSelected = (method: any) => {
  console.log('Store pickup method selected:', method);
  selectedShippingMethodData.value = method;
}

const onTrackingNumberUpdated = (newTrackingNumber: string) => {
  trackingNumber.value = newTrackingNumber;
}

const onShippingCalculated = (methods: any[]) => {
  console.log('Shipping methods calculated:', methods)
  newShippingMethods.value = methods
}

// NEW PLACE ORDER function (replace the old one when ready)
const placeOrderNew = async () => {
  if (!newCanPlaceOrder.value || isPlacingOrder.value) {
    return
  }

  isPlacingOrder.value = true

  try {
    const paymentMethod: PaymentMethod | undefined = checkoutStore.getPaymentMethodById(selectedPaymentMethod.value || '')
    
    if (!selectedShippingMethodData.value || !paymentMethod || !selectedShippingAddress.value || !originLocation.value) {
      throw new Error('Missing required checkout information')
    }

    // Ensure we have contact data
    if (!contactStore.contact) {
      throw new Error('Contact information is required')
    }

    // Transform cart items to Jubelio format
    const jubelioItems = orderTransformer.transformToJubelioItems(
      itemsForCheckout.value,
      originLocation.value.locationId || addressStore.locations[0].location_id,
      productDiscountStore,
      authStore.user?.membership_level
    );

    // Prepare totals with new values
    const totals = {
      subtotal: subtotal.value,
      discountedSubtotal: discountedSubtotal.value,
      shippingFee: newShippingFee.value,
      insuranceFee: newInsuranceFee.value,
      total: newOrderTotal.value
    }

    // Use the full contact data from store (this is the customer/reseller info)
    const contactData = contactStore.contact

    // Create Jubelio order
    const jubelioOrderData = orderTransformer.createJubelioOrder({
      contact: contactData,
      user: authStore.user,
      items: jubelioItems,
      shippingAddress: selectedShippingAddress.value, // Shipping recipient info
      shippingMethod: selectedShippingMethodData.value,
      shippingType: selectedShippingType.value,
      trackingNumber: trackingNumber.value,
      paymentMethod: paymentMethod,
      totals: totals,
      locationId: originLocation.value.locationId || addressStore.locations[0].location_id,
      storeId: addressStore.otoresellShop?.store_id.toString() || '-101',
      note: orderNote.value
    })

    // Transform cart items to OtoAPI format
    const otoapiItems = orderTransformer.transformToOtoApiItems(itemsForCheckout.value);

    // Create OtoAPI order
    const otoapiOrderData = orderTransformer.createOtoApiOrder({
      contact: contactData,
      user: authStore.user,
      items: otoapiItems,
      shippingAddress: selectedShippingAddress.value,
      shippingMethod: selectedShippingMethodData.value,
      shippingType: selectedShippingType.value,
      trackingNumber: trackingNumber.value,
      paymentMethod: paymentMethod,
      totals: totals
    })

    console.log('NEW jubelioOrderData', jubelioOrderData)
    console.log('NEW otoapiOrderData', otoapiOrderData)
    
    // if on testing mode, lets keep it disabled.
    const orderResult = await checkoutStore.createOrder({
      jubelio_order: jubelioOrderData,
      otoapi_order: otoapiOrderData
    });

    console.log('Order created successfully', orderResult)

    if (!orderResult || !orderResult.orderId) {
      // The error is already set in the store, so we just need to throw to trigger the catch block
      throw new Error(checkoutStore.orderError || 'Gagal membuat pesanan. Silakan coba lagi.')
    }

    if (orderResult.invoiceUrl) {
      // Redirect to external payment gateway
      window.location.href = orderResult.invoiceUrl;
    } else {
      // If there's no invoice URL, it's an error because payment can't proceed.
      throw new Error('Could not retrieve payment URL. Please try again.');
    }

  } catch (error) {
    logger.error('Failed to place order:', error)
    showSnackbar.value = true
    snackbarMessage.value = error instanceof Error ? error.message : 'Failed to place order. Please try again.'
    snackbarColor.value = 'error'
  } finally {
    isPlacingOrder.value = false
  }
}

// Update the onMounted function to include cart sync
onMounted(async () => {
  // only test
  // const request: JnePriceRequest = {
  //   from: 'KDR10000',
  //   thru: 'MJK10112',
  //   weight: 1
  // }

  // const response = await jneStore.getPrices(request)
  // console.log('JNE response', response)


  try {
    // Sync cart data
    try {
      await cartStore.loadCartFromServer();
      console.log('Cart synced with server for checkout');
    } catch (error) {
      console.error('Failed to sync cart from server:', error);
      showSnackbar.value = true;
      snackbarMessage.value = 'Could not sync latest cart data. Using local cart.';
      snackbarColor.value = 'warning';
    }
    
    // Initialize contact and locations (still need? for what?)
    if (authStore.user?.jubelio_contact_id) {
      await contactStore.fetchContact(authStore.user.jubelio_contact_id);
    }
    
    await addressStore.fetchLocations();
    await addressStore.getOtoresellShop();
    // Initialize locations
    await initializeOriginLocation();
    

  } catch (error) {
    console.error('Error initializing checkout page:', error);
    showSnackbar.value = true;
    snackbarMessage.value = 'Failed to load checkout data. Please try again.';
    snackbarColor.value = 'error';
  }
});

onUnmounted(() => {
  cartStore.clearSelectedItems();
});

watch(newOrderTotal, (newTotal) => {
  checkoutStore.setGrandTotal(newTotal);
}, { immediate: true });

watch(paymentMethods, (newMethods) => {
  if (newMethods.length > 0 && !newMethods.find(m => m.id === selectedPaymentMethod.value)) {
    selectedPaymentMethod.value = newMethods[0].id;
  }
}, { immediate: true });

watch(selectedShippingType, () => {
  selectedShippingMethodNew.value = null;
  selectedShippingMethodData.value = null;
  trackingNumber.value = '';
});
</script>


<style scoped>
.checkout-container {
  max-width: 1200px;
  margin: 0 auto;
}
.shipping-method-item {
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.shipping-method-item:hover {
  background-color: #f5f0ff;
}

.selected-method {
  background-color: #f5f0ff;
  border-left: 4px solid #673ab7;
}
.address-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s ease;
}
.address-card:hover {
  border-color: #9575cd;
}
.order-summary-card {
  position: sticky;
  top: 80px;
  z-index: 1;
}
.hover-underline:hover {
  text-decoration: underline !important;
}
</style>