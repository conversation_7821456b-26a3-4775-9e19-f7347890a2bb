<template>
  <div>
    <!-- Page Title -->
    <PageTitle 
      title="Cart" 
      icon="mdi-cart" 
      subtitle="Review your selected items before proceeding to checkout."
    />

    <!-- Empty Cart State -->
    <v-card v-if="cartStore.items.length === 0" class="pa-8 text-center">
      <v-icon size="64" color="grey-lighten-1" class="mb-4">mdi-cart-outline</v-icon>
      <h2 class="text-h5 mb-2">Your cart is empty</h2>
      <p class="text-body-1 text-grey mb-6">Looks like you haven't added any products to your cart yet.</p>
      <v-btn color="deep-purple" size="large" to="/products">
        Search Product
      </v-btn>
    </v-card>

    <!-- Cart Items -->
    <template v-else>
      <v-row>
        <!-- Cart Items Section (Left) -->
        <v-col cols="12" md="8">
          <v-card class="mb-4">
            <v-card-title class="py-2 bg-grey-lighten-4 d-flex align-center">
              <v-checkbox
                v-model="selectAll"
                label="Select All Products"
                hide-details
                color="deep-purple"
                @update:model-value="(value: boolean | null) => value !== null && handleSelectAllChange(value)"
              ></v-checkbox>
              <v-spacer></v-spacer>
              <span class="text-body-2 text-grey">{{ selectedItemCount }} items selected</span>
            </v-card-title>
            <v-divider></v-divider>

            <!-- Cart Items List -->
            <v-list>
              <div v-for="item in cartStore.items" :key="item.jubelio_item_id" class="cart-item">
                <v-list-item>
                  <template v-slot:prepend>
                    <v-checkbox
                      v-model="selectedItems"
                      :value="item.jubelio_item_id"
                      hide-details
                      color="deep-purple"
                      class="mr-2"
                      :disabled="isItemOutOfStock(item)"
                    ></v-checkbox>
                    <v-img
                      :src="item.image as string"
                      :alt="item.name"
                      width="80"
                      height="80"
                      cover
                      class="rounded mr-4"
                    ></v-img>
                  </template>

                  <v-list-item-title class="text-subtitle-1 font-weight-medium">
                    <router-link 
                      :to="`/products/${item.jubelio_item_id}`" 
                      class="text-decoration-none text-deep-purple-darken-1 hover-underline"
                    >
                      {{ item.name }}
                    </router-link>
                  </v-list-item-title>
                  
                  <v-list-item-subtitle v-if="item.variant && Array.isArray(item.variant)" class="text-caption text-grey mt-1">
                    <div v-for="(variantItem, index) in item.variant" :key="index" class="d-inline">
                      <span class="font-weight-medium">{{ variantItem.label }}:</span> {{ variantItem.value }}
                      <span v-if="index < item.variant.length - 1" class="mx-1">|</span>
                    </div>
                  </v-list-item-subtitle>

                  <v-list-item-subtitle v-else-if="item.variant" class="text-caption text-grey mt-1">
                    Variant: {{ item.variant }}
                  </v-list-item-subtitle>

                  
                  <v-list-item-subtitle v-if="item.sku" class="text-caption text-grey">
                    SKU: {{ item.sku }}
                  </v-list-item-subtitle>
                  
                  <v-list-item-subtitle class="text-caption" :class="getStockStatusClass(item)">
                    {{ getStockStatusText(item) }}
                  </v-list-item-subtitle>
                  
                  <template v-slot:append>
                    <div class="d-flex flex-column align-end" style="min-width: 220px;">
                      <div class="text-right w-100">
                        <div class="text-body-2 font-weight-medium">
                          <span :class="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0 ? 'text-red' : 'text-deep-purple-darken-2'">
                            {{ formatRupiah(calculateDiscountedPrice(item.price, calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage))) }}
                          </span>
                          <span class="text-grey-darken-1"> x {{ item.quantity }}</span>
                        </div>

                        <div v-if="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0" class="text-caption text-grey text-decoration-line-through">
                          {{ formatRupiah(item.price * item.quantity) }}
                        </div>

                        <div class="text-subtitle-1 font-weight-bold" :class="calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage) > 0 ? 'text-red' : 'text-grey-darken-3'">
                          {{ formatRupiah(calculateDiscountedPrice(item.price, calculateDiscountPercentage(productDiscountStore.discounts.get(item.jubelio_item_group_id)?.max_discount_percentage)) * item.quantity) }}
                        </div>
                      </div>
                      
                      <div class="d-flex align-center mt-3">
                        <v-btn
                          icon="mdi-minus"
                          variant="outlined"
                          density="comfortable"
                          size="small"
                          color="grey"
                          class="mr-2"
                          @click="decreaseQuantity(item)"
                          :disabled="item.quantity <= 1"
                        ></v-btn>
                        
                        <span class="quantity-display mx-2">{{ item.quantity }}</span>
                        
                        <v-btn
                          icon="mdi-plus"
                          variant="outlined"
                          density="comfortable"
                          size="small"
                          color="grey"
                          class="ml-2"
                          @click="increaseQuantity(item)"
                          :disabled="isMaxQuantityReached(item)"
                        ></v-btn>
                        
                        <v-btn
                          icon="mdi-delete"
                          variant="text"
                          color="error"
                          class="ml-4"
                          @click="showRemoveItemDialog(item)"
                        ></v-btn>
                      </div>
                    </div>
                  </template>
                </v-list-item>
                <v-divider></v-divider>
              </div>
            </v-list>
          </v-card>

          <!-- Clear Cart Button (Moved here) -->
          <div class="d-flex justify-end mb-6" v-if="cartStore.items.length > 0">
            <v-btn
              color="error"
              variant="text"
              prepend-icon="mdi-delete"
              @click="showClearCartDialog = true"
            >
              Clear All Items
            </v-btn>
          </div>
        </v-col>

        <!-- Order Summary Section (Right) -->
        <v-col cols="12" md="4">
          <v-card class="pa-4 order-summary" sticky>
            <h3 class="text-h6 mb-4">Order Summary</h3>
            
            <div class="d-flex justify-space-between">
              <span>Subtotal</span>
              <span>{{ formatRupiah(orderTotal) }}</span>
            </div>
            <div v-if="totalDiscount > 0" class="d-flex justify-space-between text-success">
              <span>Discount</span>
              <span>-{{ formatRupiah(totalDiscount) }}</span>
            </div>
            
            <v-divider class="my-4"></v-divider>
            
            <div class="d-flex justify-space-between mb-4">
              <span class="text-h6">Total ({{ selectedItemCount }} items)</span>
              <span class="text-h6 font-weight-bold">{{ formatRupiah(discountedOrderTotal) }}</span>
            </div>
            
            <v-btn
              color="deep-purple"
              size="large"
              block
              :disabled="selectedItems.length === 0"
              @click="proceedToCheckout"
              class="mb-2"
              :loading="isCheckingStock"
            >
              Proceed to Checkout
            </v-btn>
            
            <v-btn
              color="deep-purple"
              variant="text"
              block
              to="/products"
            >
              Search Product
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
    </template>

    <!-- Remove Item Dialog -->
    <v-dialog v-model="removeItemDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h5">Remove Item</v-card-title>
        <v-card-text>
          Are you sure you want to remove "{{ itemToRemove?.name }}" from your cart?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="removeItemDialog = false">
            Cancel
          </v-btn>
          <v-btn color="error" variant="elevated" @click="confirmRemoveItem">
            Remove
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- Clear Cart Dialog -->
    <v-dialog v-model="showClearCartDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h5">Clear Cart</v-card-title>
        <v-card-text>
          Are you sure you want to remove all items from your cart?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="showClearCartDialog = false">
            Cancel
          </v-btn>
          <v-btn color="error" variant="elevated" @click="confirmClearCart">
            Clear Cart
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
    <!-- Success Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :timeout="3000"
      :color="snackbarColor"
      location="top"
    >
      {{ snackbarMessage }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import type { CartProduct } from '~/schemas/otoapi/cart';
import { useProductDiscountStore } from '~/stores/product-discount';
import { useDiscountCalculator } from '~/composables/useDiscountCalculator';
import { logger } from '~/utils/logger';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Cart | Otoresell'
});

// Stores
const cartStore = useCartStore();
const productStore = useProductStore();
const productDiscountStore = useProductDiscountStore();
const { calculateDiscountedPrice, calculateDiscountPercentage } = useDiscountCalculator();

// State
const selectedItems = ref<(string | number)[]>(cartStore.selectedItemIds);
const selectAll = ref(true);
const removeItemDialog = ref(false);
const itemToRemove = ref<CartProduct | null>(null);
const showClearCartDialog = ref(false);
const showSnackbar = ref(false);
const snackbarMessage = ref('');
const snackbarColor = ref('success');
const stockInfo = ref<Map<string | number, any>>(new Map());
const isLoadingStock = ref(false);
const isCheckingStock = ref(false);

// Computed
const selectedItemCount = computed(() => {
  return cartStore.selectedItems.length;
});

const selectedSubtotal = computed(() => {
  return cartStore.selectedTotal;
});

const orderTotal = computed(() => {
  return cartStore.selectedTotal;
});

const discountedOrderTotal = computed(() => {
  return cartStore.selectedItems.reduce((total, item) => {
    const productDiscount = productDiscountStore.discounts.get(item.jubelio_item_group_id);
    const discountPercentage = calculateDiscountPercentage(productDiscount?.max_discount_percentage);
    return total + (calculateDiscountedPrice(item.price, discountPercentage) * item.quantity);
  }, 0);
});

const totalDiscount = computed(() => {
  return orderTotal.value - discountedOrderTotal.value;
});

// Methods
const formatRupiah = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

const handleSelectAllChange = (value: boolean) => {
  if (value) {
    // Only select items that are in stock
    selectedItems.value = cartStore.items
      .filter(item => !isItemOutOfStock(item))
      .map(item => item.jubelio_item_id);
  } else {
    selectedItems.value = [];
  }
};

const increaseQuantity = async (item: CartProduct) => {
  if (!item) return

  const stockData = stockInfo.value.get(item.jubelio_item_id)
  const availableStock = stockData?.available || 0

  if (item.quantity < availableStock) {
    try {
      await cartStore.updateQuantity(item.id, item.quantity + 1)
      snackbarMessage.value = 'Quantity updated'
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (error) {
      console.error('Failed to update quantity:', error)
      snackbarMessage.value = 'Failed to update quantity. Please try again.'
      snackbarColor.value = 'error'
      showSnackbar.value = true
    }
  } else {
    snackbarMessage.value = 'Cannot add more. Maximum stock reached.'
    snackbarColor.value = 'error'
    showSnackbar.value = true
  }
}

const decreaseQuantity = async (item: CartProduct) => {
  if (item && item.quantity > 1) {
    try {
      await cartStore.updateQuantity(item.id, item.quantity - 1)
      snackbarMessage.value = 'Quantity updated'
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (error) {
      console.error('Failed to update quantity:', error)
      snackbarMessage.value = 'Failed to update quantity. Please try again.'
      snackbarColor.value = 'error'
      showSnackbar.value = true
    }
  }
}


const showRemoveItemDialog = (item: any) => {
  itemToRemove.value = item;
  removeItemDialog.value = true;
};

const confirmRemoveItem = async () => {
  if (itemToRemove.value) {
    const id = itemToRemove.value.jubelio_item_id;
    
    try {
      await cartStore.removeItem(id); // true = sync to server
      
      // Remove from selected items if present
      const index = selectedItems.value.indexOf(id);
      if (index !== -1) {
        selectedItems.value.splice(index, 1);
      }
      
      // Remove from stock info
      stockInfo.value.delete(id);
      
      // Show success message
      snackbarMessage.value = `${itemToRemove.value.name} removed from cart`;
      snackbarColor.value = 'success';
      showSnackbar.value = true;
    } catch (error) {
      console.error('Failed to remove item:', error);
      snackbarMessage.value = 'Failed to remove item. Please try again.';
      snackbarColor.value = 'error';
      showSnackbar.value = true;
    }
    
    // Close dialog
    removeItemDialog.value = false;
    itemToRemove.value = null;
  }
};

const confirmClearCart = async () => {
  try {
    await cartStore.clearCart(); // true = sync to server
    selectedItems.value = [];
    stockInfo.value.clear();
    
    // Show success message
    snackbarMessage.value = 'Cart cleared successfully';
    snackbarColor.value = 'success';
    showSnackbar.value = true;
  } catch (error) {
    console.error('Failed to clear cart:', error);
    snackbarMessage.value = 'Failed to clear cart. Please try again.';
    snackbarColor.value = 'error';
    showSnackbar.value = true;
  }
  
  // Close dialog
  showClearCartDialog.value = false;
};


const proceedToCheckout = async () => {
  if (cartStore.selectedItems.length === 0) {
    snackbarMessage.value = 'Please select at least one item to checkout';
    snackbarColor.value = 'error';
    showSnackbar.value = true;
    return;
  }
  
  // Check stock availability before proceeding
  isCheckingStock.value = true;
  
  try {
    // Verify stock for all selected items
    const selectedCartItems = cartStore.items.filter(item => selectedItems.value.includes(item.jubelio_item_id));
    
    // Fetch stock for any items we don't have stock info for
    const itemsToFetch = selectedCartItems.filter(item => !stockInfo.value.has(item.jubelio_item_id));
    
    if (itemsToFetch.length > 0) {
      await Promise.all(itemsToFetch.map(item => fetchStockForItem(item)));
    }
    
    // Check if any selected item is out of stock or exceeds available quantity
    const invalidItems = selectedCartItems.filter(item => {
      const stockData = stockInfo.value.get(item.jubelio_item_id);
      const availableStock = stockData?.available || 0;
      
      return availableStock < item.quantity;
    });
    
    if (invalidItems.length > 0) {
      const itemNames = invalidItems.map(item => item.name).join(', ');
      snackbarMessage.value = `Some items have insufficient stock:
      ${itemNames}. Please adjust quantities.`;
      snackbarColor.value = 'error';
      showSnackbar.value = true;
      return;
    }
    
    // Save selected items to cart store
    cartStore.setSelectedItems(selectedItems.value);
    
    // Navigate to checkout
    navigateTo('/cart/checkout');
  } catch (error) {
    console.error('Error checking stock:', error);
    snackbarMessage.value = 'Error checking stock availability. Please try again.';
    snackbarColor.value = 'error';
    showSnackbar.value = true;
  } finally {
    isCheckingStock.value = false;
  }
};

const fetchStockForItem = async (item: CartProduct) => {
  // console.log('Fetching stock for item:', item);
  if (!item.sku) return;
  
  try {
    const stockData = await productStore.fetchStockByItemCode(item.sku);
    // console.log('Stock data:', stockData);
    
    if (stockData) {
      // The response is directly the item object with total_stocks property
      const availableStock = stockData.total_stocks.available;
      
      // Store the available stock in our stockInfo map
      stockInfo.value.set(item.jubelio_item_id, { available: availableStock });
      
      // If the item is out of stock, remove it from selected items
      if (availableStock <= 0 && selectedItems.value.includes(item.jubelio_item_id)) {
        const index = selectedItems.value.indexOf(item.jubelio_item_id);
        if (index !== -1) {
          selectedItems.value.splice(index, 1);
        }
      }
      
      // If quantity exceeds available stock, adjust it
      if (item.quantity > availableStock) {
        if (availableStock > 0) {
          cartStore.updateQuantity(item.id, availableStock)
          snackbarMessage.value = `Quantity for "${item.name}" adjusted to match available stock`
          snackbarColor.value = 'warning';
          showSnackbar.value = true;
        } else {
          // If no stock available, show warning
          snackbarMessage.value = `"${item.name}" is out of stock`;
          snackbarColor.value = 'warning';
          showSnackbar.value = true;
        }
      }
    }
  } catch (error) {
    console.error(`Error fetching stock for item ${item.jubelio_item_id}:`, error);
  }
};

const isItemOutOfStock = (item: CartProduct): boolean => {
  const stockData = stockInfo.value.get(item.jubelio_item_id);
  return stockData ? stockData.available <= 0 : false;
};

const isMaxQuantityReached = (item: CartProduct): boolean => {
  const stockData = stockInfo.value.get(item.jubelio_item_id);
  if (!stockData) return false;
  
  return item.quantity >= stockData.available;
};

const getStockStatusText = (item: CartProduct): string => {
  const stockData = stockInfo.value.get(item.jubelio_item_id);
  
  if (!stockData) {
    return 'Checking stock...';
  }
  
  if (stockData.available <= 0) {
    return 'Out of stock';
  }
  
  if (item.quantity > stockData.available) {
    return `Only ${stockData.available} available`;
  }
  
  return `In stock (${stockData.available} available)`;
};

const getStockStatusClass = (item: CartProduct): string => {
  const stockData = stockInfo.value.get(item.jubelio_item_id);
  
  if (!stockData) {
    return 'text-grey';
  }
  
  if (stockData.available <= 0) {
    return 'text-error';
  }
  
  if (item.quantity > stockData.available) {
    return 'text-warning';
  }
  
  return 'text-success';
};

// Lifecycle
// Lifecycle
onMounted(async () => {
  try {
    await cartStore.loadCartFromServer();
  } catch (error) {
    console.error('Failed to load cart from server:', error);
    // Continue with local cart if server fails
  }

  // Initialize selected items with cart's selectedItemIds if available
  if (cartStore.selectedItemIds.length > 0) {
    selectedItems.value = cartStore.selectedItemIds;
  } else {
    // Otherwise initialize with all cart items
    selectedItems.value = cartStore.items.map(item => item.jubelio_item_id);
    cartStore.setSelectedItems(selectedItems.value);
  }

  selectAll.value = true;

  // Save selected items to cart store
  cartStore.setSelectedItems(selectedItems.value);

  // Fetch stock information for all cart items
  if (cartStore.items.length > 0) {
    isLoadingStock.value = true;
    
    try {
      await Promise.all(cartStore.items.map(item => fetchStockForItem(item)));
      
      // After fetching stock, filter out any out-of-stock items from selection
      selectedItems.value = selectedItems.value.filter(id => {
        const stockData = stockInfo.value.get(id);
        return stockData && stockData.available > 0;
      });
      
      // Update selectAll state
      selectAll.value = selectedItems.value.length === cartStore.items.length && cartStore.items.length > 0;
      
      // Save selected items to cart store
      cartStore.setSelectedItems(selectedItems.value);
    } catch (error) {
      console.error('Error fetching stock information:', error);
    } finally {
      isLoadingStock.value = false;
    }
  }
});


// Watchers
watch(selectedItems, (newSelectedItems) => {
  // Update selectAll checkbox state
  const selectableItems = cartStore.items.filter(item => !isItemOutOfStock(item));
  selectAll.value = newSelectedItems.length === selectableItems.length && selectableItems.length > 0;
  
  // Save selected items to cart store
  cartStore.setSelectedItems(newSelectedItems);
});

watch(cartStore.items, (newCartItems) => {
  // If cart is empty, reset selected items
  if (newCartItems.length === 0) {
    selectedItems.value = [];
    selectAll.value = false;
    stockInfo.value.clear();
  } 
  // If all items are selected, and a new item is added, select it too
  else if (selectAll.value) {
    // Only select items that are in stock
    selectedItems.value = newCartItems
      .filter(item => !isItemOutOfStock(item))
      .map(item => item.jubelio_item_id);
  }
  
  // Fetch stock for any new items
  newCartItems.forEach(async (item) => {
    if (!stockInfo.value.has(item.jubelio_item_id)) {
      await fetchStockForItem(item);
    }
  });
}, { deep: true });
</script>

<style scoped>
.cart-item {
  transition: background-color 0.2s;
}
.cart-item:hover {
  background-color: rgba(103, 58, 183, 0.05);
}
.quantity-display {
  min-width: 30px;
  text-align: center;
  font-weight: 500;
}
.order-summary {
  position: sticky;
  top: 80px;
}
.hover-underline:hover {
  text-decoration: underline !important;
}
@media (max-width: 960px) {
  .order-summary {
    position: static;
  }
}
</style>
