<template>
  <div class="py-8 px-4 bg-grey-lighten-5">
    <v-container max-width="800">
      <!-- Success Header -->
      <div class="text-center mb-8">
        <v-icon color="deep-purple" size="80">mdi-check-decagram-outline</v-icon>
        <h1 class="text-h4 font-weight-bold mt-4 mb-2 text-grey-darken-3">Payment Successful!</h1>
        <p class="text-body-1 text-grey-darken-1">Your order has been successfully paid and is now being processed.</p>
        <v-chip
          size="large"
          color="deep-purple"
          variant="tonal"
          prepend-icon="mdi-receipt-text-outline"
          class="font-weight-bold mt-4"
        >
          Order #{{ orderId }}
        </v-chip>
      </div>

      <!-- Loading State -->
      <v-card v-if="loadingJubelioOrderDetail" class="mb-6 rounded-lg" elevation="1">
        <v-card-text class="text-center pa-12">
          <v-progress-circular
            indeterminate
            color="deep-purple"
            size="50"
            width="5"
          ></v-progress-circular>
          <p class="mt-6 text-body-1 text-deep-purple">Verifying payment status...</p>
        </v-card-text>
      </v-card>

      <!-- Error State -->
      <v-alert
        v-if="errorJubelioOrderDetail"
        type="error"
        variant="tonal"
        class="mb-6 rounded-lg"
        border="start"
        icon="mdi-alert-circle-outline"
      >
        <template #title><div class="font-weight-bold">Unable to Verify Payment</div></template>
        {{ errorJubelioOrderDetail }}
      </v-alert>

      <!-- Content Section -->
      <div v-if="jubelioOrderDetail && !loadingJubelioOrderDetail && jubelioOrderDetail.is_paid == true">
        <!-- Action Buttons -->
        <v-card elevation="1" class="rounded-lg">
          <v-card-text class="pa-6 text-center">
            <div class="text-body-1 font-weight-medium text-grey-darken-3">What's Next?</div>
            <div class="text-body-2 text-grey-darken-1 mb-4">
              You can track your order's progress from the transaction details page.
            </div>
            <v-row justify="center">
              <v-col cols="12" sm="5">
                <v-btn
                  v-if="transactionDetail"
                  block
                  :size="smAndDown ? 'default' : 'large'"
                  color="deep-purple"
                  variant="flat"
                  :to="`/transactions/${transactionDetail.id}`"
                  prepend-icon="mdi-file-document-outline"
                  class="text-none"
                >
                  View Order Details
                </v-btn>
              </v-col>
              <v-col cols="12" sm="5">
                <v-btn
                  block
                  :size="smAndDown ? 'default' : 'large'"
                  variant="outlined"
                  color="deep-purple"
                  to="/products"
                  prepend-icon="mdi-shopping-outline"
                  class="text-none"
                >
                  Continue Shopping
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </div>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useDisplay } from 'vuetify';
import { useTransactionStore } from '~/stores/transaction';
import type { Order } from '~/schemas/transaction/order';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Order Successful | Otoresell'
});

const route = useRoute();
const transactionStore = useTransactionStore();
const { smAndDown } = useDisplay();

const orderId = ref<string>('');
const transactionDetail = ref<Order | null>(null);

const jubelioOrderDetail = computed(() => transactionStore.jubelioOrderDetail);
const loadingJubelioOrderDetail = computed(() => transactionStore.loadingJubelioDetail);
const errorJubelioOrderDetail = computed(() => transactionStore.errorJubelioDetail);


onMounted(async () => {
  const orderIdParam = route.query.orderId;
  orderId.value = Array.isArray(orderIdParam) ? orderIdParam[0] || '' : (orderIdParam || '');
    
  if (!orderId.value.trim()) {
    return navigateTo('/cart');
  }

  const orderIdNum = parseInt(orderId.value);
  if (isNaN(orderIdNum) || orderIdNum <= 0) {
    return navigateTo('/cart');
  }

  try {
    // Fetch both Jubelio details and our internal transaction details in parallel
    const [jubelioResult] = await Promise.all([
      transactionStore.getJubelioOrderDetail(orderIdNum),
      transactionStore.fetchOrders({ jubelio_order_id: orderIdNum, per_page: 1 })
    ]);

    // Assign the fetched transaction to our local ref
    if (transactionStore.orders.length > 0) {
      transactionDetail.value = transactionStore.orders[0];
    }
    
    // If our internal transaction is not found, it's also an issue
    if (!transactionDetail.value) {
      console.error('Could not find internal transaction for Jubelio order ID:', orderIdNum);
      // We can still show the page, but the details button will be disabled.
    }
  } catch (error) {
    console.error('Failed to fetch order details:', error);
    return navigateTo('/cart');
  }
});
</script>

<style scoped>
.font-mono {
  font-family: 'Roboto Mono', 'Courier New', Courier, monospace;
}

.text-none {
  text-transform: none !important;
}

.border-b {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.payment-details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.summary-box {
  border: 1px solid #e0e0e0;
}

@media (min-width: 960px) {
  .payment-details-grid {
    grid-template-columns: 1fr 1fr;
  }
  .border-left-md {
    border-left: 1px solid rgba(0,0,0,0.12);
    padding-left: 24px !important;
  }
}

.v-btn {
  transition: all 0.2s ease-in-out;
}

.v-timeline-item .v-timeline-item__body {
  width: 100%;
}
</style>