<template>
  <div>
    <!-- Page Title -->
    <PageTitle 
      title="Dashboard"
      icon="mdi-view-dashboard"
      subtitle="Welcome to your Otoresell dashboard. Here's your business at a glance."
    />

    <!-- Welcome Card -->
    <v-row class="mb-6">
      <v-col cols="12">
        <v-card class="welcome-card pa-6 elevation-3">
          <v-card-text>
            <div class="d-flex flex-column flex-sm-row justify-space-between align-center">
              <div>
                <h1 class="text-h3 font-weight-bold mb-2">{{ greeting }}, {{ userName }}</h1>
              </div>
              <v-icon class="d-none d-sm-flex welcome-icon mt-4 mt-sm-0" size="64">mdi-view-dashboard</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Dashboard Overview Section -->
    <div class="d-flex align-center mb-6">
      <v-icon color="deep-purple" class="mr-2">mdi-chart-box</v-icon>
      <h2 class="text-h5 font-weight-medium">Overview</h2>
    </div>
        
    <v-row>
      <TotalTransaction />
      <TotalProduct />
    </v-row>
        
    <v-row class="mt-8">
      <v-col cols="12">
        <v-card class="rounded-lg elevation-2">
          <v-card-title class="d-flex align-center py-4 px-6">
            <v-icon color="deep-purple" class="mr-2">mdi-lightbulb-outline</v-icon>
            <span class="text-h6">Quick Tips for Resellers</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text class="pa-6">
            <v-row>
              <v-col
                v-for="tip in tips"
                :key="tip.title"
                cols="12"
                md="4"
              >
                <v-card class="tip-card h-100" variant="tonal">
                  <v-card-text class="pa-6">
                    <div class="d-flex justify-center mb-4">
                      <v-avatar :color="tip.color" size="60">
                        <v-icon size="30" color="white">{{ tip.icon }}</v-icon>
                      </v-avatar>
                    </div>
                    <h3 class="text-h6 font-weight-bold text-center mb-2">{{ tip.title }}</h3>
                    <p class="text-body-2 text-center">{{ tip.text }}</p>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '~/stores/auth';
import TotalTransaction from '~/components/dashboard/TotalTransaction.vue';
import TotalProduct from '~/components/dashboard/TotalProduct.vue';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Dashboard | Otoresell'
});

const authStore = useAuthStore();

const userName = computed(() => {
  return authStore.user?.name || 'User';
});

const greeting = computed(() => {
  const hour = new Date().getHours();
  if (hour < 12) return 'Good Morning';
  if (hour < 18) return 'Good Afternoon';
  return 'Good Evening';
});

const tips = ref([
  {
    icon: 'mdi-magnify',
    color: 'primary',
    title: 'Search Products',
    text: 'Use our advanced search to find the best products from suppliers. Filter by category, price, and supplier ratings to find profitable items.'
  },
  {
    icon: 'mdi-cart-plus',
    color: 'success',
    title: 'Place Orders',
    text: 'Easily place orders with suppliers directly through the platform. Track order status and manage your inventory efficiently.'
  },
  {
    icon: 'mdi-history',
    color: 'info',
    title: 'Track Transactions',
    text: 'Monitor all your transaction history in one place. Analyze your purchasing patterns and supplier performance to optimize your business.'
  }
]);
</script>

<style scoped>
.welcome-card {
  background: linear-gradient(135deg, #9c27b0, #673ab7);
  color: white;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.welcome-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  clip-path: polygon(100% 0, 0% 100%, 100% 100%);
}

.welcome-text {
  max-width: 600px;
  opacity: 0.9;
}

.welcome-icon {
  opacity: 0.8;
}

.tip-card {
  transition: all 0.3s ease;
}

.tip-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.12) !important;
}
</style>
