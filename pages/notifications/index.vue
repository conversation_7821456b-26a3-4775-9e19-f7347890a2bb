<template>
  <div>
    <!-- Page Title -->
    <PageTitle 
      title="Notifications"
      icon="mdi-bell"
      subtitle="View and manage all your notifications in one place."
    />
        
    <!-- Actions Card -->
    <v-card class="mb-6" variant="tonal">
      <v-card-text class="pa-6">
        <v-row align="center">
          <!-- Items per page -->
          <v-col cols="12" md="3">
            <v-select
              v-model="itemsPerPage"
              :items="perPageOptions"
              label="Items per Page"
              variant="outlined"
              density="compact"
              hide-details
              @update:model-value="handlePerPageChange"
            />
          </v-col>
          
          <v-col cols="12" md="9" class="d-flex justify-end flex-wrap ga-2">
            <v-btn
              color="deep-purple"
              prepend-icon="mdi-refresh"
              variant="outlined"
              @click="refreshData"
              :loading="notificationStore.loading"
            >
              Refresh
            </v-btn>
            <v-btn
              v-if="unreadCount > 0"
              color="info"
              prepend-icon="mdi-check-all"
              @click="handleMarkAllAsRead"
              :loading="notificationStore.loading"
            >
              Mark All Read
            </v-btn>
            <v-btn
              color="error"
              prepend-icon="mdi-delete-sweep"
              @click="handleClearAll"
              :loading="notificationStore.loading"
              :disabled="notificationStore.notifications.length === 0"
            >
              Clear All
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Notifications List Card -->
    <v-card elevation="2">
      <!-- Card Header with Stats -->
      <v-card-title class="d-flex align-center justify-space-between pa-4 bg-grey-lighten-5">
        <div class="d-flex align-center">
          <v-icon color="deep-purple" class="me-2">mdi-bell-ring</v-icon>
          <span class="text-h6">Notifications List</span>
        </div>
        <div class="d-flex ga-2">
          <v-chip
            v-if="!notificationStore.loading"
            color="deep-purple"
            variant="outlined"
            size="small"
          >
            {{ notificationStore.pagination.total }} Total
          </v-chip>
          <v-chip
            v-if="unreadCount > 0"
            color="error"
            variant="tonal"
            size="small"
          >
            {{ unreadCount }} Unread
          </v-chip>
        </div>
      </v-card-title>
      <v-divider />

      <!-- Loading State -->
      <div v-if="notificationStore.loading" class="text-center pa-8">
        <v-progress-circular
          indeterminate
          color="deep-purple"
          size="64"
        />
        <div class="mt-4 text-h6 text-grey-darken-1">Loading notifications...</div>
      </div>

      <!-- Error State -->
      <v-alert
        v-else-if="notificationStore.error"
        type="error"
        class="ma-4"
        closable
        @click:close="notificationStore.error = null"
      >
        {{ notificationStore.error }}
      </v-alert>

      <!-- Empty State -->
      <div v-else-if="notificationStore.notifications.length === 0" class="text-center pa-12">
        <v-icon size="80" color="grey-lighten-1" class="mb-4">
          mdi-bell-outline
        </v-icon>
        <div class="text-h6 text-grey-darken-1 mb-2">No notifications found</div>
        <div class="text-body-1 text-grey-darken-1">
          You're all caught up! No notifications to show.
        </div>
      </div>

      <!-- Notifications Timeline -->
      <template v-else>
        <v-timeline align="start" density="compact" class="notification-timeline pa-4">
          <v-timeline-item
            v-for="notification in notificationStore.notifications"
            :key="notification.id"
            :dot-color="notification.read_at ? 'grey-lighten-1' : 'deep-purple'"
            :icon="getNotificationIcon(notification)"
            size="small"
            fill-dot
          >
            <div
              class="notification-card pa-4 elevation-2"
              :class="{ 'unread': !notification.read_at }"
              @click="handleNotificationClick(notification)"
            >
              <div class="d-flex justify-space-between align-start">
                <component
                  :is="resolveNotificationComponent(notification)"
                  :notification="notification"
                  class="flex-grow-1"
                />
                
                <!-- Action Menu -->
                <v-menu location="bottom end">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon
                      size="small"
                      variant="text"
                      v-bind="props"
                      @click.stop
                    >
                      <v-icon size="20">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list density="compact">
                    <v-list-item
                      v-if="!notification.read_at"
                      @click.stop="handleMarkAsRead(notification.id)"
                    >
                      <template v-slot:prepend>
                        <v-icon>mdi-check</v-icon>
                      </template>
                      <v-list-item-title>Mark as read</v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      @click.stop="handleDelete(notification.id)"
                      class="text-error"
                    >
                      <template v-slot:prepend>
                        <v-icon color="error">mdi-delete</v-icon>
                      </template>
                      <v-list-item-title>Delete</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
          </v-timeline-item>
        </v-timeline>

        <!-- Pagination -->
        <v-divider />
        <div class="d-flex align-center justify-space-between pa-4">
          <div class="text-body-2 text-grey-darken-1">
            Showing {{ notificationStore.pagination.from }} to {{ notificationStore.pagination.to }} of {{ notificationStore.pagination.total }} notifications
          </div>
          <v-pagination
            v-model="currentPage"
            :length="notificationStore.pagination.last_page"
            :total-visible="5"
            @update:model-value="handlePageChange"
            density="comfortable"
          />
        </div>
      </template>
    </v-card>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import { useNotificationAppStore } from '~/stores/notification-app'
import { storeToRefs } from 'pinia'
import type { Notification } from '~/schemas/otoapi/notification'
import { useSnackbar } from '~/composables/useSnackbar'
import { useNotificationUtils } from '~/composables/useNotificationUtils'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
})

useHead({
  title: 'Notifications | Otoadmin'
})

const notificationStore = useNotificationAppStore()
const { notifications, loading, error, pagination, unreadCount } = storeToRefs(notificationStore)
const { showSnackbar } = useSnackbar()
const { getNotificationLink, getNotificationIcon } = useNotificationUtils()

// Reactive data
const itemsPerPage = ref(15)
const currentPage = ref(1)

// Options
const perPageOptions = [
  { title: '10', value: 10 },
  { title: '15', value: 15 },
  { title: '25', value: 25 },
  { title: '50', value: 50 }
]

// Methods
const NotificationOrderCreated = defineAsyncComponent(() => import('~/components/notifications/OrderCreated.vue'))
const NotificationOrderStatusUpdated = defineAsyncComponent(() => import('~/components/notifications/OrderStatusUpdated.vue'))
const NotificationUserCreated = defineAsyncComponent(() => import('~/components/notifications/UserCreated.vue'))
const NotificationWelcome = defineAsyncComponent(() => import('~/components/notifications/Welcome.vue'))
const NotificationDefault = defineAsyncComponent(() => import('~/components/notifications/Default.vue'))

const resolveNotificationComponent = (notification: Notification) => {
  const { type, action } = notification.data
  if (type === 'order' && action === 'created') return NotificationOrderCreated
  if (type === 'order' && action === 'status_updated') return NotificationOrderStatusUpdated
  if (type === 'user' && action === 'created') return NotificationUserCreated
  if (type === 'user' && action === 'verified') return NotificationWelcome
  return NotificationDefault
}


// Page and data methods
const handlePerPageChange = async () => {
  try {
    currentPage.value = 1
    await loadNotifications()
  } catch (error) {
    showSnackbar('Failed to update page size', 'error')
  }
}

const handlePageChange = async (page: number) => {
  try {
    currentPage.value = page
    await loadNotifications()
  } catch (error) {
    showSnackbar('Failed to load page', 'error')
  }
}

const refreshData = async () => {
  try {
    await loadNotifications()
    showSnackbar('Notifications refreshed successfully', 'success')
  } catch (error) {
    showSnackbar('Failed to refresh notifications', 'error')
  }
}

const loadNotifications = async () => {
  try {
    await notificationStore.fetchNotifications(currentPage.value, itemsPerPage.value)
  } catch (error) {
    showSnackbar('Failed to load notifications', 'error')
  }
}

// Action handlers
const handleMarkAsRead = async (id: string) => {
  try {
    await notificationStore.markNotificationAsRead(id)
    showSnackbar('Notification marked as read', 'success')
  } catch (error) {
    showSnackbar('Failed to mark notification as read', 'error')
  }
}

const handleMarkAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead()
    showSnackbar('All notifications marked as read', 'success')
  } catch (error) {
    showSnackbar('Failed to mark all notifications as read', 'error')
  }
}

const handleDelete = async (id: string) => {
  try {
    await notificationStore.deleteNotification(id)
    showSnackbar('Notification deleted', 'success')
  } catch (error) {
    showSnackbar('Failed to delete notification', 'error')
  }
}

const handleClearAll = async () => {
  try {
    await notificationStore.clearAllNotifications()
    showSnackbar('All notifications cleared', 'success')
  } catch (error) {
    showSnackbar('Failed to clear notifications', 'error')
  }
}

const handleNotificationClick = async (notification: Notification) => {
  const link = getNotificationLink(notification)
  
  if (link) {
    if (!notification.read_at) {
      await handleMarkAsRead(notification.id)
    }
    await navigateTo(link)
  }
}

// Lifecycle
onMounted(async () => {
  await loadNotifications()
})
</script>

<style scoped>
.notification-timeline {
  padding-top: 0;
}


.notification-card {
  transition: all 0.2s ease-in-out;
  border-left: 4px solid transparent;
  border-radius: 12px;
  cursor: pointer;
}

.notification-card:hover {
  transform: translateY(-2px);
}

.notification-card.unread {
  background-color: rgba(98, 0, 238, 0.07);
  border-left-width: 5px;
  border-left-color: #6200EE;
}

/* Consistent styling with user page */
.v-card {
  border-radius: 12px;
}

.v-card-title {
  font-weight: 600;
}

.v-btn {
  text-transform: none;
  font-weight: 500;
  min-height: 40px;
}

.v-text-field .v-field,
.v-select .v-field {
  border-radius: 8px;
}

.v-pagination {
  justify-content: center;
}

.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.v-chip {
  font-weight: 500;
}

.v-menu .v-list {
  min-width: 180px;
}

.v-list-item {
  padding-inline: 16px;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  text-decoration: underline;
}

@media (max-width: 960px) {
  .d-flex.justify-end {
    justify-content: start !important;
  }
}

@media (max-width: 600px) {
  .notification-card:hover {
    transform: none;
  }
}
</style>
