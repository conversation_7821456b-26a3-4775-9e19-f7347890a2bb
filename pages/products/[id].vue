<template>
  <div>
    <!-- Page Title with Back Button -->
    <div class="d-flex flex-wrap align-center mb-4">
      <v-btn
        color="deep-purple"
        variant="text"
        @click="goBackToProducts"
        prepend-icon="mdi-arrow-left"
        class="px-1"
        density="comfortable"
      >
        Back to Products
      </v-btn>
      
      <v-spacer></v-spacer>
      
      <!-- Optional: Add product ID or SKU for reference -->
      <span class="text-caption text-grey" v-if="product">
        Product ID: {{ product?.item_group_id || 'N/A' }} - Variant ID: {{ selectedVariant?.item_id || 'N/A' }}
      </span>
    </div>

    <!-- Page Title -->
    <PageTitle 
      title="Product Detail"
      icon="mdi-package-variant"
      subtitle="Explore detailed information about this product before purchasing."
    />

    <!-- Loading State -->
    <v-container v-if="isLoading" class="d-flex justify-center align-center my-8">
      <v-progress-circular indeterminate color="deep-purple" size="64"></v-progress-circular>
    </v-container>
    
    <!-- Error State -->
    <v-container v-else-if="hasError" class="text-center my-8">
      <v-icon size="64" color="error">mdi-alert-circle-outline</v-icon>
      <h3 class="text-h5 mt-4">Oops! Something went wrong</h3>
      <v-btn color="deep-purple" class="mt-4" @click="fetchData">Try Again</v-btn>
    </v-container>
    
    <!-- Product Detail Content -->
    <div v-else-if="product" class="">
      <v-container class="px-0 py-0">
        <v-row>
          <!-- Product Images -->
          <v-col cols="12" md="6" class="mb-4 mb-md-0">
            <div class="product-images">
              <!-- Main Image with Out of Stock Badge -->
              <v-img
                :src="selectedImage || productStore.placeholderImage"
                :aspect-ratio="1"
                class="rounded-sm main-image mb-4 position-relative"
              >
                <template v-slot:placeholder>
                  <v-row class="fill-height ma-0" align="center" justify="center">
                    <v-progress-circular indeterminate color="grey-lighten-5"></v-progress-circular>
                  </v-row>
                </template>
                
                <!-- Out of Stock Badge for Main Image -->
                <div v-if="!selectedVariant || realTimeStock <= 0"
                     class="stock-badge out-of-stock">
                  Out of Stock
                </div>
              </v-img>
            </div>
          </v-col>
          
          <!-- Product Info -->
          <v-col cols="12" md="6">
            <div class="product-info">
              <!-- Product Name -->
              <h5 class="text-h5 font-weight-bold product-title">
                {{ product.item_group_name }}
              </h5>
              
              <!-- Selected Variant -->
              <div v-if="selectedVariant" class="mt-2 d-flex flex-wrap">
                <v-chip 
                  v-for="(varValue, index) in formatVariationValues(selectedVariant.variation_values)"
                  :key="index"
                  color="deep-purple-darken-2" 
                  size="small"
                  class="mr-2 mb-2"
                >
                  {{ varValue }}
                </v-chip>
              </div>
              
              <!-- Product Price -->
              <div class="mt-4">
                <div v-if="discountPercentage > 0" class="text-body-1 text-grey text-decoration-line-through">
                  {{ formatPrice(selectedVariant?.sell_price || product?.sell_price) }}
                </div>
                <div class="d-flex align-center">
                  <v-icon
                    v-if="discountPercentage > 0"
                    size="small"
                    color="red"
                    class="mr-1"
                  >
                    mdi-ticket-percent-outline
                  </v-icon>
                  <h2
                    class="text-h5 font-weight-bold"
                    :class="discountPercentage > 0 ? 'text-red' : 'deep-purple--text'"
                  >
                    {{ formatPrice(discountedPrice) }}
                  </h2>
                </div>
              </div>
              
              <!-- Product Attributes -->

              <div class="d-flex align-center mt-2">
                <v-icon color="grey-darken-1" class="mr-2">mdi-barcode</v-icon>
                <span class="text-body-1">SKU: {{ selectedVariant?.item_code || 'N/A' }}</span>
              </div>
              
              <div class="mt-2">
                <div class="d-flex align-center mb-2">
                  <v-icon color="grey-darken-1" class="mr-2">mdi-weight</v-icon>
                  <span class="text-body-1">Weight: {{ productWeight }} g</span>
                </div>
              </div>
              
              <!-- Stock Information -->
              <div class="mt-2">
                <div class="d-flex align-center">
                  <v-icon color="grey-darken-1" class="mr-2">mdi-package-variant-closed</v-icon>
                  <span class="text-body-1">
                    Stock: <strong>{{ realTimeStock }}</strong> pcs
                  </span>
                </div>
              </div>
              
              <!-- Quantity and Add to Cart -->
              <div class="mt-6">
                <div class="d-flex flex-wrap align-center">
                  <v-text-field
                    v-model="quantity"
                    type="number"
                    min="1"
                    :max="realTimeStock"
                    hide-details
                    density="compact"
                    class="quantity-input quantity-width mr-4 mb-2 mb-sm-0"
                    @input="validateQuantity"
                    label="Qty"
                    variant="outlined"
                    color="deep-purple"
                    bg-color="grey-lighten-4"
                    :disabled="realTimeStock <= 0"
                  ></v-text-field>

                  <v-btn
                    color="deep-purple"
                    :disabled="realTimeStock <= 0 || quantity <= 0 || cartStore.isLoading"
                    :loading="cartStore.isLoading"
                    @click="addToCart"
                    class="add-to-cart-btn"
                    prepend-icon="mdi-cart-plus"
                    elevation="1"
                  >
                    Add to Cart
                  </v-btn>
                </div>
              </div>
              
              <!-- Product Description (Collapsible) -->
              <div class="mt-4">
                <v-expansion-panels variant="accordion" elevation="1">
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <h3 class="text-subtitle-1 font-weight-bold">Description</h3>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <div v-html="product.description"></div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
      
      <!-- Other Variants Section -->
      <v-container v-if="hasMultipleVariants" class="mt-8 px-0">
        <h2 class="text-h5 font-weight-bold mb-4">Other Variants</h2>
        
        <!-- No Variants Message -->
        <v-card 
          v-if="relatedProducts.length === 0"
          class="pa-6 text-center mb-4"
          color="grey-lighten-4"
          elevation="0"
          border
        >
          <v-icon 
            size="64"
            color="grey-lighten-1"
            class="mb-4"
          >
            mdi-package-variant
          </v-icon>
          <h3 class="text-h6 mb-2">No Other Variants Available</h3>
          <p class="text-body-2 text-grey">This product doesn't have any additional variants.</p>
        </v-card>
        
        <!-- Variants Gallery -->
        <v-row v-else>
          <v-col 
            v-for="relatedProduct in relatedProducts"
            :key="relatedProduct.item_id"
            cols="6" sm="4" md="3" lg="2"
            class="variant-col"
          >
            <v-hover v-slot="{ isHovering, props }">
              <v-card
                v-bind="props"
                class="variant-card"
                @click="navigateToProduct(relatedProduct.item_id)"
                :elevation="isHovering ? 3 : 1"
                :class="{'out-of-stock-card': getVariantRealTimeStock(relatedProduct) <= 0}"
              >
                <div class="position-relative">
                  <v-img
                    :src="relatedProduct.images?.[0]?.thumbnail || productStore.placeholderImage"
                    :aspect-ratio="1"
                  >
                    <template v-slot:placeholder>
                      <v-row class="fill-height ma-0" align="center" justify="center">
                        <v-progress-circular indeterminate color="grey-lighten-5"></v-progress-circular>
                      </v-row>
                    </template>
                  </v-img>
                  
                  <!-- Out of Stock Badge for Variant -->
                  <div v-if="getVariantRealTimeStock(relatedProduct) <= 0"
                       class="stock-badge out-of-stock">
                    Out of Stock
                  </div>
                </div>
                
                <v-card-text class="pa-3">
                  <div class="text-subtitle-2 text-truncate font-weight-medium">
                    {{ formatVariationValues(relatedProduct.variation_values).join(' ') }}
                  </div>
                  <div class="d-flex align-center mt-1">
                    <v-icon
                      v-if="discountPercentage > 0"
                      size="x-small"
                      color="red"
                      class="mr-1"
                    >
                      mdi-ticket-percent-outline
                    </v-icon>
                    <div
                      class="text-subtitle-1 font-weight-bold"
                      :class="discountPercentage > 0 ? 'text-red' : 'deep-purple--text'"
                    >
                      <div v-if="discountPercentage > 0" class="text-caption text-grey text-decoration-line-through">
                        {{ formatPrice(relatedProduct.sell_price) }}
                      </div>
                      {{ formatPrice(calculateDiscountedPrice(relatedProduct.sell_price)) }}
                    </div>
                  </div>
                  <div class="text-caption">
                    <span :class="{'text-error': getVariantRealTimeStock(relatedProduct) <= 0}">
                      Total Stock: {{ getVariantRealTimeStock(relatedProduct) }} pcs
                    </span>
                    <v-tooltip location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-icon v-bind="props" size="x-small" class="ml-1 text-deep-purple-lighten-2">mdi-information-outline</v-icon>
                      </template>
                      <span>Includes items that may be on order or reserved</span>
                    </v-tooltip>
                  </div>
                </v-card-text>
              </v-card>
            </v-hover>
          </v-col>
        </v-row>
      </v-container>
    </div>
    
    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
    >
      {{ snackbar.text }}
      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="snackbar.show = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>

    <div v-if="showCartAnimation" class="cart-animation-overlay">
      <div 
        class="cart-animation-item"
        :class="{ 'animate-to-cart': showCartAnimation }"
        :style="cartAnimationStyle"
      >
        <v-img
          :src="selectedImage || productStore.placeholderImage"
          width="60"
          height="60"
          class="cart-item-image"
        />
        <v-icon class="cart-icon">mdi-cart-plus</v-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductStore } from '~/stores/product'
import { useCartStore } from '~/stores/cart'
import { useAuthStore } from '~/stores/auth'
import { useProductDiscountStore } from '~/stores/product-discount'
import { useCartAnimation } from '~/composables/useCartAnimation'
import { useDiscount } from '~/composables/useDiscount'
import type { AddToCartPayload } from '~/schemas/otoapi/cart'
import type { JubelioProductDetail, JubelioProductToSell, JubelioProductSku } from '~/schemas/jubelio/product'
import type { VariationValue } from '~/schemas/jubelio/common'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Product Detail | Otoresell'
});

// ==========================================
// STORES AND COMPOSABLES
// ==========================================
const productStore = useProductStore()
const cartStore = useCartStore()
const authStore = useAuthStore()
const productDiscountStore = useProductDiscountStore()
const route = useRoute()
const router = useRouter()
// Use the composable for cart position
const { getCartIconPosition } = useCartAnimation()

// ==========================================
// STATE MANAGEMENT
// ==========================================
// Basic state
const quantity = ref<number>(1)
const selectedVariantId = ref<string | number | null>(null)
const snackbar = ref({
  show: false,
  text: '',
  color: 'success'
})
const pendingFetchRequests = ref(new Set())
const showCartAnimation = ref(false)
const animationStartPosition = ref({ x: 0, y: 0 })
const animationEndPosition = ref({ x: 0, y: 0 })

// Loading and error state
const isLoading = computed(() => productStore.isProductLoading)
const hasError = computed(() => productStore.hasProductError)

// Product data state
const productDetailsLoaded = ref(false)
const additionalDetailsLoaded = ref(false)

// ==========================================
// COMPUTED PROPERTIES
// ==========================================
// Product data
const product = computed(() => productStore.currentProduct)

const productDiscount = computed(() => {
  if (!product.value) return null
  return productDiscountStore.discounts.get(product.value.item_group_id)
})

const productMaxDiscount = computed(() => productDiscount.value?.max_discount_percentage);

const { discountPercentage, calculateDiscountedPrice } = useDiscount(
  productMaxDiscount
);

const discountedPrice = computed(() => {
  const price = selectedVariant.value?.sell_price
  return calculateDiscountedPrice(price as number)
})

const selectedVariant = computed((): JubelioProductSku | null => {
  if (!product.value || !selectedVariantId.value) return null
  const variant = product.value.product_skus.find(sku => sku.item_id == selectedVariantId.value)
  return variant || null
})

const hasMultipleVariants = computed(() => {
  return product.value && product.value.product_skus && product.value.product_skus.length > 1;
});

const relatedProducts = computed((): JubelioProductSku[] => {
  if (!product.value || !product.value.product_skus) return []
  
  // Filter out the currently selected variant
  return product.value.product_skus.filter(sku => sku.item_id != selectedVariantId.value)
})

// Product details
const selectedImage = computed(() => {
  if (!selectedVariant.value || !selectedVariant.value.images || selectedVariant.value.images.length === 0) {
    return null
  }
  
  const image = selectedVariant.value.images[0]
  return image.cloud_key && image.cloud_key !== ""
      ? image.cloud_key
      : image.thumbnail
})

const productWeight = computed(() => {
  // First try to get weight from additional details
  if (productStore.currentProductToSell && productStore.currentProductToSell.weight_in_gram) {
    return parseFloat(productStore.currentProductToSell.weight_in_gram.toString())
  }
  
  // Fallback to package_weight from the original product data
  if (product.value && product.value.package_weight) {
    return parseFloat(product.value.package_weight.toString())
  }
  
  // Default weight if nothing is available
  return 500
})

// Stock calculation
const realTimeStock = computed(() => {
  if (!selectedVariant.value) return 0
  
  // Check if currentProductToSell exists first
  if (!productStore.currentProductToSell) return 0
  
  // Use available_qty from currentProductToSell
  const baseStock = productStore.currentProductToSell.available_qty || 0
  
  const inCartQty = cartStore.getItemQuantityInCart(selectedVariant.value.item_id)
  
  // If available_qty is null, treat as out of stock
  if (baseStock === null) return 0
  
  // Calculate real-time stock by subtracting what's in cart
  return Math.max(0, Number(baseStock) - inCartQty)
})

const cartAnimationStyle = computed(() => {
  if (!showCartAnimation.value) return {}
  
  return {
    '--start-x': `${animationStartPosition.value.x}px`,
    '--start-y': `${animationStartPosition.value.y}px`,
    '--end-x': `${animationEndPosition.value.x}px`,
    '--end-y': `${animationEndPosition.value.y}px`,
  }
})

// ==========================================
// METHODS
// ==========================================
// Data fetching
const fetchData = async () => {
  const productId: string = route.params.id as string
  if (!productId) return
  
  try {
    // Reset loading flags
    productDetailsLoaded.value = false
    additionalDetailsLoaded.value = false
    
    // Fetch product details
    await fetchProductDetail(productId)
  } catch (error) {
    console.error('Failed to fetch product data:', error)
  }
}

const fetchProductDetail = async (productId: string) => {
  if (productDetailsLoaded.value) return
  
  try {
    const fetchedProduct = await productStore.fetchProductDetail(productId)
    productDetailsLoaded.value = true
    
    if (fetchedProduct) {
      await productDiscountStore.fetchBatchProductDiscounts([fetchedProduct.item_group_id.toString()])
    }
    
    // Initialize with the current product ID as the selected variant
    selectVariant(productId)
  } catch (error) {
    console.error('Failed to fetch product detail:', error)
  }
}

const fetchAdditionalProductDetails = async (itemCode: string) => {
  if (!itemCode || additionalDetailsLoaded.value) return
  
  // Skip if we're already fetching this item code
  if (pendingFetchRequests.value.has(itemCode)) return
  
  pendingFetchRequests.value.add(itemCode)
  
  try {
    await productStore.fetchProductToSell(itemCode)
    additionalDetailsLoaded.value = true
  } catch (error) {
    console.error('Failed to fetch additional product details:', error)
  } finally {
    pendingFetchRequests.value.delete(itemCode)
  }
}

// UI Helpers
const formatVariationValues = (variationValues: VariationValue[] | undefined) => {
  if (!variationValues || !Array.isArray(variationValues)) return []
  return variationValues.map(v => v.value)
}

const formatPrice = (price: number | string | null | undefined) => {
  return productStore.formatPrice(price)
}

const getVariantRealTimeStock = (variant: JubelioProductSku): number => {
  if (!variant) return 0
  
  const baseStock = variant.end_qty || 0
  const inCartQty = cartStore.getItemQuantityInCart(variant.item_id)
  
  // If end_qty is null, treat as out of stock
  if (baseStock === null) return 0
  
  // Calculate real-time stock
  return Math.max(0, baseStock - inCartQty)
}

// Product interaction
const selectVariant = (variantId: string | number) => {
  selectedVariantId.value = variantId
  
  // Reset quantity
  quantity.value = 1
  
  // Only fetch additional details if not already loaded
  if (!additionalDetailsLoaded.value && selectedVariant.value?.item_code) {
    fetchAdditionalProductDetails(selectedVariant.value.item_code)
  }
}

const validateQuantity = () => {
  // Use real-time stock for validation
  const max = realTimeStock.value
  
  // Ensure quantity is a number
  let qty = Number(quantity.value)
  if (isNaN(qty) || qty < 1) {
    qty = 1
  } else if (qty > max) {
    qty = max
  }
  
  quantity.value = qty
}

const addToCart = async () => {
  if (!selectedVariant.value || !product.value) {
    showSnackbar('Please select a variant', 'error')
    return
  }
  if (!productStore.currentProductToSell) {
    showSnackbar('Product details are not available at the moment.', 'error')
    return
  }

  if (realTimeStock.value <= 0) {
    showSnackbar('This product is out of stock', 'error')
    return
  }

  try {
    await nextTick()
    calculateAnimationPositions()
    showCartAnimation.value = true

    const productPayload: AddToCartPayload = {
      jubelio_item_id: selectedVariant.value.item_id,
      jubelio_item_group_id: product.value!.item_group_id,
      sku: selectedVariant.value.item_code,
      name: product.value!.item_group_name,
      price: selectedVariant.value.sell_price,
      quantity: Number(quantity.value),
      weight: productWeight.value,
      unit: productStore.currentProductToSell.sell_unit || product.value!.sell_unit,
      variant: selectedVariant.value.variation_values,
      image: selectedVariant.value.images?.[0]?.thumbnail || productStore.placeholderImage,
      tax_id: productStore.currentProductToSell.sell_tax_id,
      tax_rate_percent: productStore.currentProductToSell.rate ? parseFloat(productStore.currentProductToSell.rate.toString()) : 0
    }

    await cartStore.addItem(productPayload)

    showSnackbar('Added to cart successfully', 'success')
    quantity.value = 1

    setTimeout(() => {
      showCartAnimation.value = false
    }, 1400)
  } catch (error) {
    showCartAnimation.value = false
    console.error('Failed to add to cart:', error)
    showSnackbar('Failed to add to cart. Please try again.', 'error')
  }
}

// Add this new function to calculate animation positions
const calculateAnimationPositions = () => {
  try {
    // Get cart icon position from header
    const cartPosition = getCartIconPosition() || {
      x: window.innerWidth - 100,
      y: 70
    }
    
    // Get the "Add to Cart" button position as start point
    const addToCartBtn = document.querySelector('.add-to-cart-btn')
    let startPosition = { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    
    if (addToCartBtn) {
      const btnRect = addToCartBtn.getBoundingClientRect()
      startPosition = {
        x: btnRect.left + btnRect.width / 2,
        y: btnRect.top + btnRect.height / 2
      }
    }
    
    // Set start position (Add to Cart button)
    animationStartPosition.value = startPosition
    
    // Set end position (cart icon in header)
    animationEndPosition.value = cartPosition
  } catch (error) {
    console.warn('Failed to calculate animation positions:', error)
    // Fallback positions
    animationStartPosition.value = { x: window.innerWidth / 2, y: window.innerHeight / 2 }
    animationEndPosition.value = { x: window.innerWidth - 100, y: 70 }
  }
}

// Navigation
const navigateToProduct = (productId: string | number) => {
  router.push(`/products/${productId}`)
}

const goBackToProducts = () => {
  router.push('/products');
}

// Notifications
const showSnackbar = (text: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    text,
    color
  }
}

// ==========================================
// LIFECYCLE HOOKS
// ==========================================
onMounted(() => {
  fetchData()
})

// Clean up
onBeforeUnmount(() => {
  // Clear product detail when leaving the page
  productStore.clearProductDetail()
  productDetailsLoaded.value = false
  additionalDetailsLoaded.value = false
})

// ==========================================
// WATCHERS
// ==========================================
// Watch for route changes to reload product
watch(() => route.params.id, (newId, oldId) => {
  if (newId !== oldId) {
    productDetailsLoaded.value = false
    additionalDetailsLoaded.value = false
    fetchData()
  }
})

// Watch for changes in the selected variant
watch(selectedVariant, (newVariant) => {
  if (newVariant && newVariant.item_code && !additionalDetailsLoaded.value) {
    fetchAdditionalProductDetails(newVariant.item_code)
  }
})
</script>

<style scoped>
.product-title {
  line-height: 1.3;
}

.quantity-input {
  background-color: #f5f5f5;
  border-radius: 8px;
}

.quantity-width {
  width: 80px;
}

.add-to-cart-btn {
  min-width: 150px;
  border-radius: 8px;
  text-transform: none;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 5px rgba(103, 58, 183, 0.2);
}

.position-relative {
  position: relative;
}

.stock-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
  color: white;
}

.stock-badge.out-of-stock {
  background-color: rgba(244, 67, 54, 0.9); /* Red */
}

.variant-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  cursor: pointer;
}

.variant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.out-of-stock-card {
  opacity: 0.8;
}

.variant-col {
  padding: 8px;
}

/* Responsive styles */
@media (max-width: 600px) {
  .add-to-cart-btn {
    min-width: 120px;
    width: 100%;
  }
}


/* Add to style section */
.cart-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9999;
}

.cart-animation-item {
  position: fixed;
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10000;
  /* Position will be set dynamically via CSS variables */
  left: var(--start-x);
  top: var(--start-y);
  transform: translate(-50%, -50%);
}

.cart-animation-item.animate-to-cart {
  animation: cartFlyToCart 1.2s ease-out forwards;
}

@keyframes cartFlyToCart {
  0% {
    opacity: 0;
    left: var(--start-x);
    top: var(--start-y);
    transform: translate(-50%, -50%) scale(0.8);
  }
  15% {
    opacity: 1;
    left: var(--start-x);
    top: var(--start-y);
    transform: translate(-50%, -50%) scale(1);
  }
  85% {
    opacity: 1;
    left: var(--end-x);
    top: var(--end-y);
    transform: translate(-50%, -50%) scale(0.3);
  }
  100% {
    opacity: 0;
    left: var(--end-x);
    top: var(--end-y);
    transform: translate(-50%, -50%) scale(0.1);
  }
}

/* Add responsive adjustments */
@media (max-width: 768px) {
  .cart-animation-item {
    padding: 12px 16px;
    gap: 8px;
  }
  
  .cart-item-image {
    width: 40px !important;
    height: 40px !important;
  }
  
  .cart-icon {
    font-size: 20px;
  }
}

</style>