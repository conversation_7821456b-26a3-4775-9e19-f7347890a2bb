<template>
  <div>
    <!-- Page Title -->
    <PageTitle 
      title="Products" 
      icon="mdi-package-variant-closed" 
      subtitle="Browse our catalog of quality products for your business needs."
    />

    <!-- Search and Filter Section -->
    <v-card class="mb-6 rounded-lg" elevation="0" border>
      <v-card-text class="pa-4">
        <v-text-field
          v-model="searchQuery"
          prepend-inner-icon="mdi-magnify"
          label="Search products by name or SKU..."
          density="comfortable"
          hide-details
          variant="outlined"
          rounded="pill"
          single-line
          color="deep-purple"
          @keyup.enter="handleSearch"
        >
          <template v-slot:append-inner>
            <v-btn
              v-if="searchQuery"
              icon="mdi-close-circle"
              variant="text"
              size="small"
              @click="clearSearch"
              class="mr-2"
              aria-label="Clear search"
            ></v-btn>
            <v-btn
              color="deep-purple"
              variant="flat"
              @click="handleSearch"
              :loading="productStore.loading"
              icon="mdi-magnify"
              size="small"
              aria-label="Search"
              class="rounded-pill"
            >
            </v-btn>
          </template>
        </v-text-field>
      </v-card-text>
      <v-expansion-panels flat>
        <v-expansion-panel>
          <v-expansion-panel-title>
            <template v-slot:default="{ expanded }">
              <v-icon size="small" class="mr-3">mdi-filter-variant</v-icon>
              <span class="text-subtitle-1 font-weight-medium">View, Filter & Sort</span>
              <v-spacer></v-spacer>
              <v-chip v-if="!expanded && activeFilterCount > 0" size="small" color="deep-purple" class="mr-4">
                {{ activeFilterCount }} Active Filter{{ activeFilterCount > 1 ? 's' : '' }}
              </v-chip>
            </template>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-divider class="mb-4"></v-divider>
            <v-row dense>
              <!-- View Mode -->
              <v-col cols="12" md="auto">
                <v-btn-toggle v-model="viewMode" mandatory color="deep-purple" divided>
                  <v-btn value="grid">
                    <v-icon start>mdi-view-grid</v-icon>
                    Grid
                  </v-btn>
                  <v-btn value="list">
                    <v-icon start>mdi-view-list</v-icon>
                    List
                  </v-btn>
                </v-btn-toggle>
              </v-col>
            </v-row>
            <v-row dense class="mt-4">
              <!-- Sort By -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortBy"
                  label="Sort by"
                  :items="sortByOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
              
              <!-- Sort Direction -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortDirection"
                  label="Order"
                  :items="sortDirectionOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>

              <!-- Stock Status Filter -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="stockStatusFilter"
                  label="Stock Status"
                  :items="stockStatusOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                ></v-select>
              </v-col>
            </v-row>
            
            <div class="d-flex justify-end ga-2 mt-4">
              <v-btn
                color="grey"
                variant="text"
                @click="clearFilters"
              >
                Clear All Filters
              </v-btn>
              <v-btn
                color="deep-purple"
                variant="outlined"
                prepend-icon="mdi-refresh"
                @click="loadProducts"
                :loading="productStore.loading"
              >
                Refresh
              </v-btn>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card>

    <!-- Results Summary -->
    <v-card
      v-if="!productStore.loading && displayProducts.length > 0"
      class="mb-6 rounded-lg"
      elevation="1"
      color="grey-lighten-5"
    >
      <v-card-text class="py-3">
        <div class="d-flex flex-column flex-sm-row justify-space-between align-start align-sm-center ga-2">
          <div class="text-body-2 text-grey-darken-1">
            <v-icon size="small" class="mr-1">mdi-information-outline</v-icon>
            Showing <span class="font-weight-bold">{{ displayProducts.length }}</span>
            of <span class="font-weight-bold">{{ productStore.pagination.totalItems }}</span> products
            <span v-if="searchQuery" class="text-deep-purple">
              (filtered by "{{ searchQuery }}")
            </span>
          </div>
          <div class="text-body-2 text-grey-darken-1">
            Page <span class="font-weight-bold">{{ pagination.page }}</span>
            of <span class="font-weight-bold">{{ productStore.pagination.totalPages }}</span>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Products Container -->
    <v-row>
      <!-- Products Grid -->
      <v-col cols="12">
        <!-- Loading State -->
        <div v-if="productStore.loading" class="d-flex justify-center align-center my-8">
          <v-progress-circular indeterminate color="deep-purple"></v-progress-circular>
        </div>
        
        <!-- Error State -->
        <div v-else-if="productStore.hasError" class="text-center my-8">
          <v-icon size="64" color="error">mdi-alert-circle</v-icon>
          <h3 class="text-h6 mt-4">{{ getErrorCode(productStore.error) }}</h3>
          <v-btn color="deep-purple" class="mt-4" @click="loadProducts">Try Again</v-btn>
        </div>

        <!-- Empty State - No Products Found -->
        <div v-else-if="displayProducts.length === 0" class="text-center my-8">
          <v-icon size="64" color="grey-lighten-1">mdi-shopping-search</v-icon>
          <h3 class="text-h6 mt-4">No products found</h3>
          <p class="text-body-2 text-grey">Try adjusting your search criteria</p>
          <v-btn color="deep-purple" class="mt-4" @click="clearSearch">Clear Search</v-btn>
        </div>
        
        <!-- Grid View -->
        <v-row v-else-if="viewMode === 'grid'" class="product-grid">
          <v-col
            v-for="product in displayProducts"
            :key="product.id"
            cols="12"
            sm="6"
            md="4"
            lg="3"
            xl="2"
            class="product-col"
          >
            <v-hover v-slot="{ isHovering, props }">
              <v-card
                v-bind="props"
                class="product-card"
                @click="navigateToProduct(product.id)"
                :elevation="isHovering ? 4 : 1"
              >
                <div class="position-relative pa-3">
                  <v-img
                    :src="product.image"
                    :aspect-ratio="1"
                    class="product-image mx-auto"
                    max-width="256"
                  >
                    <template v-slot:placeholder>
                      <v-row class="fill-height ma-0" align="center" justify="center">
                        <v-progress-circular indeterminate color="grey-lighten-5"></v-progress-circular>
                      </v-row>
                    </template>
                  </v-img>
                  
                  <!-- Out of Stock Badge -->
                  <div v-if="product.allVariantsOutOfStock" class="stock-badge out-of-stock">
                    Out of Stock
                  </div>
                </div>
                
                <v-card-text class="pa-3 pt-0">
                  <div class="d-flex flex-column">
                    <!-- Variant Count -->
                    <span class="text-caption text-deep-purple-darken-1 font-weight-medium mb-1">
                      {{ product.variantCount }} variants
                    </span>
                    
                    <!-- Product Name -->
                    <v-tooltip location="top" :text="product.name">
                      <template v-slot:activator="{ props }">
                        <span v-bind="props" class="text-subtitle-1 font-weight-medium product-name">
                          {{ product.name }}
                        </span>
                      </template>
                    </v-tooltip>
                    
                    <!-- Price Range -->
                    <div class="d-flex flex-column align-start justify-space-between mt-2">
                      <span
                        v-if="product.discountPercentage > 0"
                        class="text-caption text-grey text-decoration-line-through"
                      >
                        {{ product.priceRange }}
                      </span>
                      <div class="d-flex align-center" :class="{ 'text-grey': product.allVariantsOutOfStock }">
                        <v-icon
                          v-if="product.discountPercentage > 0"
                          size="small"
                          color="red"
                          class="mr-1"
                        >
                          mdi-ticket-percent-outline
                        </v-icon>
                        <span
                          class="text-subtitle-1 font-weight-bold"
                          :class="{ 'text-red': product.discountPercentage > 0, 'deep-purple--text': !product.allVariantsOutOfStock && product.discountPercentage === 0 }"
                        >
                          {{ product.discountPercentage > 0 ? product.discountedPriceRange : product.priceRange }}
                        </span>
                      </div>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-hover>
          </v-col>
        </v-row>
        
        <!-- List View -->
        <v-card v-else elevation="1" class="list-view-card">
          <div class="table-responsive">
            <v-table>
              <thead>
                <tr>
                  <th class="text-left">Image</th>
                  <th class="text-left">Product Name</th>
                  <th class="text-left hide-sm-and-down">Variants</th>
                  <th class="text-left">Price Range</th>
                  <th class="text-left hide-sm-and-down">Status</th>
                  <th class="text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="product in displayProducts"
                  :key="product.id"
                  @click="navigateToProduct(product.id)"
                  class="product-row"
                  :class="{'out-of-stock-row': product.allVariantsOutOfStock}"
                >
                  <td class="py-3">
                    <div class="image-container">
                      <v-img
                        :src="product.image"
                        :aspect-ratio="1"
                        width="80"
                      ></v-img>
                    </div>
                  </td>
                  <td class="py-3">
                    <v-tooltip location="top" :text="product.name">
                      <template v-slot:activator="{ props }">
                        <span v-bind="props" class="product-name-list">{{ product.name }}</span>
                      </template>
                    </v-tooltip>
                  </td>
                  <td class="py-3 hide-sm-and-down">{{ product.variantCount }} variants</td>
                  <td class="py-3" :class="{'text-grey': product.allVariantsOutOfStock}">
                    <div class="d-flex flex-column">
                      <span
                        v-if="product.discountPercentage > 0"
                        class="text-caption text-grey text-decoration-line-through"
                      >
                        {{ product.priceRange }}
                      </span>
                      <div class="d-flex align-center" :class="{ 'text-grey': product.allVariantsOutOfStock }">
                        <v-icon
                          v-if="product.discountPercentage > 0"
                          size="small"
                          color="red"
                          class="mr-1"
                        >
                          mdi-ticket-percent-outline
                        </v-icon>
                        <span
                          :class="{ 'text-red font-weight-bold': product.discountPercentage > 0 }"
                        >
                          {{ product.discountPercentage > 0 ? product.discountedPriceRange : product.priceRange }}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td class="py-3 hide-sm-and-down">
                    <v-chip
                      size="small"
                      :color="product.allVariantsOutOfStock ? 'error' : 'success'"
                      :text="product.allVariantsOutOfStock ? 'Out of Stock' : 'In Stock'"
                      class="text-caption"
                    ></v-chip>
                  </td>
                  <td class="py-3" @click.stop>
                    <v-btn
                      icon="mdi-eye"
                      variant="tonal"
                      color="deep-purple"
                      size="small"
                      @click="navigateToProduct(product.id)"
                    ></v-btn>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </div>
        </v-card>
        
        <!-- Pagination -->
        <div v-if="!productStore.hasError && displayProducts.length > 0" class="d-flex justify-center mt-6">
          <client-only>
            <v-pagination
              v-model="pagination.page"
              :length="productStore.pagination.totalPages"
              :total-visible="isMobile ? 5 : 10"
              rounded="circle"
              color="deep-purple"
              @update:model-value="handlePageChange"
            ></v-pagination>
          </client-only>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useProductStore } from '~/stores/product';
import { useProductDiscountStore } from '~/stores/product-discount';
import { useAuthStore } from '~/stores/auth';
import { logger } from '~/utils/logger';

// Store
const productStore = useProductStore()
const productDiscountStore = useProductDiscountStore()
const authStore = useAuthStore()
const { searchQuery, sortBy, sortDirection, viewMode, pagination } = storeToRefs(productStore);

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
});

useHead({
  title: 'Products | Otoresell'
});

// UI State
const stockStatusFilter = ref('haveStock');

const stockStatusOptions = [
  { title: 'All', value: 'all' },
  { title: 'Have Stock', value: 'haveStock' },
  { title: 'Out of Stock', value: 'outOfStock' }
];


const itemsPerPage = ref(60);

const showSnackbar = ref(false);
const snackbarMessage = ref('');
const isMobile = ref(false);

// Sort options
const sortByOptions = [
  { title: 'Last Modified', value: 'last_modified' },
  { title: 'Group ID', value: 'item_group_id' },
  { title: 'Name', value: 'item_name' },
  { title: 'Price', value: 'sell_price' },
];

const sortDirectionOptions = [
  { title: 'Newest First', value: 'DESC' },
  { title: 'Oldest First', value: 'ASC' }
];

// Computed properties
const displayProducts = computed(() => {
  const allProducts = productStore.getFormattedInventoryItems(productDiscountStore.discounts);

  if (stockStatusFilter.value === 'haveStock') {
    return allProducts.filter(product => !product.allVariantsOutOfStock);
  }
  if (stockStatusFilter.value === 'outOfStock') {
    return allProducts.filter(product => product.allVariantsOutOfStock);
  }
  // This handles the 'all' case
  return allProducts;
});

const activeFilterCount = computed(() => {
  let count = 0;
  if (searchQuery.value.trim()) {
    count++;
  }
  if (sortBy.value !== 'last_modified') {
    count++;
  }
  if (sortDirection.value !== 'DESC') {
    count++;
  }
  if (viewMode.value !== 'grid') {
    count++;
  }
  if (stockStatusFilter.value !== 'haveStock') {
    count++;
  }
  return count;
});

// Methods
const loadProducts = async () => {
  try {
    await productStore.fetchInventoryItems({
      page: pagination.value.page,
      pageSize: itemsPerPage.value,
      sortDirection: sortDirection.value,
      sortBy: sortBy.value === 'none' ? undefined : sortBy.value,
      q: searchQuery.value.trim()
    });

    if (productStore.productsGroup.length > 0) {
      const itemGroupIds = productStore.productsGroup.map(p => p.item_group_id.toString());
      await productDiscountStore.fetchBatchProductDiscounts(itemGroupIds);
    }

    await nextTick();
    if (import.meta.client) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  } catch (error) {
    logger.error('Failed to load products:', error);
  }
};

const handleSearch = () => {
  pagination.value.page = 1;
  loadProducts();
};

const clearSearch = async () => {
  searchQuery.value = '';
  pagination.value.page = 1;
  await loadProducts();
};

const handleFilterChange = () => {
  pagination.value.page = 1;
  loadProducts();
};

const clearFilters = () => {
  searchQuery.value = '';
  sortBy.value = 'last_modified';
  sortDirection.value = 'DESC';
  viewMode.value = 'grid';
  stockStatusFilter.value = 'haveStock';
  pagination.value.page = 1;
  loadProducts();
};

const navigateToProduct = (productId: number | string) => {
  navigateTo(`/products/${productId}`);
};

const handlePageChange = async (page: number) => {
  pagination.value.page = page;
  await loadProducts();
};

// Helper functions for error handling
const getErrorCode = (errorMessage: string | null): string => {
  if (!errorMessage) return 'Error Loading Products';
  
  // Check for common HTTP error codes
  if (errorMessage.includes('500')) return '500 Server Error';
  if (errorMessage.includes('404')) return '404 Not Found';
  if (errorMessage.includes('403')) return '403 Forbidden';
  if (errorMessage.includes('401')) return '401 Unauthorized';
  if (errorMessage.includes('400')) return '400 Bad Request';
  
  return 'Error Loading Products';
};

// Check if device is mobile
const checkIfMobile = () => {
  if (import.meta.client) {
    isMobile.value = window.innerWidth < 768;
  }
};

// Lifecycle
onMounted(() => {
  if (import.meta.client) {
    // Check if mobile on initial load
    checkIfMobile();
    
    // Add resize event listener for responsive behavior
    window.addEventListener('resize', checkIfMobile);
  }
  
  loadProducts();
});

// Clean up event listeners
onBeforeUnmount(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', checkIfMobile);
    
    // No timeout to clear anymore
  }
});

// Watch for changes in pagination




watch(searchQuery, (newValue, oldValue) => {
  if (newValue === '' && oldValue !== '') {
    handleSearch();
  }
});
</script>

<style scoped>
.sort-select {
  width: 200px;
}

.search-card {
  background-color: transparent;
  border: none;
}

.product-grid {
  margin: 0 -12px;
}

.product-col {
  padding: 12px;
}

.product-card {
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.product-image {
  transition: transform 0.3s ease-in-out;
  /* Promote hardware acceleration to prevent flickering/blur during animation */
  backface-visibility: hidden;
  transform: translateZ(0);
  /* Hint for browsers to prioritize image quality */
  image-rendering: -webkit-optimize-contrast;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.stock-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
  color: white;
}

.stock-badge.in-stock {
  background-color: rgba(76, 175, 80, 0.9); /* Green */
}

.stock-badge.low-stock {
  background-color: rgba(255, 152, 0, 0.9); /* Orange */
}

.stock-badge.out-of-stock {
  background-color: rgba(244, 67, 54, 0.9); /* Red */
}

.product-name {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 24px;
  margin-bottom: 4px;
}

.product-name-list {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.list-view-card {
  border-radius: 8px;
  overflow: hidden;
}

.product-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.product-row:hover {
  background-color: rgba(103, 58, 183, 0.05);
}

.out-of-stock-row {
  background-color: rgba(244, 67, 54, 0.05);
}

.product-row td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.image-container {
  padding: 4px;
  margin: 4px 0;
}

.v-table {
  border-spacing: 0;
  border-collapse: separate;
}

.v-table tbody tr:not(:last-child) {
  margin-bottom: 8px;
}

.table-responsive {
  overflow-x: auto;
}

/* Hide elements on small screens */
@media (max-width: 767px) {
  .hide-sm-and-down {
    display: none;
  }
  
  .sort-select {
    width: 150px;
  }
}

@media (max-width: 960px) {
  .product-col {
    padding: 8px;
  }
}
</style>
