<template>
  <div>
    <!-- Page Title -->
    <PageTitle
      title="Account Settings"
      icon="mdi-account-cog"
      subtitle="Manage your personal information, addresses, and account preferences."
    />

    <!-- Tabs for navigation -->
    <v-tabs v-model="activeTab" bg-color="transparent" color="deep-purple" class="mb-6">
      <v-tab value="profile" class="text-body-1">
        <v-icon start>mdi-account-outline</v-icon>
        Profile
      </v-tab>
      <v-tab value="password" class="text-body-1">
        <v-icon start>mdi-lock-reset</v-icon>
        Change Password
      </v-tab>
      <v-tab :to="'/addresses'" class="text-body-1">
        <v-icon start>mdi-map-marker-outline</v-icon>
        Addresses
      </v-tab>
    </v-tabs>

    <!-- Tab content -->
    <v-window v-model="activeTab">
      <v-window-item value="profile">
        <Profile />
      </v-window-item>
      <v-window-item value="password">
        <ChangePassword />
      </v-window-item>
    </v-window>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Profile from '~/components/settings/Profile.vue'
import ChangePassword from '~/components/settings/ChangePassword.vue'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
})

useHead({
  title: 'Account Settings | Otoresell'
})

const activeTab = ref('profile')
</script>
