<template>
  <div>
    <!-- Back Button -->
    <v-btn 
      to="/transactions" 
      color="deep-purple" 
      variant="text" 
      class="mb-4"
      prepend-icon="mdi-arrow-left"
    >
      Back to Transactions
    </v-btn>

    <!-- Loading Skeleton -->
    <div v-if="loading">
      <v-skeleton-loader type="article, actions"></v-skeleton-loader>
      <v-skeleton-loader type="table"></v-skeleton-loader>
    </div>

    <!-- Error Message -->
    <v-alert v-else-if="error" type="error" prominent>
      <template #title>Error Loading Order</template>
      {{ error }}
    </v-alert>

    <!-- Order Detail Display -->
    <div v-else-if="order">
      <v-row>
        <v-col cols="12" md="8">
          <!-- Order Items Card -->
          <v-card class="mb-4">
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="deep-purple">mdi-package-variant-closed</v-icon>
              Items in Order #{{ order.jubelio_order_id }}
            </v-card-title>
            <v-divider></v-divider>
            <v-list>
              <v-list-item v-for="item in order.items" :key="item.id">
                <template #prepend>
                  <v-img :src="item.image || '/images/placeholder.png'" width="60" height="60" class="rounded mr-4" :alt="item.name"></v-img>
                </template>
                <v-list-item-title class="font-weight-medium">
                  <v-tooltip activator="parent" location="top" open-delay="300">
                    <span>{{ item.name }}</span>
                  </v-tooltip>
                  <router-link
                    :to="`/products/${item.jubelio_item_id}`"
                    class="text-decoration-none text-deep-purple--text d-inline-block text-truncate"
                    style="max-width: 450px;"
                  >
                    {{ item.name }}
                  </router-link>
                </v-list-item-title>
                <v-list-item-subtitle class="text-caption text-grey-darken-1 mt-1">
                  SKU: {{ item.sku }}
                </v-list-item-subtitle>

                <template #append>
                  <div class="text-right" style="min-width: 150px;">
                    <div class="text-body-2 font-weight-medium">
                      <span :class="(item.discount_percentage ?? 0) > 0 ? 'text-red' : 'text-deep-purple-darken-2'">
                        {{ formatPrice(item.price) }}
                      </span>
                      <span class="text-grey-darken-1"> x {{ item.quantity }}</span>
                    </div>

                    <div v-if="(item.discount_percentage ?? 0) > 0" class="text-caption text-grey text-decoration-line-through">
                      {{ formatPrice(parseFloat(item.original_price ?? '0') * item.quantity) }}
                    </div>

                    <div class="text-subtitle-1 font-weight-bold" :class="(item.discount_percentage ?? 0) > 0 ? 'text-red' : 'text-grey-darken-3'">
                      {{ formatPrice(parseFloat(item.price) * item.quantity) }}
                    </div>
                  </div>
                </template>
              </v-list-item>
            </v-list>
          </v-card>

          <!-- Shipping Information Card -->
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="deep-purple">mdi-truck-delivery-outline</v-icon>
              Shipping Details
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <v-list lines="two" class="py-0">
                <v-list-item class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" class="mr-4">
                      <v-icon color="grey-darken-1">mdi-map-marker-outline</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Shipping Address</div>
                    <div class="font-weight-medium">{{ order.shipping_full_name }}</div>
                    <div>{{ order.shipping_phone }}</div>
                    <div>{{ order.shipping_address }}</div>
                    <div>{{ order.shipping_subdistrict }}, {{ order.shipping_district }}</div>
                    <div>{{ order.shipping_city }}, {{ order.shipping_province }} {{ order.shipping_post_code }}</div>
                  </div>
                </v-list-item>
              </v-list>
              <v-divider class="my-3"></v-divider>
              <v-list lines="one" density="compact" class="py-0">
                <v-list-item v-if="order.shipping_type" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" size="32" class="mr-4">
                      <v-icon color="grey-darken-1" size="small">mdi-package-variant</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Shipping Type</div>
                    <div class="font-weight-medium text-capitalize">{{ order.shipping_type.replace('_', ' ') }}</div>
                  </div>
                </v-list-item>
                <v-list-item v-if="order.expedition" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" size="32" class="mr-4">
                      <v-icon color="grey-darken-1" size="small">mdi-truck-outline</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Shipper</div>
                    <div class="font-weight-medium">{{ order.expedition.name }} ({{ order.expedition.service }})</div>
                  </div>
                </v-list-item>
                <v-list-item v-if="order.tracking_number" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" size="32" class="mr-4">
                      <v-icon color="grey-darken-1" size="small">mdi-barcode-scan</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Tracking Number</div>
                    <div class="font-weight-medium">{{ order.tracking_number }}</div>
                  </div>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="4">
          <!-- Order Summary Card -->
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2" color="deep-purple">mdi-receipt-text-outline</v-icon>
              Order Summary
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text>
              <div class="d-flex justify-space-between mb-2">
                <span>Status:</span>
                <v-chip :color="getStatusColor(order.status)" size="small" class="text-capitalize">
                  {{ order.status }}
                </v-chip>
              </div>
              <div class="d-flex justify-space-between mb-2">
                <span>Order Date:</span>
                <span class="font-weight-medium">{{ formatDate(order.created_at) }}</span>
              </div>
              <div class="d-flex justify-space-between mb-3">
                <span>Payment Method:</span>
                <span class="font-weight-medium text-capitalize">{{ order.payment_method?.replace('_', ' ') }}</span>
              </div>

              <!-- Countdown Timer -->
              <div v-if="order.status === 'pending' && order.expired_at" class="my-3">
                <v-divider></v-divider>
                <div class="d-flex justify-space-between align-center mt-3">
                  <span class="font-weight-medium">Time left to pay:</span>
                  <CountdownTimer :expiry-date="order.expired_at" />
                </div>
              </div>

              <v-divider></v-divider>
              <div class="py-2">
                <div class="d-flex justify-space-between mb-2">
                  <span>Subtotal:</span>
                  <span>{{ formatPrice(order.sub_total) }}</span>
                </div>
                <div class="d-flex justify-space-between mb-2">
                  <span>Shipping:</span>
                  <span>{{ formatPrice(order.shipping_cost) }}</span>
                </div>
                <div v-if="parseFloat(order.insurance_cost) > 0" class="d-flex justify-space-between mb-2">
                  <span>Insurance:</span>
                  <span>{{ formatPrice(order.insurance_cost) }}</span>
                </div>
                <div v-if="parseFloat(order.total_discount) > 0" class="d-flex justify-space-between mb-2 text-success">
                  <span>Discount:</span>
                  <span>-{{ formatPrice(order.total_discount) }}</span>
                </div>
                <v-divider></v-divider>
                <div class="d-flex justify-space-between align-center mt-2">
                  <span class="text-h6 font-weight-bold">Total</span>
                  <span class="text-h6 font-weight-bold text-deep-purple">{{ formatPrice(order.grand_total) }}</span>
                </div>
              </div>
            </v-card-text>
            <v-divider v-if="order.status === 'pending' && order.invoice_url"></v-divider>
            <v-card-actions v-if="order.status === 'pending' && order.invoice_url">
              <v-btn
                block
                color="orange"
                variant="flat"
                prepend-icon="mdi-credit-card-outline"
                class="text-white"
                @click="showPaymentInstructions"
              >
                Payment Instructions
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useTransactionStore } from '~/stores/transaction'
import type { OrderDetail } from '~/schemas/transaction/order'
import CountdownTimer from '~/components/transactions/CountdownTimer.vue'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
})

const route = useRoute()
const transactionStore = useTransactionStore()

const orderId = computed(() => Number(route.params.id))

const order = computed<OrderDetail | null>(() => transactionStore.orderDetail)
const loading = computed(() => transactionStore.loadingDetail)
const error = computed(() => transactionStore.errorDetail)

 
useHead({
  title: computed(() => `Order #${order.value?.jubelio_order_id || orderId.value} | Otoresell`)
})

onMounted(async () => {
  if (orderId.value) {
    await transactionStore.fetchOrderDetail(orderId.value)
  }
})

const showPaymentInstructions = () => {
  if (order.value?.invoice_url) {
    window.open(order.value.invoice_url, '_blank')
  }
};

const formatPrice = (price: string | number): string => {
  const num = typeof price === 'string' ? parseFloat(price) : price
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
  }).format(num)
};

const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  })
};

const getStatusColor = (status: string): string => {
  const colors: { [key: string]: string } = {
    pending: 'warning',
    paid: 'teal',
    processing: 'info',
    shipped: 'deep-purple',
    delivered: 'success',
    cancelled: 'error',
    refunded: 'error',
  }
  return colors[status] || 'grey'
};
</script>

<style scoped>
/* Add any specific styles for this page if needed */
</style>