<template>
  <div>
    <!-- Page Title -->
    <PageTitle 
      title="Transaction History"
      icon="mdi-history"
      subtitle="View and track all your past and current orders in one place."
    />
    
    <!-- Filter & Sort Section -->
    <v-card class="mb-6 rounded-lg" elevation="0" border>
      <v-card-text class="pa-4">
        <v-text-field
          v-model="searchQuery"
          prepend-inner-icon="mdi-magnify"
          label="Search by Order ID, Status, or Name"
          density="comfortable"
          hide-details
          variant="outlined"
          rounded="pill"
          single-line
          color="deep-purple"
          @keyup.enter="handleSearch"
        >
          <template v-slot:append-inner>
            <v-btn
              v-if="searchQuery"
              icon="mdi-close-circle"
              variant="text"
              size="small"
              @click="clearSearch"
              class="mr-2"
              aria-label="Clear search"
            ></v-btn>
            <v-btn
              color="deep-purple"
              variant="flat"
              @click="handleSearch"
              :loading="transactionStore.loading"
              icon="mdi-magnify"
              size="small"
              aria-label="Search"
              class="rounded-pill"
            >
            </v-btn>
          </template>
        </v-text-field>
      </v-card-text>
      <v-expansion-panels flat>
        <v-expansion-panel>
          <v-expansion-panel-title>
            <template v-slot:default="{ expanded }">
              <v-icon size="small" class="mr-3">mdi-filter-variant</v-icon>
              <span class="text-subtitle-1 font-weight-medium">Filter & Sort</span>
              <v-spacer></v-spacer>
              <v-chip v-if="!expanded && activeFilterCount > 0" size="small" color="deep-purple" class="mr-4">
                {{ activeFilterCount }} Active Filter{{ activeFilterCount > 1 ? 's' : '' }}
              </v-chip>
            </template>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <v-divider class="mb-4"></v-divider>

            <v-row dense>
              <!-- Sort By -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortBy"
                  label="Sort by"
                  :items="sortOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
              
              <!-- Sort Direction -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="sortDirection"
                  label="Order"
                  :items="sortDirectionOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
              
              <!-- Per Page -->
              <v-col cols="12" md="4">
                <v-select
                  v-model="perPage"
                  label="Items per page"
                  :items="perPageOptions"
                  density="comfortable"
                  variant="outlined"
                  hide-details
                  color="deep-purple"
                  @update:model-value="handleFilterChange"
                ></v-select>
              </v-col>
            </v-row>
            
            <div class="d-flex justify-end ga-2 mt-4">
              <v-btn
                color="grey"
                variant="text"
                @click="clearFilters"
              >
                Clear All Filters
              </v-btn>
              <v-btn
                color="deep-purple"
                variant="outlined"
                prepend-icon="mdi-refresh"
                @click="fetchOrdersWithFilters"
                :loading="transactionStore.loading"
              >
                Refresh
              </v-btn>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-card>

    <!-- Tabs Section -->
    <v-tabs
      v-model="activeTab"
      bg-color="transparent"
      color="deep-purple"
      show-arrows
      slider-color="deep-purple"
      @update:model-value="handleFilterChange"
      class="transaction-tabs mb-4"
    >
      <v-tab value="all" class="text-body-1">
        <v-icon icon="mdi-format-list-bulleted" class="mr-2"></v-icon>
        All
      </v-tab>
      <v-tab value="pending" class="text-body-1">
        <v-icon icon="mdi-clock-outline" class="mr-2"></v-icon>
        Pending
      </v-tab>
      <v-tab value="paid" class="text-body-1">
        <v-icon icon="mdi-cash-check" class="mr-2"></v-icon>
        Paid
      </v-tab>
      <v-tab value="processing" class="text-body-1">
        <v-icon icon="mdi-progress-clock" class="mr-2"></v-icon>
        Processing
      </v-tab>
      <v-tab value="shipped" class="text-body-1">
        <v-icon icon="mdi-truck-delivery-outline" class="mr-2"></v-icon>
        Shipped
      </v-tab>
      <v-tab value="delivered" class="text-body-1">
        <v-icon icon="mdi-check-circle-outline" class="mr-2"></v-icon>
        Delivered
      </v-tab>
      <v-tab value="cancelled" class="text-body-1">
        <v-icon icon="mdi-close-circle-outline" class="mr-2"></v-icon>
        Cancelled
      </v-tab>
    </v-tabs>

    <!-- Results Summary -->
    <v-card 
      v-if="!transactionStore.loading && transactionStore.orders.length > 0" 
      class="mb-6 rounded-lg" 
      elevation="1"
      color="grey-lighten-5"
    >
      <v-card-text class="py-3">
        <div class="d-flex flex-column flex-sm-row justify-space-between align-start align-sm-center ga-2">
          <div class="text-body-2 text-grey-darken-1">
            <v-icon size="small" class="mr-1">mdi-information-outline</v-icon>
            Showing <span class="font-weight-bold">{{ transactionStore.orders.length }}</span> 
            of <span class="font-weight-bold">{{ transactionStore.totalCount }}</span> orders
            <span v-if="searchQuery" class="text-deep-purple">
              (filtered by "{{ searchQuery }}")
            </span>
          </div>
          <div class="text-body-2 text-grey-darken-1">
            Page <span class="font-weight-bold">{{ currentPage }}</span> 
            of <span class="font-weight-bold">{{ totalPages }}</span>
          </div>
        </div>
      </v-card-text>
    </v-card>
        
    <!-- Loading State -->
    <v-card v-if="transactionStore.loading" class="pa-4 mb-4 rounded-lg" elevation="1">
      <div class="d-flex justify-center align-center py-8">
        <v-progress-circular indeterminate color="deep-purple" size="32"></v-progress-circular>
        <span class="ml-4 text-body-1">Loading your orders...</span>
      </div>
    </v-card>
        
    <!-- Error State -->
    <v-card v-else-if="transactionStore.error" class="pa-4 mb-4 bg-error-lighten-4 rounded-lg" elevation="1">
      <div class="d-flex align-center">
        <v-icon color="error" class="mr-3" size="24">mdi-alert-circle</v-icon>
        <div>
          <div class="text-subtitle-1 font-weight-medium">Unable to load orders</div>
          <div class="text-body-2">{{ transactionStore.error }}</div>
        </div>
      </div>
      <div class="text-center mt-4">
        <v-btn color="deep-purple" @click="fetchOrdersWithFilters">Try Again</v-btn>
      </div>
    </v-card>
        
    <!-- Orders List -->
    <template v-else-if="transactionStore.orders.length > 0">
      <v-card 
        v-for="order in transactionStore.orders"
        :key="order.id"
        class="mb-4 transaction-card rounded-lg"
        elevation="1"
      >
        <!-- Order Header -->
        <v-card-item class="py-3 bg-grey-lighten-4 border-b">
          <div class="d-flex flex-wrap justify-space-between align-center">
            <div class="d-flex align-center flex-wrap">
              <div class="d-flex align-center mr-4 mb-2 mb-sm-0">
                <v-icon icon="mdi-store" class="mr-2" color="deep-purple" size="small"></v-icon>
                <span class="font-weight-medium">Order #{{ order.jubelio_order_id }}</span>
              </div>
              <v-chip
                :color="getStatusColor(order.status)"
                size="small"
                class="text-capitalize mr-4 mb-2 mb-sm-0"
                variant="outlined"
              >
                {{ getStatusText(order.status) }}
              </v-chip>
              <div class="d-flex align-center text-grey text-caption mb-2 mb-sm-0">
                <v-icon icon="mdi-calendar" size="small" class="mr-1"></v-icon>
                {{ formatDate(order.created_at) }}
              </div>
            </div>
            <div class="d-flex align-center">
              <div class="text-caption text-grey mr-4">
                ID: <span class="font-weight-medium">{{ order.id }}</span>
              </div>
              <CountdownTimer
                v-if="order.status === 'pending' && order.expired_at"
                :key="`timer-${order.id}`"
                :expiry-date="order.expired_at"
              />
            </div>
          </div>
        </v-card-item>
                    
        <!-- Order Content -->
        <v-card-text class="pa-4">
          <v-row>
            <!-- Left Column: Recipient & Tracking -->
            <v-col cols="12" md="6">
              <v-list lines="two" density="compact" class="bg-transparent">
                <v-list-item class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" class="mr-3">
                      <v-icon color="grey-darken-1">mdi-account-outline</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Recipient</div>
                    <div class="font-weight-medium">{{ order.shipping_full_name }}</div>
                  </div>
                </v-list-item>

                <v-list-item v-if="order.shipping_type" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" class="mr-3">
                      <v-icon color="grey-darken-1">mdi-package-variant</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Shipping Type</div>
                    <div class="font-weight-medium text-capitalize">{{ order.shipping_type.replace('_', ' ') }}</div>
                  </div>
                </v-list-item>

                <v-list-item v-if="order.shipper" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" class="mr-3">
                      <v-icon color="grey-darken-1">mdi-truck-outline</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Shipper</div>
                    <div class="font-weight-medium">{{ order.shipper }}</div>
                  </div>
                </v-list-item>

                <v-list-item v-if="order.tracking_number" class="px-0">
                  <template v-slot:prepend>
                    <v-avatar color="grey-lighten-3" class="mr-3">
                      <v-icon color="grey-darken-1">mdi-barcode-scan</v-icon>
                    </v-avatar>
                  </template>
                  <div>
                    <div class="text-caption text-grey">Tracking Number</div>
                    <div class="font-weight-medium">{{ order.tracking_number }}</div>
                  </div>
                </v-list-item>
              </v-list>
            </v-col>
            
            <!-- Right Column: Financial Summary -->
            <v-col cols="12" md="6">
              <div class="summary-box pa-3 rounded-lg" style="border: 1px solid #e0e0e0;">
                <div class="d-flex justify-space-between align-center mb-2">
                  <span class="text-body-2">Subtotal:</span>
                  <span class="text-body-2">Rp {{ formatPrice(order.sub_total) }}</span>
                </div>
                <div v-if="Number(order.total_discount) > 0" class="d-flex justify-space-between align-center mb-2">
                  <span class="text-body-2 text-success">Discount:</span>
                  <span class="text-body-2 text-success">-Rp {{ formatPrice(order.total_discount) }}</span>
                </div>
                <div v-if="Number(order.shipping_cost) > 0" class="d-flex justify-space-between align-center mb-2">
                  <span class="text-body-2">Shipping:</span>
                  <span class="text-body-2">Rp {{ formatPrice(order.shipping_cost) }}</span>
                </div>
                <div v-if="Number(order.insurance_cost) > 0" class="d-flex justify-space-between align-center mb-2">
                  <span class="text-body-2">Insurance:</span>
                  <span class="text-body-2">Rp {{ formatPrice(order.insurance_cost) }}</span>
                </div>
                <v-divider class="my-2"></v-divider>
                <div class="d-flex justify-space-between align-center">
                  <span class="text-body-1 font-weight-bold">Total:</span>
                  <span class="text-body-1 font-weight-bold text-deep-purple">Rp {{ formatPrice(order.grand_total) }}</span>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
                    
        <!-- Order Footer -->
        <v-card-actions class="pa-4">
          <div class="d-flex flex-wrap justify-space-between align-center w-100">
            <div class="d-flex align-center mb-3 mb-sm-0">
              <v-icon                 
                :icon="getDeliveryIcon(order.status)"
                :color="getStatusColor(order.status)"
                class="mr-2"
              ></v-icon>
              <div>
                <div class="text-body-2 font-weight-medium">
                  {{ getDeliveryStatus(order.status) }}
                </div>
                <div class="text-caption text-grey">
                  Created: {{ formatDate(order.created_at) }}
                </div>
              </div>
            </div>
            <div class="d-flex flex-wrap">
              <!-- Payment Instructions Button - Only show for pending orders -->
              <v-btn
                v-if="order.status === 'pending' && order.invoice_url"
                variant="outlined"
                color="orange"
                size="small"
                class="mr-2 mb-2 mb-sm-0"
                prepend-icon="mdi-credit-card-outline"
                @click="showPaymentInstructions(order)"
              >
                Payment Instructions
              </v-btn>
              
              <v-btn
                v-if="order.status === 'shipped'"
                variant="outlined"
                color="deep-purple"
                size="small"
                class="mr-2 mb-2 mb-sm-0"
                prepend-icon="mdi-truck-check"
              >
                Track Package
              </v-btn>
              <v-btn
                v-if="order.status === 'delivered'"
                variant="outlined"
                color="success"
                size="small"
                class="mr-2 mb-2 mb-sm-0"
                prepend-icon="mdi-cart"
              >
                Buy Again
              </v-btn>
              <v-btn
                variant="outlined"
                color="deep-purple"
                size="small"
                class="mb-2 mb-sm-0"
                prepend-icon="mdi-file-document-outline"
                :to="`/transactions/${order.id}`"
              >
                View Details
              </v-btn>
            </div>
          </div>
        </v-card-actions>
      </v-card>
    </template>
        
    <!-- Empty State -->
    <v-card v-else class="pa-8 text-center rounded-lg" elevation="1">
      <v-img
        src="https://assets.tokopedia.net/assets-tokopedia-lite/v2/zeus/kratos/9527c778.svg"
        max-width="200"
        class="mx-auto mb-6"
      ></v-img>
      <h2 class="text-h5 mb-2">No orders found</h2>
      <p class="text-body-1 text-grey mb-6">
        {{ getEmptyStateMessage() }}
      </p>
      <v-btn color="deep-purple" size="large" to="/products" prepend-icon="mdi-shopping">
        Start Shopping
      </v-btn>
    </v-card>
        
    <!-- Pagination -->
    <v-card v-if="totalPages > 1" class="mt-6 pa-4 rounded-xl" elevation="1">
      <v-pagination
        v-model="currentPage"
        :length="totalPages"
        :total-visible="7"
        rounded
        color="deep-purple"
        @update:model-value="handlePageChange"
        class="justify-center"
      ></v-pagination>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import CountdownTimer from '~/components/transactions/CountdownTimer.vue'
import { useTransactionStore } from '~/stores/transaction'
import { useAuthStore } from '~/stores/auth'
import type { Order, OrderFilter } from '~/schemas/transaction/order'

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth']
})

useHead({
  title: 'Transaction History | Otoresell'
})

const transactionStore = useTransactionStore()
const authStore = useAuthStore()

// State
const searchQuery = ref('')
const activeTab = ref('all')
const currentPage = ref(1)
const perPage = ref(10)
const sortBy = ref<OrderFilter['sort_by']>('created_at')
const sortDirection = ref<OrderFilter['sort_direction']>('desc')
const searchTimeout = ref<ReturnType<typeof setTimeout> | null>(null)

// Options
const sortOptions = [
  { title: 'Order Date', value: 'created_at' },
  { title: 'Last Updated', value: 'updated_at' },
  { title: 'Order ID', value: 'jubelio_order_id' },
  { title: 'Status', value: 'status' },
  { title: 'Total Amount', value: 'grand_total' }
]

const sortDirectionOptions = [
  { title: 'Newest First', value: 'desc' },
  { title: 'Oldest First', value: 'asc' }
]

const perPageOptions = [
  { title: '10', value: 10 },
  { title: '20', value: 20 },
  { title: '50', value: 50 },
  { title: '100', value: 100 }
]

// Computed
const totalPages = computed(() => {
  if (transactionStore.totalCount === 0 || perPage.value === 0) return 1
  return Math.ceil(transactionStore.totalCount / perPage.value)
})

const activeFilterCount = computed(() => {
  let count = 0
  if (searchQuery.value.trim()) count++
  if (activeTab.value !== 'all') count++
  if (sortBy.value !== 'created_at') count++
  if (sortDirection.value !== 'desc') count++
  if (perPage.value !== 10) count++
  return count
})

// Fetch orders with current filters
const fetchOrdersWithFilters = async () => {
  const params: Partial<OrderFilter> = {
    page: currentPage.value,
    per_page: perPage.value,
    sort_by: sortBy.value,
    sort_direction: sortDirection.value,
    // Always include user-specific filters
    // jubelio_contact_id: authStore.user?.jubelio_contact_id as number, dont use this field
    user_id: authStore.user?.id
  }
  
  // Add status filter if not 'all'
  if (activeTab.value !== 'all') {
    params.status = activeTab.value
  }
  
  // Add search query if present
  if (searchQuery.value) {
    params.search = searchQuery.value
  }
  
  await transactionStore.fetchOrders(params)
  
  await nextTick()
  if (import.meta.client) {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
};

// Handlers
const handlePageChange = (newPage: number) => {
  currentPage.value = newPage
  fetchOrdersWithFilters()
};

const handleFilterChange = () => {
  currentPage.value = 1
  fetchOrdersWithFilters()
};

const handleSearch = () => {
  currentPage.value = 1
  fetchOrdersWithFilters()
};

const clearSearch = () => {
  searchQuery.value = ''
  handleSearch()
};

const clearFilters = () => {
  searchQuery.value = ''
  activeTab.value = 'all'
  sortBy.value = 'created_at'
  sortDirection.value = 'desc'
  perPage.value = 10
  currentPage.value = 1
  fetchOrdersWithFilters()
};

// Status and Formatting Functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'processing':
      return 'info'
    case 'pending':
      return 'warning'
    case 'paid':
      return 'teal'
    case 'shipped':
      return 'deep-purple'
    case 'delivered':
      return 'success'
    case 'cancelled':
    case 'refunded':
      return 'error'
    default:
      return 'grey'
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'processing':
      return 'Processing'
    case 'pending':
      return 'Pending'
    case 'paid':
      return 'Paid'
    case 'shipped':
      return 'Shipped'
    case 'delivered':
      return 'Delivered'
    case 'cancelled':
      return 'Cancelled'
    case 'refunded':
      return 'Refunded'
    default:
      return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown'
  }
};

const getDeliveryIcon = (status: string) => {
  switch (status) {
    case 'processing':
      return 'mdi-package-variant'
    case 'pending':
      return 'mdi-clock-outline'
    case 'paid':
      return 'mdi-cash-check'
    case 'shipped':
      return 'mdi-truck-delivery-outline'
    case 'delivered':
      return 'mdi-check-circle-outline'
    case 'cancelled':
    case 'refunded':
      return 'mdi-close-circle-outline'
    default:
      return 'mdi-help-circle-outline'
  }
};

const getDeliveryStatus = (status: string) => {
  switch (status) {
    case 'processing':
      return 'Order is being processed'
    case 'pending':
      return 'Waiting for payment'
    case 'paid':
      return 'Payment has been confirmed'
    case 'shipped':
      return 'Package is on the way'
    case 'delivered':
      return 'Order has been delivered'
    case 'cancelled':
      return 'Order has been cancelled'
    case 'refunded':
      return 'Order has been refunded'
    default:
      return 'Status unknown'
  }
};

const getEmptyStateMessage = () => {
  if (searchQuery.value) {
    return `No orders found for "${searchQuery.value}"`
  }
  
  switch (activeTab.value) {
    case 'all':
      return "You haven't made any orders yet."
    case 'pending':
      return "You don't have any pending orders."
    case 'processing':
      return "You don't have any processing orders."
    case 'paid':
      return "You don't have any paid orders."
    case 'shipped':
      return "You don't have any shipped orders."
    case 'delivered':
      return "You don't have any delivered orders."
    case 'cancelled':
      return "You don't have any cancelled orders."
    default:
      return "No orders found."
  }
};

const formatPrice = (price: string) => {
  return Number(price).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".")
};

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
};

const showPaymentInstructions = (order: Order) => {
  if (order.invoice_url) {
    window.open(order.invoice_url, '_blank')
  }
};

// Lifecycle and Watchers
onMounted(async () => {
  await fetchOrdersWithFilters()
})

// Implement debounce for search
watch(searchQuery, () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  searchTimeout.value = setTimeout(() => {
    currentPage.value = 1 // Reset to first page when search changes
    fetchOrdersWithFilters()
  }, 500)
})
</script>

<style scoped>
.transaction-card {
  transition: all 0.2s ease;
  overflow: hidden;
  border: 1px solid transparent;
}

.transaction-card:hover {
  border-color: rgba(103, 58, 183, 0.3);
  box-shadow: 0 4px 15px rgba(103, 58, 183, 0.1) !important;
  transform: translateY(-2px);
}

.border-b {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.transaction-tabs .v-tab {
  min-width: 120px;
  height: 48px;
  font-weight: 500;
}

.transaction-tabs .v-tab--selected {
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .transaction-tabs .v-tab {
    min-width: auto;
    padding: 0 12px;
  }
}
</style>
