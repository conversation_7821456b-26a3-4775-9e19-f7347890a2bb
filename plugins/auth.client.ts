export default defineNuxtPlugin(async () => {
  // Only run on client-side
  const authStore = useAuthStore()
  
  // Check authentication status once at app initialization
  await authStore.checkAuth()
  
  // Global variable to store the interval
  let refreshInterval: NodeJS.Timeout | null = null
  
  // Set up token refresh interval if authenticated
  if (authStore.isAuthenticated) {
    // Refresh token every 25 minutes (assuming 30 min token life)
    refreshInterval = setInterval(async () => {
      if (authStore.isAuthenticated) {
        await authStore.refreshToken()
      } else {
        if (refreshInterval) {
          clearInterval(refreshInterval)
          refreshInterval = null
        }
      }
    }, 25 * 60 * 1000) // 25 minutes
  }
  
  // Add a window event listener for cleanup
  if (import.meta.client && window) {
    window.addEventListener('beforeunload', () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
        refreshInterval = null
      }
    })
  }
})
