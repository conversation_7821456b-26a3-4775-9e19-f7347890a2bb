export default defineNuxtPlugin(async () => {
  const cartStore = useCartStore();
  const authStore = useAuthStore();
  const productDiscountStore = useProductDiscountStore();

  const loadCartAndDiscounts = async () => {
    if (authStore.isAuthenticated) {
      try {
        await cartStore.loadCartFromServer();
        if (cartStore.items.length > 0) {
          const itemGroupIds = cartStore.items.map((item) => item.jubelio_item_group_id.toString());
          await productDiscountStore.fetchBatchProductDiscounts(itemGroupIds);
        }
      } catch (error) {
        console.error('Failed to load cart or discounts:', error);
      }
    }
  };

  // Watch for authentication state changes
  watch(() => authStore.isAuthenticated, (isAuthenticated) => {
    if (isAuthenticated) {
      loadCartAndDiscounts();
    }
  });

  // Initial load
  await loadCartAndDiscounts();
});