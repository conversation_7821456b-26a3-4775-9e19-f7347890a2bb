// use zod 3
import { z, type ZodType } from 'zod/v3';

// --- Schema Definitions ---
// These functions create the schemas for the API response "envelopes".
// They are not transformed here to avoid complex type inference issues.

const paginationLinksSchema = z.object({
  first: z.string().url().nullable(),
  last: z.string().url().nullable(),
  prev: z.string().url().nullable(),
  next: z.string().url().nullable(),
});

const paginationMetaSchema = z.object({
  current_page: z.number(),
  from: z.number().nullable(),
  last_page: z.number(),
  links: z.array(z.object({
    url: z.string().url().nullable(),
    label: z.string(),
    active: z.boolean(),
  })),
  path: z.string().url(),
  per_page: z.number(),
  to: z.number().nullable(),
  total: z.number(),
});

/**
 * Creates the schema for a paginated API response envelope.
 * @param itemSchema The Zod schema for a single item in the data array.
 */
export const createPaginatedResponseSchema = <T extends ZodType>(itemSchema: T) => {
  return z.object({
    data: z.array(itemSchema),
    links: paginationLinksSchema,
    meta: paginationMetaSchema,
  });
};

/**
 * Creates the schema for a paginated API response envelope that includes a success flag.
 * @param itemSchema The Zod schema for a single item in the data array.
 */
export const createSuccessfulPaginatedResponseSchema = <T extends ZodType>(itemSchema: T) => {
  return z.object({
    success: z.boolean(),
    data: z.array(itemSchema),
    links: paginationLinksSchema.nullable(),
    meta: paginationMetaSchema.nullable(),
  });
};

/**
 * Creates the schema for a single item API response envelope.
 * @param dataSchema The Zod schema for the data object.
 */
export const createSingleItemResponseSchema = <T extends ZodType>(dataSchema: T) => {
  return z.object({
    data: dataSchema,
  }).passthrough(); // Allow other properties like 'message'
};

/**
 * Creates the schema for the authentication API response.
 * @param userSchema The Zod schema for the user object.
 */
export const createAuthResponseSchema = <T extends ZodType>(userSchema: T) => {
  return z.object({
    user: userSchema,
    token_type: z.literal('Bearer'),
    token: z.string(), // token from external API
    jubelio_token: z.string(), // jubelio token
    message: z.string().optional(),
  });
};


// --- Parsers & Transformers ---
// These functions take raw data and a schema, then parse and transform the data.
// This separates the parsing logic from the schema definition.

/**
 * Parses a paginated response and returns the data and metadata.
 * @param rawData The unknown data from the API.
 * @param itemSchema The Zod schema for a single item.
 */
export const parsePaginatedResponse = <T extends ZodType>(rawData: unknown, itemSchema: T) => {
  const paginatedSchema = createPaginatedResponseSchema(itemSchema);
  const parsed = paginatedSchema.parse(rawData);
  return {
    data: parsed.data,
    meta: parsed.meta,
  };
};

/**
 * Parses a single item response and returns only the inner data object.
 * @param rawData The unknown data from the API.
 * @param dataSchema The Zod schema for the data object.
 */
export const parseSingleItemResponse = <T extends ZodType>(rawData: unknown, dataSchema: T) => {
  const singleItemSchema = createSingleItemResponseSchema(dataSchema);
  const parsed = singleItemSchema.parse(rawData) as { data: z.infer<T> };
  return parsed.data;
};

// --- Inferred Types ---
export type PaginatedResponse<T extends ZodType> = z.infer<ReturnType<typeof createPaginatedResponseSchema<T>>>;
export type SingleItemResponse<T extends ZodType> = z.infer<ReturnType<typeof createSingleItemResponseSchema<T>>>;
export type SuccessfulPaginatedResponse<T extends ZodType> = z.infer<ReturnType<typeof createSuccessfulPaginatedResponseSchema<T>>>;

export const successResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});

export type SuccessResponse = z.infer<typeof successResponseSchema>;
