// use zod 3
import { z } from 'zod/v3'
import { userSchema, updateUserSchema } from '~/schemas/otoapi/user'

/**
 * Defines the schema for a successful response from the /api/auth/login endpoint.
 * This is the "contract" between your server and frontend.
 */
export const loginSuccessResponseSchema = z.object({
  status: z.literal('success'),
  token: z.string(),
  csrfToken: z.string(),
  user: userSchema, // Re-uses the existing user schema
});

/**
 * The TypeScript type inferred from the schema.
 * This is what you will import and use in your frontend code.
 */
export type LoginSuccessResponse = z.infer<typeof loginSuccessResponseSchema>;

/**
 * Defines the schema for a successful response from the /api/auth/check endpoint.
 */
export const authCheckResponseSchema = z.object({
  authenticated: z.boolean(),
  user: userSchema.optional(),
  token: z.string().optional(),
  csrfToken: z.string().optional(),
});

/**
 * The TypeScript type inferred from the schema.
 */
export type AuthCheckResponse = z.infer<typeof authCheckResponseSchema>;

/**
 * Defines the schema for a successful response from the /api/auth/refresh endpoint.
 */
export const refreshTokenResponseSchema = z.object({
  status: z.literal('success'),
  token: z.string(),
  csrfToken: z.string(),
});

/**
 * The TypeScript type inferred from the schema.
 */
export type RefreshTokenResponse = z.infer<typeof refreshTokenResponseSchema>;

/**
 * Defines the schema for the request body of the /api/auth/update-user endpoint.
 */
export const updateUserBodySchema = z.object({
  userData: updateUserSchema,
});

/**
 * Defines the schema for a successful response from the /api/auth/update-user endpoint.
 */
export const updateUserResponseSchema = z.object({
  status: z.literal('success'),
  user: userSchema,
  token: z.string(),
  csrfToken: z.string(),
});

/**
 * The TypeScript type inferred from the schema.
 */
export type UpdateUserResponse = z.infer<typeof updateUserResponseSchema>;