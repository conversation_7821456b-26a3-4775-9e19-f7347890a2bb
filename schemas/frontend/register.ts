import { z } from 'zod/v3';

// Base schema without refinement for individual field validation
export const baseRegisterSchema = z.object({
  name: z.string().min(1, { message: 'Full Name is required' }).max(255, { message: 'Full Name cannot exceed 255 characters' }),
  email: z.string().email({ message: 'Please enter a valid email' }).max(255, { message: 'Email cannot exceed 255 characters' }),
  phone: z.string().min(10, { message: 'Phone number must be at least 10 characters' }).max(20, { message: 'Phone number cannot exceed 20 characters' }),
  password: z.string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one number' }),
  password_confirmation: z.string(),
  turnstileToken: z.string().optional(),
});

// Schema with refinement for final form submission
export const registerSchema = baseRegisterSchema.refine(data => data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ["password_confirmation"], // path of error
});

export type RegisterForm = z.infer<typeof registerSchema>;