import { z } from 'zod/v3';
import { baseRegisterSchema } from './register';

// Schema for the forgot password form (only email is needed)
export const forgotPasswordSchema = z.object({
  email: baseRegisterSchema.shape.email,
  turnstileToken: z.string().optional(),
});

// Schema for the reset password form
export const resetPasswordSchema = z.object({
  token: z.string().min(1, { message: 'Token is required' }),
  email: baseRegisterSchema.shape.email,
  password: baseRegisterSchema.shape.password,
  password_confirmation: z.string(),
  turnstileToken: z.string().optional(),
}).refine(data => data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ["password_confirmation"],
});

export type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordForm = z.infer<typeof resetPasswordSchema>;