import { z } from 'zod/v3';

// Schema for the request body to generate an airwaybill (cnote)
export const jneAirwaybillRequestSchema = z.object({
  OLSHOP_BRANCH: z.string(),
  OLSHOP_CUST: z.string(),
  OLSHOP_ORDERID: z.string(),
  OLSHOP_SHIPPER_NAME: z.string(),
  OLSHOP_SHIPPER_ADDR1: z.string(),
  OLSHOP_SHIPPER_ADDR2: z.string().optional(),
  OLSHOP_SHIPPER_ADDR3: z.string().optional(),
  OLSHOP_SHIPPER_CITY: z.string(),
  OLSHOP_SHIPPER_REGION: z.string(),
  OLSHOP_SHIPPER_ZIP: z.string(),
  OLSHOP_SHIPPER_PHONE: z.string(),
  OLSHOP_RECEIVER_NAME: z.string(),
  OLSHOP_RECEIVER_ADDR1: z.string(),
  OLSHOP_RECEIVER_ADDR2: z.string().optional(),
  OLSHOP_RECEIVER_ADDR3: z.string().optional(),
  OLSHOP_RECEIVER_CITY: z.string(),
  OLSHOP_RECEIVER_REGION: z.string(),
  OLSHOP_RECEIVER_ZIP: z.string(),
  OLSHOP_RECEIVER_PHONE: z.string(),
  OLSHOP_QTY: z.coerce.number().int().positive(),
  OLSHOP_WEIGHT: z.coerce.number().positive(),
  OLSHOP_GOODSDESC: z.string(),
  OLSHOP_GOODSVALUE: z.coerce.number().int().nonnegative(),
  OLSHOP_GOODSTYPE: z.coerce.number().int(),
  OLSHOP_INST: z.string().optional(),
  OLSHOP_INS_FLAG: z.enum(['Y', 'N']),
  OLSHOP_ORIG: z.string(),
  OLSHOP_DEST: z.string(),
  OLSHOP_SERVICE: z.string(),
  OLSHOP_COD_FLAG: z.enum(['Y', 'N']),
  OLSHOP_COD_AMOUNT: z.coerce.number().nonnegative(),
});

// Schema for the success detail object in the response
const airwaybillSuccessDetailSchema = z.object({
  status: z.literal('sukses'),
  cnote_no: z.string(),
});

// Schema for the error detail object in the response
const airwaybillErrorDetailSchema = z.object({
  status: z.literal('Error'),
  reason: z.string(),
  cnote_no: z.string().optional(),
});

// Schema for the successful response
export const jneAirwaybillResponseSchema = z.object({
  detail: z.array(z.union([airwaybillSuccessDetailSchema, airwaybillErrorDetailSchema])),
});

// Infer types from schemas
export type JneAirwaybillRequest = z.infer<typeof jneAirwaybillRequestSchema>;
export type JneAirwaybillResponse = z.infer<typeof jneAirwaybillResponseSchema>;