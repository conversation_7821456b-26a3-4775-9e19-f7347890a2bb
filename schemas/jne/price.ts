import { z } from 'zod/v3';

// Schema for the request body of the pricing endpoint
export const jnePriceRequestSchema = z.object({
  from: z.string().min(1, 'Origin is required'),
  thru: z.string().min(1, 'Destination is required'),
  weight: z.coerce.number().positive('Weight must be a positive number i Kg'),
});

// Schema for a single price object in the response
const priceDetailSchema = z.object({
  origin_name: z.string(),
  destination_name: z.string(),
  service_display: z.string(),
  service_code: z.string(),
  goods_type: z.string(),
  currency: z.string(),
  price: z.coerce.number(),
  etd_from: z.string(),
  etd_thru: z.string(),
  times: z.string(),
});

// Schema for the successful response of the pricing endpoint
export const jnePriceResponseSchema = z.object({
  price: z.array(priceDetailSchema),
});

// Infer types from schemas
export type JnePriceRequest = z.infer<typeof jnePriceRequestSchema>;
export type JnePriceResponse = z.infer<typeof jnePriceResponseSchema>;
export type JnePriceDetail = z.infer<typeof priceDetailSchema>;