import { z } from 'zod/v3';

// Schema for the request body of the tracking endpoint
export const jneTrackingRequestSchema = z.object({
  // AWB is part of the URL, so the body only needs credentials
  // which are handled by the service, but we can define a schema for the param
});

export const jneTrackingParamsSchema = z.object({
    awb: z.string().min(1, 'Airwaybill number is required'),
});

// Schema for the main CNOTE details
const cnoteSchema = z.object({
  cnote_no: z.string(),
  reference_number: z.string(),
  cnote_origin: z.string(),
  cnote_destination: z.string(),
  cnote_services_code: z.string(),
  servicetype: z.string(),
  cnote_cust_no: z.string(),
  cnote_date: z.string(), // Could be z.coerce.date() if needed
  cnote_pod_receiver: z.string(),
  cnote_receiver_name: z.string(),
  city_name: z.string(),
  cnote_pod_date: z.string(), // Could be z.coerce.date()
  pod_status: z.string(),
  last_status: z.string(),
  cust_type: z.string(),
  cnote_amount: z.string().transform(val => parseFloat(val)),
  cnote_weight: z.string().transform(val => parseFloat(val)),
  pod_code: z.string(),
  keterangan: z.string(),
  cnote_goods_descr: z.string(),
  freight_charge: z.string().transform(val => parseFloat(val)),
  shippingcost: z.string().transform(val => parseFloat(val)),
  insuranceamount: z.string().transform(val => parseFloat(val)),
  priceperkg: z.string().transform(val => parseFloat(val)),
  signature: z.string().url().nullable(),
  photo: z.string().url().nullable(),
  long: z.string(),
  lat: z.string(),
  estimate_delivery: z.string(),
});

// Schema for the shipment detail
const detailSchema = z.object({
  cnote_no: z.string(),
  cnote_date: z.string(),
  cnote_weight: z.string(),
  cnote_origin: z.string(),
  cnote_shipper_name: z.string(),
  cnote_shipper_addr1: z.string(),
  cnote_shipper_addr2: z.string().nullable(),
  cnote_shipper_addr3: z.string().nullable(),
  cnote_shipper_city: z.string(),
  cnote_receiver_name: z.string(),
  cnote_receiver_addr1: z.string(),
  cnote_receiver_addr2: z.string().nullable(),
  cnote_receiver_addr3: z.string().nullable(),
  cnote_receiver_city: z.string(),
});

// Schema for a single history entry
const historySchema = z.object({
  date: z.string(),
  desc: z.string(),
  code: z.string(),
});

// Schema for the successful tracking response
export const jneTrackingResponseSchema = z.object({
  cnote: cnoteSchema,
  detail: z.array(detailSchema),
  history: z.array(historySchema),
});

// Infer types from schemas
export type JneTrackingParams = z.infer<typeof jneTrackingParamsSchema>;
export type JneTrackingResponse = z.infer<typeof jneTrackingResponseSchema>;