// schemas/jubelio/address.ts
import { z } from 'zod/v3';
import { createJubelioPaginatedResponseSchema } from './response';

// Corresponds to JubelioProvince
export const jubelioProvinceSchema = z.object({
  province_id: z.string(),
  name: z.string(),
});

// Corresponds to JubelioCity
export const jubelioCitySchema = z.object({
  city_id: z.string(),
  province_id: z.string(),
  name: z.string(),
});

// Corresponds to JubelioDistrict
export const jubelioDistrictSchema = z.object({
  district_id: z.string(),
  city_id: z.string(),
  name: z.string(),
});

// Corresponds to JubelioSubdistrict
export const jubelioSubdistrictSchema = z.object({
  subdistrict_id: z.string(),
  district_id: z.string(),
  name: z.string(),
});

// Corresponds to JubelioLocation
export const jubelioLocationSchema = z.object({
  location_id: z.number(),
  location_name: z.string(),
  is_pos_outlet: z.boolean(),
  address: z.string(),
  city: z.string(),
  phone: z.string().nullable(),
  email: z.string().nullable(),
  province: z.string(),
  post_code: z.string(),
  is_fbl: z.boolean(),
  location_code: z.string(),
  area: z.string().nullable(),
  is_tcb: z.boolean(),
  warehouse_id: z.string(),
  warehouse_store_id: z.string().nullable(),
  pos_discount: z.number().nullable(),
  pos_discount_type: z.string().nullable(),
  pos_struct_id: z.coerce.string().nullable(),
  pos_tax: z.number(),
  is_active: z.boolean(),
  pos_discount_max: z.number().nullable(),
  is_warehouse: z.boolean(),
  is_sbs: z.boolean(),
  wms_migration_date: z.string(),
  default_warehouse_user: z.string(),
  source_replenishment: z.number(),
  is_multi_origin: z.boolean(),
  subdistrict: z.string(),
  coordinate: z.string().nullable(),
  province_id: z.string(),
  city_id: z.string(),
  district_id: z.string(),
  subdistrict_id: z.string(),
  pos_email_receipt_id: z.number(),
  lz_warehouse_id: z.string(),
  is_o2o: z.boolean(),
  location_group_id: z.number(),
  location_type: z.string(),
});

// Corresponds to ShopLogo
const shopLogoSchema = z.object({
  url: z.string(),
  file_name: z.string(),
  thumbnail: z.string(),
  sequence_number: z.number(),
});

// Corresponds to JubelioShops
export const jubelioShopsSchema = z.object({
  store_id: z.number(),
  store_name: z.string(),
  channel_id: z.number(),
  location_id: z.number().nullable(),
  address: z.string().nullable(),
  setting: z.object({}), // Define more strictly if needed
  is_active: z.boolean(),
  shipment_type: z.string().nullable(),
  sync_qty_percentage: z.number(),
  store_phone: z.string().nullable(),
  logo: z.array(shopLogoSchema).nullable(),
});

// Response Schemas
export const provincesResponseSchema = z.array(jubelioProvinceSchema);
export const citiesResponseSchema = z.array(jubelioCitySchema);
export const districtsResponseSchema = z.array(jubelioDistrictSchema);
export const subdistrictsResponseSchema = z.array(jubelioSubdistrictSchema);
export const locationsResponseSchema = createJubelioPaginatedResponseSchema(jubelioLocationSchema);
export const shopsResponseSchema = createJubelioPaginatedResponseSchema(jubelioShopsSchema);

// --- Inferred Types ---
export type JubelioProvince = z.infer<typeof jubelioProvinceSchema>;
export type JubelioCity = z.infer<typeof jubelioCitySchema>;
export type JubelioDistrict = z.infer<typeof jubelioDistrictSchema>;
export type JubelioSubdistrict = z.infer<typeof jubelioSubdistrictSchema>;
export type JubelioLocation = z.infer<typeof jubelioLocationSchema>;
export type JubelioShops = z.infer<typeof jubelioShopsSchema>;

export type ProvincesResponse = z.infer<typeof provincesResponseSchema>;
export type CitiesResponse = z.infer<typeof citiesResponseSchema>;
export type DistrictsResponse = z.infer<typeof districtsResponseSchema>;
export type SubdistrictsResponse = z.infer<typeof subdistrictsResponseSchema>;
export type LocationsResponse = z.infer<typeof locationsResponseSchema>;
export type ShopsResponse = z.infer<typeof shopsResponseSchema>;