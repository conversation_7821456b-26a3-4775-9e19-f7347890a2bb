// schemas/jubelio/common.ts
import { z } from 'zod/v3';

// From example1.json.example and example2.json.example
export const jubelioChannelSchema = z.object({
  channel_name: z.string(),
  store_name: z.string(),
  store_id: z.union([z.string(), z.number()]),
  channel_id: z.number().optional(),
});

// From example1.json.example
export const jubelioLocationSchema = z.object({
  location_id: z.number(),
  location_name: z.string(),
});

export const variationValueSchema = z.object({
  label: z.string(),
  value: z.string(),
});

export type JubelioChannel = z.infer<typeof jubelioChannelSchema>;
export type JubelioLocation = z.infer<typeof jubelioLocationSchema>;
export type VariationValue = z.infer<typeof variationValueSchema>;
