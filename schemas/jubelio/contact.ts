// schemas/jubelio/contact.ts
import { z } from 'zod/v3';

export const jubelioContactSchema = z.object({
  contact_id: z.number(),
  contact_name: z.string(),
  contact_type: z.number(),
  primary_contact: z.string().nullable(),
  contact_position: z.string().nullable(),
  email: z.string().email(),
  phone: z.string(),
  mobile: z.string().nullable(),
  fax: z.string().nullable(),
  npwp: z.string().nullable(),
  payment_term: z.number().nullable(),
  notes: z.string().nullable(),
  s_address: z.string().nullable(),
  s_area: z.string().nullable(),
  s_city: z.string().nullable(),
  s_province: z.string().nullable(),
  s_post_code: z.string().nullable(),
  b_address: z.string().nullable(),
  b_area: z.string().nullable(),
  b_city: z.string().nullable(),
  b_province: z.string().nullable(),
  b_post_code: z.string().nullable(),
  location_id: z.number().nullable(),
  is_loyalty_member: z.boolean(),
  b_sex: z.string().nullable(),
  b_birthday: z.string().nullable(), // Could be z.date() if transformed
  is_dropshipper: z.boolean(),
  is_reseller: z.boolean(),
  created_date: z.string(), // Could be z.date() if transformed
  category_id: z.number(),
  category_display: z.string(),
  nik: z.string().nullable(),
  is_company: z.boolean(),
  nik_images: z.any().nullable(), // Type is unknown from example
  npwp_images: z.any().nullable(), // Type is unknown from example
  s_subdistrict: z.string().nullable(),
  s_coordinate: z.string().nullable(),
  s_province_id: z.number().nullable(),
  s_city_id: z.number().nullable(),
  s_district_id: z.number().nullable(),
  s_subdistrict_id: z.number().nullable(),
  b_subdistrict: z.string().nullable(),
  b_province_id: z.number().nullable(),
  b_city_id: z.number().nullable(),
  b_district_id: z.number().nullable(),
  b_subdistrict_id: z.number().nullable(),
  debt_acct_id: z.number().nullable(),
  country_id: z.number().nullable(),
  dob: z.string().nullable(), // Could be z.date() if transformed
  contact_source: z.string().nullable(),
  source_detail: z.string().nullable(),
  is_pkp: z.boolean(),
  npwp_name: z.string().nullable(),
  npwp_street: z.string().nullable(),
  npwp_block: z.string().nullable(),
  npwp_house_no: z.string().nullable(),
  npwp_rt: z.string().nullable(),
  npwp_rw: z.string().nullable(),
  npwp_subdistrict_id: z.number().nullable(),
  npwp_subdistrict: z.string().nullable(),
  npwp_district_id: z.number().nullable(),
  npwp_district: z.string().nullable(),
  npwp_city_id: z.number().nullable(),
  npwp_city: z.string().nullable(),
  npwp_province_id: z.number().nullable(),
  npwp_province: z.string().nullable(),
  npwp_post_code: z.string().nullable(),
  contact_id_webstore: z.number().nullable(),
  account_id: z.number().nullable(),
  account_code: z.string().nullable(),
  account_name: z.string().nullable(),
  is_active: z.boolean().nullable(),
  type: z.string().nullable(),
  bank_id: z.number().nullable(),
  country_code: z.string().nullable(),
  country_name: z.string().nullable(),
  debt_acct_name: z.string().nullable(),
});

// --- Inferred Types ---
export type JubelioContact = z.infer<typeof jubelioContactSchema>;
