// schemas/jubelio/product.ts
import { z } from 'zod/v3';
import { variationValueSchema } from './common';

// From example1.json.example - GET /inventory/
const totalStocksSchema = z.object({
  on_hand: z.number(),
  on_order: z.number(),
  reserved: z.number(),
  available: z.number(),
});

export const jubelioProductSchema = z.object({
  item_id: z.number(),
  item_code: z.string(),
  item_name: z.string(),
  variation_values: z.preprocess((val) => (val === null ? [] : val), z.array(variationValueSchema)),
  total_stocks: totalStocksSchema,
  thumbnail: z.string().url().nullable(),
});

// From example2.json.example - GET /inventory/internal-price-list/
export const priceSchema = z.object({
  store_id: z.number(),
  price: z.number(),
});

export const jubelioPriceListItemSchema = z.object({
  item_id: z.number(),
  item_code: z.string(),
  item_name: z.string(),
  prices: z.array(priceSchema),
});

// From example3.json.example - GET /inventory/items/
const productGroupVariantSchema = z.object({
  item_id: z.number(),
  item_code: z.string(),
  item_name: z.string(),
  is_bundle: z.boolean(),
  variation_values: z.preprocess((val) => (val === null ? [] : val), z.array(variationValueSchema)),
  thumbnail: z.string().url().nullable(),
  sell_price: z.number(),
  end_qty: z.number().nullable(),
  available_qty: z.number().nullable(),
});

export const jubelioProductGroupSchema = z.object({
  item_group_id: z.number(),
  item_name: z.string(),
  thumbnail: z.string().url().nullable(),
  variants: z.array(productGroupVariantSchema),
});

// From example4.json.example - GET /inventory/items/{productId}
const skuImageSchema = z.object({
  item_id: z.number(),
  image_id: z.number(),
  cloud_key: z.string().url(),
  thumbnail: z.string().url(),
  file_name: z.string(),
  sequence_number: z.number(),
  channel_info: z.any().nullable(),
});

const productSkuSchema = z.object({
  item_id: z.number(),
  item_code: z.string(),
  sell_price: z.number(),
  variation_values: z.preprocess((val) => (val === null ? [] : val), z.array(variationValueSchema)),
  images: z.array(skuImageSchema),
  barcode: z.string().nullable(),
  end_qty: z.number().nullable(),
  average_cost: z.number().nullable(),
  amount: z.number().nullable(),
  prices: z.array(z.any()).optional(), // Can be defined more strictly
});

export const jubelioProductDetailSchema = z.object({
  item_group_id: z.number(),
  item_group_name: z.string(),
  description: z.string().nullable(),
  thumbnail: z.string().url().optional().nullable(),
  product_skus: z.array(productSkuSchema),
  sell_unit: z.string(),
  package_weight: z.string().transform(val => parseFloat(val)),
  sell_price: z.string().transform(val => parseFloat(val)),
});

// From example5.json.example - GET /inventory/items/to-sell/-1
export const jubelioProductToSellSchema = z.object({
  item_id: z.number(),
  item_code: z.string(),
  item_name: z.string(),
  sell_price: z.coerce.number(),
  thumbnail: z.string().nullable(),
  available_qty: z.coerce.number(),
  weight_in_gram: z.string().transform(val => parseFloat(val)),
  sell_unit: z.string(),
  rate: z.string().nullable().transform(val => val ? parseFloat(val) : 0),
  sell_tax_id: z.number(),
});

// --- Inferred Types ---
export type JubelioProduct = z.infer<typeof jubelioProductSchema>;
export type JubelioPriceListItem = z.infer<typeof jubelioPriceListItemSchema>;
export type JubelioProductGroup = z.infer<typeof jubelioProductGroupSchema>;
export type JubelioProductDetail = z.infer<typeof jubelioProductDetailSchema>;
export type JubelioProductToSell = z.infer<typeof jubelioProductToSellSchema>;
export type JubelioProductSku = z.infer<typeof productSkuSchema>;
