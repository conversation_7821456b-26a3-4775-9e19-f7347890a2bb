// schemas/jubelio/response.ts
import { z, type ZodType } from 'zod/v3';
import { jubelioChannelSchema, jubelioLocationSchema } from './common';

/**
 * Creates a schema for a standard Jubelio paginated list response.
 * @param itemSchema The Zod schema for a single item in the data array.
 */
export const createJubelioPaginatedResponseSchema = <T extends ZodType>(itemSchema: T) => {
  return z.object({
    data: z.array(itemSchema),
    totalCount: z.number(),
  });
};

/**
 * Schema for the response from GET /inventory/.
 * It has a unique structure with top-level channels and locations.
 * @param itemSchema The Zod schema for a single product item.
 */
export const createJubelioInventoryResponseSchema = <T extends ZodType>(itemSchema: T) => {
  return z.object({
    data: z.array(itemSchema),
    totalCount: z.number(),
    channels: z.array(jubelioChannelSchema),
    locations: z.array(jubelioLocationSchema),
  });
};

/**
 * Schema for the response from GET /inventory/internal-price-list/.
 * It has a unique structure with top-level channels.
 * @param itemSchema The Zod schema for a single price list item.
 */
export const createJubelioPriceListResponseSchema = <T extends ZodType>(itemSchema: T) => {
  return z.object({
    data: z.array(itemSchema),
    totalCount: z.number(),
    channels: z.array(jubelioChannelSchema),
  });
};

// --- Inferred Types ---
export type JubelioPaginatedResponse<T extends ZodType> = z.infer<ReturnType<typeof createJubelioPaginatedResponseSchema<T>>>;
export type JubelioInventoryResponse<T extends ZodType> = z.infer<ReturnType<typeof createJubelioInventoryResponseSchema<T>>>;
export type JubelioPriceListResponse<T extends ZodType> = z.infer<ReturnType<typeof createJubelioPriceListResponseSchema<T>>>;
