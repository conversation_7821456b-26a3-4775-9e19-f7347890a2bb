import { z } from 'zod/v3';
import { createSingleItemResponseSchema } from '~/schemas/api-response';

// 1. Define a BASE schema with fields common to all cart operations.
// This is our "single source of truth" for the core cart item properties.
const cartItemBaseSchema = z.object({
  jubelio_item_id: z.number(),
  jubelio_item_group_id: z.number(),
  sku: z.string(),
  name: z.string(),
  unit: z.string(),
  price: z.number(),
  weight: z.number(),
  variant: z.array(z.object({
    label: z.string(),
    value: z.string(),
  })).nullable().default([]),
  image: z.string().nullable(),
  tax_id: z.number(),
  tax_rate_percent: z.number(),
});

// 2. Schema for ADDING an item to the cart.
// Extends the base schema and adds the `quantity`.
export const addToCartSchema = cartItemBaseSchema.extend({
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

// 3. Schema for the full cart item object returned by the API.
// Extends the base schema and adds server-generated fields like `id` and timestamps.
export const cartItemSchema = cartItemBaseSchema.extend({
  id: z.number(),
  user_id: z.number(),
  quantity: z.number(), // Quantity is present here as well
  created_at: z.string().transform(str => new Date(str)),
  updated_at: z.string().transform(str => new Date(str)),
});

// 4. Schema for UPDATING an item's quantity.
// This one is simple and doesn't need to extend the base.
export const updateCartItemSchema = z.object({
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

// 5. Schemas for API responses and route/query validation.
export const cartApiResponseSchema = z.array(cartItemSchema);
export const singleCartItemApiResponseSchema = createSingleItemResponseSchema(cartItemSchema);

// Schema for validating the cart item ID from a URL parameter.
export const cartItemIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for validating the request to delete multiple items.
export const deleteMultipleCartItemsSchema = z.object({
    ids: z.array(z.number().int().positive()).min(1, 'At least one item ID is required'),
});

// Schema for validating the GET cart query parameters.
export const getCartQuerySchema = z.object({
  user_id: z.coerce.number().int().positive('User ID must be a positive integer'),
});


// --- Inferred Types ---
// These types are derived from our schemas and will be used in the app.
export type CartProduct = z.infer<typeof cartItemSchema>;
export type AddToCartPayload = z.infer<typeof addToCartSchema>;
export type UpdateCartPayload = z.infer<typeof updateCartItemSchema>;