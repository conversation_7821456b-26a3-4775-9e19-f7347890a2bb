// using zod 3
import { z } from 'zod/v3';

const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const frontendAppSchema = z.object({
  id: z.number(),
  name: z.string(),
  identifier: z.string(),
  maintenance_status: z.boolean(),
  force_logout_before: dateSchema.nullable(),
  created_at: dateSchema,
  updated_at: dateSchema,
});

export type FrontendApp = z.infer<typeof frontendAppSchema>;