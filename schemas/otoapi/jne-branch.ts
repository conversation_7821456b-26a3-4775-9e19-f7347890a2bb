// using zod 3
import { z } from 'zod/v3';

// Base schema for JNE branch properties
const jneBranchBaseSchema = z.object({
  branch_code: z.string()
    .min(1, 'Branch code is required')
    .max(255, 'Branch code must not exceed 255 characters'),
  name: z.string()
    .min(1, 'Name is required')
    .max(255, 'Name must not exceed 255 characters'),
  is_default: z.boolean().default(false),
});

// Schema for creating a JNE branch
export const createJneBranchSchema = jneBranchBaseSchema;

// Schema for updating a JNE branch
export const updateJneBranchSchema = jneBranchBaseSchema.partial();

// Schema for the full JNE branch object returned from the API
const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const jneBranchSchema = jneBranchBaseSchema.extend({
  id: z.number(),
  created_at: dateSchema,
  updated_at: dateSchema,
});

// Infer all necessary types
export type JneBranch = z.infer<typeof jneBranchSchema>;
export type CreateJneBranch = z.infer<typeof createJneBranchSchema>;
export type UpdateJneBranch = z.infer<typeof updateJneBranchSchema>;

// Schema for route parameter validation
export const jneBranchIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for query filter parameters
export const jneBranchFilterSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  per_page: z.coerce.number().int().min(1).max(100).default(15),
  search: z.string().max(255).optional(),
  is_default: z.preprocess(
    (val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return val;
    },
    z.boolean()
  ).optional(),
  branch_code: z.string().max(255).optional(),
  name: z.string().max(255).optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'name', 'branch_code', 'is_default']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
});

export type JneBranchFilter = z.infer<typeof jneBranchFilterSchema>;