// using zod 3
import { z } from 'zod/v3';

// Base schema for JNE destination properties
const jneDestinationBaseSchema = z.object({
  country_name: z.string()
    .min(1, 'Country name is required')
    .max(255, 'Country name must not exceed 255 characters'),
  province_name: z.string()
    .min(1, 'Province name is required')
    .max(255, 'Province name must not exceed 255 characters'),
  city_name: z.string()
    .min(1, 'City name is required')
    .max(255, 'City name must not exceed 255 characters'),
  district_name: z.string()
    .min(1, 'District name is required')
    .max(255, 'District name must not exceed 255 characters'),
  subdistrict_name: z.string()
    .min(1, 'Subdistrict name is required')
    .max(255, 'Subdistrict name must not exceed 255 characters'),
  zip_code: z.string()
    .min(1, 'Zip code is required')
    .max(10, 'Zip code must not exceed 10 characters'),
  tariff_code: z.string()
    .min(1, 'Tariff code is required')
    .max(255, 'Tariff code must not exceed 255 characters'),
});

// Schema for creating a JNE destination
export const createJneDestinationSchema = jneDestinationBaseSchema;

// Schema for updating a JNE destination
export const updateJneDestinationSchema = jneDestinationBaseSchema.partial();

// Schema for the full JNE destination object returned from the API
const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const jneDestinationSchema = jneDestinationBaseSchema.extend({
  id: z.number(),
  created_at: dateSchema,
  updated_at: dateSchema,
});

// Infer all necessary types
export type JneDestination = z.infer<typeof jneDestinationSchema>;
export type CreateJneDestination = z.infer<typeof createJneDestinationSchema>;
export type UpdateJneDestination = z.infer<typeof updateJneDestinationSchema>;

// Schema for route parameter validation
export const jneDestinationIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for query filter parameters
export const jneDestinationFilterSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  per_page: z.coerce.number().int().min(1).max(100).default(15),
  search: z.string().max(255).optional(),
  country_name: z.string().max(255).optional(),
  province_name: z.string().max(255).optional(),
  city_name: z.string().max(255).optional(),
  district_name: z.string().max(255).optional(),
  subdistrict_name: z.string().max(255).optional(),
  zip_code: z.string().max(10).optional(), // zip_code and pos_code is the same
  tariff_code: z.string().max(255).optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'country_name', 'province_name', 'city_name', 'district_name', 'subdistrict_name', 'zip_code', 'tariff_code']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
});

export type JneDestinationFilter = z.infer<typeof jneDestinationFilterSchema>;