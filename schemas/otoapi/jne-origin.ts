// using zod 3
import { z } from 'zod/v3';

// Base schema for JNE origin properties
const jneOriginBaseSchema = z.object({
  origin_code: z.string()
    .min(1, 'Origin code is required')
    .max(255, 'Origin code must not exceed 255 characters'),
  name: z.string()
    .min(1, 'Name is required')
    .max(255, 'Name must not exceed 255 characters'),
  is_default: z.boolean().default(false),
});

// Schema for creating a JNE origin
export const createJneOriginSchema = jneOriginBaseSchema;

// Schema for updating a JNE origin
export const updateJneOriginSchema = jneOriginBaseSchema.partial();

// Schema for the full JNE origin object returned from the API
const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const jneOriginSchema = jneOriginBaseSchema.extend({
  id: z.number(),
  created_at: dateSchema,
  updated_at: dateSchema,
});

// Infer all necessary types
export type JneOrigin = z.infer<typeof jneOriginSchema>;
export type CreateJneOrigin = z.infer<typeof createJneOriginSchema>;
export type UpdateJneOrigin = z.infer<typeof updateJneOriginSchema>;

// Schema for route parameter validation
export const jneOriginIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for query filter parameters
export const jneOriginFilterSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  per_page: z.coerce.number().int().min(1).max(100).default(15),
  search: z.string().max(255).optional(),
  is_default: z.preprocess(
    (val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return val;
    },
    z.boolean()
  ).optional(),
  origin_code: z.string().max(255).optional(),
  name: z.string().max(255).optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'name', 'origin_code', 'is_default']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
});

export type JneOriginFilter = z.infer<typeof jneOriginFilterSchema>;