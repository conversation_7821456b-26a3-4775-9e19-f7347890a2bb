// using zod 3
import { z } from 'zod/v3';

const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

// Based on MembershipLevelResource.php
// Admin-only fields are included here but will only be present in the API response if the authenticated user is an admin.
export const membershipLevelSchema = z.object({
  id: z.number(),
  name: z.string(),
  discount_percentage: z.preprocess(
    (val) => typeof val === 'string' ? parseFloat(val) : val,
    z.number()
  ),
  description: z.string().optional(),
  is_default: z.boolean().optional(),
  created_at: dateSchema.optional(),
  updated_at: dateSchema.optional(),
});

export type MembershipLevel = z.infer<typeof membershipLevelSchema>;