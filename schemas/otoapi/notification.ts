// using zod 3
import { z } from 'zod/v3';

// Helper schema for dates
const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

// Simplified user schema for notification context
const notificationUserSchema = z.object({
  id: z.number(),
  name: z.string(),
  role: z.string().optional(), // Role is not always present
});

// --- Schemas for specific notification data types ---

const orderSchema = z.object({
    id: z.number(),
    jubelio_order_id: z.number(),
    status: z.string().optional(), // Not in status updated
    old_status: z.string().optional(),
    new_status: z.string().optional(),
    grand_total: z.string(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
});

const userCreatedDataSchema = z.object({
    title: z.string(),
    message: z.string(),
    type: z.literal('user'),
    action: z.literal('created'),
    user: notificationUserSchema,
    created_by: notificationUserSchema,
});

const welcomeDataSchema = z.object({
    title: z.string(),
    message: z.string(),
    type: z.literal('user'),
    action: z.literal('verified'),
});

const orderCreatedDataSchema = z.object({
    title: z.string(),
    message: z.string(),
    type: z.literal('order'),
    action: z.literal('created'),
    order: orderSchema,
    created_by: notificationUserSchema,
});

const orderStatusUpdatedDataSchema = z.object({
    title: z.string(),
    message: z.string(),
    type: z.literal('order'),
    action: z.literal('status_updated'),
    order: orderSchema,
    updated_by: notificationUserSchema.nullable(),
    order_owner: notificationUserSchema,
});

const orderPaidDataSchema = z.object({
    title: z.string(),
    message: z.string(),
    type: z.literal('order'),
    action: z.literal('paid'),
    order: orderSchema,
    updated_by: notificationUserSchema.nullable(),
    order_owner: notificationUserSchema,
});

// Use a simple union. Type narrowing will be done in the components.
const notificationDataSchema = z.union([
    userCreatedDataSchema,
    welcomeDataSchema,
    orderCreatedDataSchema,
    orderStatusUpdatedDataSchema,
    orderPaidDataSchema,
]);


// --- Main Notification Schema ---

export const notificationSchema = z.object({
  id: z.string(),
  type: z.string(), // The full class name from Laravel
  notifiable_type: z.string(),
  notifiable_id: z.number(),
  data: notificationDataSchema,
  read_at: dateSchema.nullable(),
  created_at: dateSchema,
  updated_at: dateSchema,
});

export type Notification = z.infer<typeof notificationSchema>;

// --- API Response Schemas ---

const laravelPaginationSchema = z.object({
    current_page: z.number(),
    data: z.array(notificationSchema),
    first_page_url: z.string().url().nullable(),
    from: z.number().nullable(),
    last_page: z.number(),
    last_page_url: z.string().url().nullable(),
    links: z.array(z.object({
        url: z.string().url().nullable(),
        label: z.string(),
        active: z.boolean(),
    })),
    next_page_url: z.string().url().nullable(),
    path: z.string().url(),
    per_page: z.number(),
    prev_page_url: z.string().url().nullable(),
    to: z.number().nullable(),
    total: z.number(),
});

export const paginatedNotificationsResponseSchema = z.object({
    message: z.string(),
    data: laravelPaginationSchema,
});

export type PaginatedNotificationsResponse = z.infer<typeof paginatedNotificationsResponseSchema>;

export const unreadCountSchema = z.object({
    unread_count: z.number(),
});

export const unreadCountResponseSchema = z.object({
    message: z.string(),
    data: unreadCountSchema,
});

export type UnreadCountResponse = z.infer<typeof unreadCountResponseSchema>;
