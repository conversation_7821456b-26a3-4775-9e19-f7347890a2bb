// using zod 3
import { z } from 'zod/v3';

const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const productDiscountSchema = z.object({
  id: z.number(),
  jubelio_item_group_id: z.number(),
  name: z.string().nullable(),
  max_discount_percentage: z.string(), // The backend sends it as a string, will be parsed to number
  created_at: dateSchema,
  updated_at: dateSchema,
});

export const batchUpdateProductDiscountSchema = z.object({
    discounts: z.array(z.object({
        jubelio_item_group_id: z.number(),
        name: z.string().nullable().optional(),
        max_discount_percentage: z.number().min(0).max(100),
    }))
});

export const batchIndexProductDiscountSchema = z.object({
    jubelio_item_group_ids: z.array(z.string()),
});

export const batchDestroyProductDiscountSchema = z.object({
    jubelio_item_group_ids: z.array(z.string()),
});


export type ProductDiscount = z.infer<typeof productDiscountSchema>;
export type BatchUpdateProductDiscount = z.infer<typeof batchUpdateProductDiscountSchema>;
export type BatchIndexProductDiscount = z.infer<typeof batchIndexProductDiscountSchema>;
export type BatchDestroyProductDiscount = z.infer<typeof batchDestroyProductDiscountSchema>;

// Schema for query filter parameters
export const productDiscountFilterSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  per_page: z.coerce.number().int().min(1).max(100).default(10),
  search: z.string().max(255).nullable().optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'jubelio_item_group_id', 'name', 'max_discount_percentage']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
});

export type ProductDiscountFilter = z.infer<typeof productDiscountFilterSchema>;