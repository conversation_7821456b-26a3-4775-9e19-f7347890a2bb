// using zod v3
import { z } from 'zod/v3';
import { userSchema } from './user';

// Base schema for shipping address properties
const shippingAddressBaseSchema = z.object({
  shipping_full_name: z.string().min(1, 'Full name is required').max(255),
  shipping_phone: z.string().min(1, 'Phone number is required').max(20),
  shipping_address: z.string().min(1, 'Address is required').max(1000),
  shipping_province_id: z.string().min(1, 'Province ID is required').max(255),
  shipping_province: z.string().min(1, 'Province is required').max(255),
  shipping_city_id: z.string().min(1, 'City ID is required').max(255),
  shipping_city: z.string().min(1, 'City is required').max(255),
  shipping_district_id: z.string().min(1, 'District ID is required').max(255),
  shipping_district: z.string().min(1, 'District is required').max(255),
  shipping_subdistrict_id: z.string().min(1, 'Subdistrict ID is required').max(255),
  shipping_subdistrict: z.string().min(1, 'Subdistrict is required').max(255),
  shipping_post_code: z.string().max(10).nullable().optional(),
  user_id: z.number().int().min(1, 'User ID is required'),
});

// Schema for creating a shipping address
export const createShippingAddressSchema = shippingAddressBaseSchema;

// Schema for updating a shipping address, all fields are optional
export const updateShippingAddressSchema = shippingAddressBaseSchema.partial();

// Schema for the full shipping address object from GET requests
export const shippingAddressSchema = shippingAddressBaseSchema.extend({
  id: z.number(),
  user: userSchema.optional(), // User relation is optional
  created_at: z.string(),
  updated_at: z.string(),
});

// Infer all necessary types
export type ShippingAddress = z.infer<typeof shippingAddressSchema>;
export type CreateShippingAddress = z.infer<typeof createShippingAddressSchema>;
export type UpdateShippingAddress = z.infer<typeof updateShippingAddressSchema>;

// Schema for route parameter validation
export const shippingAddressIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for query filter parameters
export const shippingAddressFilterSchema = z.object({
  user_id: z.coerce.number().int().min(1).optional(),
  search: z.string().max(255).optional(),
  sort_by: z.enum(['id', 'shipping_full_name', 'shipping_phone', 'shipping_province', 'shipping_city', 'created_at', 'user.name']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
  per_page: z.coerce.number().int().min(1).max(100).default(15),
  page: z.coerce.number().int().min(1).default(1)
});

export type ShippingAddressFilter = z.infer<typeof shippingAddressFilterSchema>;
