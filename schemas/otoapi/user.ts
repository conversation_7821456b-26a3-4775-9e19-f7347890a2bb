// using zod 3
import { z } from 'zod/v3';
import { frontendAppSchema } from './fronten-app';
import { membershipLevelSchema } from './membership-level';

// 1. Define a BASE schema with fields common to create and update
// Base schema for user properties
const userBaseSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(255, 'Name must not exceed 255 characters'),
  email: z.string()
    .email('Invalid email format')
    .max(255, 'Email must not exceed 255 characters'),
  phone: z.string()
    .min(1, 'Phone number is required')
    .max(20, 'Phone number must not exceed 20 characters'),
  role: z.enum(['admin', 'reseller'], {
    required_error: 'Role is required',
    invalid_type_error: 'Role must be either admin or reseller'
  }),
});

// Schema for creating a user, extends base and adds password and app IDs
export const createUserSchema = userBaseSchema.extend({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[a-z]/, 'Password must contain lowercase letters')
    .regex(/[A-Z]/, 'Password must contain uppercase letters')
    .regex(/[0-9]/, 'Password must contain numbers'),
  frontend_app_ids: z.array(z.number().int().positive()).min(1, 'At least one frontend app is required'),
});

// Schema for updating a user, makes all fields optional
export const updateUserSchema = userBaseSchema.extend({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[a-z]/, 'Password must contain lowercase letters')
    .regex(/[A-Z]/, 'Password must contain uppercase letters')
    .regex(/[0-9]/, 'Password must contain numbers')
    .optional(),
  frontend_app_ids: z.array(z.number().int().positive()).min(1, 'At least one frontend app is required').optional(),
}).partial();

// Schema for the full user object returned from the API
const dateSchema = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) return new Date(arg);
}, z.date());

export const userSchema = userBaseSchema.extend({
  id: z.number(),
  jubelio_contact_id: z.number().nullable(),
  email_verified: z.boolean(),
  is_active: z.boolean(),
  created_at: dateSchema,
  updated_at: dateSchema,
  frontend_apps: z.array(frontendAppSchema).optional(),
  membership_level: membershipLevelSchema.optional(),
});


// Infer all necessary types
export type User = z.infer<typeof userSchema>;
export type CreateUser = z.infer<typeof createUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;

// Schema for updating the user's own profile (less restrictive)
export const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255).optional(),
  phone: z.string().min(1, 'Phone is required').max(20).optional(),
});
export type UpdateProfile = z.infer<typeof updateProfileSchema>;


// Schema for changing password
export const changePasswordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[a-z]/, 'Password must contain lowercase letters')
    .regex(/[A-Z]/, 'Password must contain uppercase letters')
    .regex(/[0-9]/, 'Password must contain numbers'),
  new_password_confirmation: z.string().min(1, 'New password confirmation is required'),
}).refine(data => data.new_password === data.new_password_confirmation, {
  message: "Passwords don't match",
  path: ["new_password_confirmation"], // path of error
});
export type ChangePassword = z.infer<typeof changePasswordSchema>;


// Schema for route parameter validation
export const userIdSchema = z.object({
  id: z.coerce.number().int().min(1, 'ID must be a positive integer'),
});

// Schema for query filter parameters
export const userFilterSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  per_page: z.coerce.number().int().min(1).max(100).default(15),
  search: z.string().max(255).optional(),
  role: z.enum(['admin', 'reseller']).optional(),
  sort_by: z.enum(['id', 'name', 'email', 'created_at']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
});

export type UserFilter = z.infer<typeof userFilterSchema>;