import { z } from 'zod/v3';

// Based on RajaOngkirDestination interface
export const rajaOngkirDestinationSchema = z.object({
  id: z.number(),
  label: z.string(),
  province_name: z.string(),
  city_name: z.string(),
  district_name: z.string(),
  subdistrict_name: z.string(),
  zip_code: z.string(),
});

// Based on RajaOngkirShippingCost interface
export const rajaOngkirShippingCostSchema = z.object({
  name: z.string(),
  code: z.string(),
  service: z.string(),
  description: z.string(),
  cost: z.number(),
  etd: z.string(),
});

// Based on DomesticShippingParams interface
export const domesticShippingParamsSchema = z.object({
  origin: z.string(),
  destination: z.string(),
  weight: z.number(),
  courier: z.string(),
  price: z.string().optional(),
});

// Based on DomesticDestinationParams interface
export const domesticDestinationParamsSchema = z.object({
  search: z.string(),
  limit: z.number().optional(),
  offset: z.number().optional(),
});

// --- Inferred Types ---
export type RajaOngkirDestination = z.infer<typeof rajaOngkirDestinationSchema>;
export type RajaOngkirShippingCost = z.infer<typeof rajaOngkirShippingCostSchema>;
export type DomesticShippingParams = z.infer<typeof domesticShippingParamsSchema>;
export type DomesticDestinationParams = z.infer<typeof domesticDestinationParamsSchema>;