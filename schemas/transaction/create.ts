import { z } from 'zod/v3'

// From App\Models\Order
export const SHIPPING_TYPES = [
  'expedition', 
  'online_recipient', 
  'store_pickup', 
  'jne_expedition'
] as const
export type ShippingType = typeof SHIPPING_TYPES[number]
const STATUSES = [
  'pending',
  'paid',
  'confirmed',
  'processing',
  'shipped',
  'delivered',
  'completed',
  'cancelled',
  'refunded'
] as const

const OrderItemSchema = z.object({
  jubelio_item_id: z.number(),
  jubelio_item_group_id: z.number(),
  sku: z.string().max(255),
  name: z.string().max(255),
  unit: z.string().max(255), // example buah, biji, kotak and etc
  original_price: z.number().min(0),
  quantity: z.number().min(1),
  weight: z.number().min(0),
  variant: z.array(z.any()).nullable(),
  image: z.string().max(255).nullable(),
  tax_id: z.number(),
  tax_rate_percent: z.number().min(0).max(100)
})
export type OtoApiOrderItem = z.infer<typeof OrderItemSchema>

export const CreateOtoApiOrderSchema = z.object({
  jubelio_order_id: z.number(),
  user_id: z.number(),
  shipping_address_id: z.number(),
  user_name: z.string().max(255),
  user_phone: z.string().max(20),
  shipping_full_name: z.string().max(255),
  shipping_phone: z.string().max(20),
  shipping_address: z.string().max(65535),
  shipping_province_id: z.string().max(255),
  shipping_province: z.string().max(255),
  shipping_city_id: z.string().max(255),
  shipping_city: z.string().max(255),
  shipping_district_id: z.string().max(255),
  shipping_district: z.string().max(255),
  shipping_subdistrict_id: z.string().max(255),
  shipping_subdistrict: z.string().max(255),
  shipping_post_code: z.string().max(10).nullable(),
  status: z.enum(STATUSES).optional(),
  shipping_type: z.enum(SHIPPING_TYPES),
  tracking_number: z.string().nullable(),
  sub_total: z.number().min(0),
  shipping_cost: z.number().min(0).optional(),
  insurance_cost: z.number().min(0).optional(),
  grand_total: z.number().min(0),
  payment_method: z.string().max(100).nullable(),
  expedition: z
    .object({
    name: z.string().max(255),
    code: z.string().max(50),
    service: z.string().max(100),
    description: z.string().max(255).nullable(),
    cost: z.number().min(0),
    etd: z.string().max(100).nullable()
    })
    .nullable(),
  etc: z.record(z.any()).nullable(),
  items: z.array(OrderItemSchema).min(1)
})

// Infer the TypeScript type from the Zod schema
export type CreateOtoApiOrderRequest = z.infer<typeof CreateOtoApiOrderSchema>