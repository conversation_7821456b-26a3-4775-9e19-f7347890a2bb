import { z } from 'zod/v3'

// Schema for the Expedition object
export const ExpeditionSchema = z.object({
  name: z.string(),
  code: z.string(),
  service: z.string(),
  description: z.string().nullable(),
  cost: z.number(),
  etd: z.string().nullable(),
})

// Schema for a single item within an order
export const OtoapiOrderItemSchema = z.object({
  id: z.number(),
  jubelio_item_id: z.number(),
  sku: z.string(),
  name: z.string(),
  unit: z.string(),
  price: z.string(), // Keep as string as it comes from backend
  original_price: z.string().nullable().optional(),
  discount_percentage: z.coerce.number().nullable().optional(),
  quantity: z.number(),
  weight: z.string(), // Keep as string
  variant: z.array(z.object({
    label: z.string(),
    value: z.string(),
  })).nullable(),
  image: z.string().nullable(),
  tax_id: z.number(),
  tax_rate_percent: z.string(), // Keep as string
})

// Schema for the base Order object (used in lists)
export const OrderSchema = z.object({
  id: z.number(),
  jubelio_order_id: z.number(),
  jubelio_contact_id: z.number(),
  user_name: z.string(),
  shipping_full_name: z.string(),
  shipping_type: z.string().nullable(),
  status: z.string(),
  shipper: z.string().nullable(),
  tracking_number: z.string().nullable(),
  sub_total: z.string(),
  total_discount: z.string(),
  shipping_cost: z.string(),
  insurance_cost: z.string(),
  grand_total: z.string(),
  invoice_url: z.string().nullable(),
  expired_at: z.string().nullable(),
  created_at: z.string(),
})

// Schema for the detailed Order object
export const OrderDetailSchema = OrderSchema.extend({
  user_phone: z.string(),
  shipping_phone: z.string(),
  shipping_address: z.string(),
  shipping_province_id: z.string(),
  shipping_province: z.string(),
  shipping_city_id: z.string(),
  shipping_city: z.string(),
  shipping_district_id: z.string(),
  shipping_district: z.string(),
  shipping_subdistrict_id: z.string(),
  shipping_subdistrict: z.string(),
  shipping_post_code: z.string(),
  payment_method: z.string().nullable(),
  invoice_url: z.string().nullable(),
  expired_at: z.string().nullable(),
  expedition: ExpeditionSchema.nullable(),
  etc: z.any().nullable(),
  updated_at: z.string(),
  items: z.array(OtoapiOrderItemSchema),
  user: z.object({
    id: z.number(),
    name: z.string(),
    email: z.string(),
    phone: z.string(),
    role: z.string(),
    jubelio_contact_id: z.number(),
    email_verified: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
  }),
  shippingAddress: z.object({
    id: z.number(),
    shipping_full_name: z.string(),
    shipping_phone: z.string(),
    shipping_address: z.string(),
    shipping_province_id: z.string(),
    shipping_province: z.string(),
    shipping_city_id: z.string(),
    shipping_city: z.string(),
    shipping_district_id: z.string(),
    shipping_district: z.string(),
    shipping_subdistrict_id: z.string(),
    shipping_subdistrict: z.string(),
    shipping_post_code: z.string(),
  }),
  payments: z.array(z.any()), // Assuming payments can be any array for now
})

// Schema for the API response for a list of orders
export const OrderResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(OrderSchema),
  meta: z.object({
    current_page: z.number(),
    per_page: z.number(),
    total: z.number(),
    last_page: z.number(),
    from: z.number().nullable(),
    to: z.number().nullable(),
    path: z.string(),
    links: z.array(
      z.object({
        url: z.string().nullable(),
        label: z.string(),
        active: z.boolean(),
      })
    ),
  }).optional(),
  links: z.object({
    first: z.string().nullable(),
    last: z.string().nullable(),
    prev: z.string().nullable(),
    next: z.string().nullable(),
  }).optional(),
})

// Schema for the API response for a single detailed order
export const OrderDetailResponseSchema = z.object({
  success: z.boolean(),
  data: OrderDetailSchema,
})

// Schema for Order Filter
export const OrderFilterSchema = z.object({
  status: z.string().optional(),
  jubelio_contact_id: z.coerce.number().optional(),
  jubelio_order_id: z.coerce.number().optional(),
  user_id: z.coerce.number().optional(),
  search: z.string().optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'jubelio_order_id', 'status', 'grand_total']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
  per_page: z.coerce.number().optional(),
  page: z.coerce.number().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  min_total: z.coerce.number().optional(),
  max_total: z.coerce.number().optional(),
})

// INFERRED TYPES
export type Expedition = z.infer<typeof ExpeditionSchema>
export type OtoapiOrderItem = z.infer<typeof OtoapiOrderItemSchema>
export type Order = z.infer<typeof OrderSchema>
export type OrderDetail = z.infer<typeof OrderDetailSchema>
export type OrderResponse = z.infer<typeof OrderResponseSchema>
export type OrderDetailResponse = z.infer<typeof OrderDetailResponseSchema>
export type OrderFilter = z.infer<typeof OrderFilterSchema>