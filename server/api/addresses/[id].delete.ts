import { otoapiApiRequest } from '~/server/services/otoapi'
import { shippingAddressIdSchema } from '~/schemas/otoapi/shipping-address'

export default defineEventHandler(async (event) => {
  try {
    // Only allow DELETE method
    assertMethod(event, 'DELETE')
    
    // Get route parameters
    const params = getRouterParams(event)
    
    // Validate route parameters
    const paramsValidation = shippingAddressIdSchema.safeParse(params)
    
    if (!paramsValidation.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid address ID',
        data: paramsValidation.error.errors
      })
    }
    
    const { id } = paramsValidation.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/shipping-addresses/${id}`,
      'DELETE',
      event
    )
    
    return {
      success: true,
      message: 'Shipping address deleted successfully'
    }
    
  } catch (error: any) {
    console.error('Delete address error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Shipping address not found'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete shipping address'
    })
  }
})
