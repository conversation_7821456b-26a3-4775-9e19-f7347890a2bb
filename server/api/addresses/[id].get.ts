import { otoapiApiRequest } from '~/server/services/otoapi'
import { shippingAddressIdSchema } from '~/schemas/otoapi/shipping-address'

export default defineEventHandler(async (event) => {
  try {
    // Only allow GET method
    assertMethod(event, 'GET')
    
    // Get route parameters
    const params = getRouterParams(event)
    
    // Validate route parameters
    const validationResult = shippingAddressIdSchema.safeParse(params)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid address ID',
        data: validationResult.error.errors
      })
    }
    
    const { id } = validationResult.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/shipping-addresses/${id}`,
      'GET',
      event
    )
    
    return {
      success: true,
      data: response.data || response
    }
    
  } catch (error: any) {
    console.error('Get address error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Shipping address not found'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch shipping address'
    })
  }
})
