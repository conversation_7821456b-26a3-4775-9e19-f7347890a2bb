import { otoapiApiRequest } from '~/server/services/otoapi'
import { updateShippingAddressSchema, shippingAddressIdSchema } from '~/schemas/otoapi/shipping-address'

export default defineEventHandler(async (event) => {
  try {
    // Only allow PUT method
    assertMethod(event, 'PUT')
    
    // Get route parameters
    const params = getRouterParams(event)
    
    // Validate route parameters
    const paramsValidation = shippingAddressIdSchema.safeParse(params)
    
    if (!paramsValidation.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid address ID',
        data: paramsValidation.error.errors
      })
    }
    
    const { id } = paramsValidation.data
    
    // Get request body
    const body = await readBody(event)
    
    // Validate request body
    const validationResult = updateShippingAddressSchema.safeParse(body)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: {
          errors: validationResult.error.flatten().fieldErrors
        }
      })
    }
    
    const validatedData = validationResult.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/shipping-addresses/${id}`,
      'PUT',
      event,
      validatedData
    )
    
    return {
      success: true,
      message: 'Shipping address updated successfully',
      data: response.data || response
    }
    
  } catch (error: any) {
    console.error('Update address error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Shipping address not found'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: error.data?.errors || {}
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update shipping address'
    })
  }
})
