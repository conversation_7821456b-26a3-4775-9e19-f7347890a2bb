import { z } from 'zod/v3'
import { otoapiApiRequest } from '~/server/services/otoapi'

const bulkDeleteSchema = z.object({
  ids: z.array(z.number().int().min(1)).min(1, 'At least one ID is required')
})

export default defineEventHandler(async (event) => {
  try {
    // Only allow DELETE method
    assertMethod(event, 'DELETE')
    
    // Get request body
    const body = await readBody(event)
    
    // Validate request body
    const validationResult = bulkDeleteSchema.safeParse(body)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: {
          errors: validationResult.error.flatten().fieldErrors
        }
      })
    }
    
    const { ids } = validationResult.data
    
    // Make request to OtoAPI - using DELETE method and sending body
    const response = await otoapiApiRequest(
      '/shipping-addresses',
      'DELETE',
      event,
      { ids }
    )
    
    return {
      success: true,
      message: response.message || 'Shipping addresses deleted successfully'
    }
    
  } catch (error: any) {
    console.error('Bulk delete addresses error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: error.data?.errors || {}
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete shipping addresses'
    })
  }
})
