import { otoapiApiRequest } from '~/server/services/otoapi'
import { shippingAddressFilterSchema } from '~/schemas/otoapi/shipping-address'

export default defineEventHandler(async (event) => {
  try {
    // Only allow GET method
    assertMethod(event, 'GET')
    
    // Get query parameters
    const query = getQuery(event)
    
    // Validate query parameters
    const validationResult = shippingAddressFilterSchema.safeParse(query)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: validationResult.error.errors
      })
    }
    
    const validatedQuery = validationResult.data
    
    // Build query string for OtoAPI
    const queryParams = new URLSearchParams()
    
    // Add all valid parameters to query string
    Object.entries(validatedQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/shipping-addresses?${queryParams.toString()}`,
      'GET',
      event
    )
    
    return {
      success: true,
      data: response.data || response,
      meta: response.meta || null,
      links: response.links || null
    }
    
  } catch (error: any) {
    console.error('Get addresses error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request parameters'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: error.data?.errors || {}
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch shipping addresses'
    })
  }
})
