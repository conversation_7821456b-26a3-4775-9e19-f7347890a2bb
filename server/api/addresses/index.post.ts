import { otoapiApiRequest } from '~/server/services/otoapi'
import { createShippingAddressSchema } from '~/schemas/otoapi/shipping-address'

export default defineEventHandler(async (event) => {
  try {
    // Only allow POST method
    assertMethod(event, 'POST')
    
    // Get request body
    const body = await readBody(event)
    
    // Validate request body
    const validationResult = createShippingAddressSchema.safeParse(body)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: {
          errors: validationResult.error.flatten().fieldErrors
        }
      })
    }
    
    const validatedData = validationResult.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      '/shipping-addresses',
      'POST',
      event,
      validatedData
    )
    
    return {
      success: true,
      message: 'Shipping address created successfully',
      data: response.data || response
    }
    
  } catch (error: any) {
    console.error('Store address error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: error.data?.errors || {}
      })
    }
    
    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request data'
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create shipping address'
    })
  }
})
