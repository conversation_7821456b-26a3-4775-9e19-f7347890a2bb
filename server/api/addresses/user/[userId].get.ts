// not used
import { z } from 'zod/v3'
import { otoapiApiRequest } from '~/server/services/otoapi'

// Define validation schema for route parameters
const getUserAddressesSchema = z.object({
  userId: z.coerce.number().int().min(1)
})

export default defineEventHandler(async (event) => {
  try {
    // Only allow GET method
    assertMethod(event, 'GET')
    
    // Get route parameters
    const params = getRouterParams(event)
    
    // Validate route parameters
    const validationResult = getUserAddressesSchema.safeParse(params)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid user ID',
        data: validationResult.error.errors
      })
    }
    
    const { userId } = validationResult.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/shipping/${userId}/addresses`,
      'GET',
      event
    )
    
    return {
      success: true,
      data: response.data || response
    }
    
  } catch (error: any) {
    console.error('Get user addresses error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user shipping addresses'
    })
  }
})
