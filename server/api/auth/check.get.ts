import { getSecureCookie } from '~/server/utils/cookies'
import { extractUserData } from '~/server/utils/jwt'
import { otoapiApiRequest } from '~/server/services/otoapi'
import { createUserSession } from '~/server/services/session'
import type { BackendResponse } from '~/server/services/session'

export default defineEventHandler(async (event) => {
  try {
    // Get token from cookie using our utility
    const token = getSecureCookie(event, 'reseller_auth_token')
    
    if (!token) {
      return { authenticated: false }
    }
    
    // Extract user data from token using our new utility
    const userData = await extractUserData(token)
    
    if (!userData) {
      return { authenticated: false }
    }
    
    // Call the backend to get fresh user data
    const profileResponse = await otoapiApiRequest('/profile', 'GET', event)

    if (!profileResponse || !profileResponse.data) {
      console.error('Failed to fetch fresh user profile from backend')
      return { authenticated: false }
    }

    // We have fresh data, now we need to re-create the session
    // which will generate a new JWT and set all cookies.
    // The backend login response and profile response structures are slightly different,
    // so we adapt the profile response to match what createUserSession expects.
    const otoapiToken = getSecureCookie(event, 'reseller_otoapi_token')
    const jubelioToken = getSecureCookie(event, 'reseller_jubelio_token')

    if (!otoapiToken || !jubelioToken) {
      console.error('Missing API or Jubelio token during session refresh')
      return { authenticated: false }
    }

    const backendResponse: BackendResponse = {
      token: otoapiToken,
      token_type: 'Bearer',
      jubelio_token: jubelioToken,
      user: profileResponse.data
    }

    // Create a new session with the fresh data. This will also set a new CSRF token.
    const newSession = await createUserSession(event, backendResponse)

    return {
      authenticated: true,
      ...newSession
    }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authenticated: false }
  }
})
