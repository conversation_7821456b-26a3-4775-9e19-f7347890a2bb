import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { forgotPasswordSchema } from '~/schemas/frontend/reset-password'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { turnstileToken, ...forgotPasswordDetails } = body;

    if (!turnstileToken) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Turnstile token not provided.'
      });
    }
    const isValid = await verifyTurnstileToken(turnstileToken);
    if (!isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid Turnstile token.'
      });
    }
    
    const validation = forgotPasswordSchema.safeParse(forgotPasswordDetails)

    if (!validation.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid input',
        data: validation.error.flatten(),
      })
    }

    const { email } = validation.data

    const response = await otoapiPublicRequest('/forgot-password', 'POST', { email })

    logger.info(`Forgot password request for: ${email}`)

    return {
      status: 'success',
      message: response.message,
    }
  } catch (error: any) {
    logger.error('Forgot password error:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    })

    if (error.statusCode) {
      throw createError({
        statusCode: error.statusCode,
        statusMessage: error.data?.message || error.statusMessage,
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'An unexpected error occurred.',
    })
  }
})