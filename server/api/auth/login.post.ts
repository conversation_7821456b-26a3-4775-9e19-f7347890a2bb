// import { verifyTurnstileToken } from '#turnstile';
import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { createUserSession } from '~/server/services/session'
import { loginSchema } from '~/schemas/frontend/login'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { turnstileToken, ...loginDetails } = body;

    if (!turnstileToken) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Turnstile token not provided.'
      });
    }
    const isValid = await verifyTurnstileToken(turnstileToken);
    if (!isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid Turnstile token.'
      });
    }
    
    const validation = loginSchema.safeParse(loginDetails)

    if (!validation.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid input',
        data: validation.error.flatten(),
      })
    }

    const { email, password, rememberMe } = validation.data

    // Step 1: Login to OtoAPI
    const loginData = await otoapiPublicRequest('/login', 'POST', { email, password })
    logger.info('Login successful:', loginData)

    // Step 2: Create user session
    const session = await createUserSession(event, loginData, { rememberMe })

    logger.info(`Successful login for user: ${email}, jubelio_contact_id: ${loginData.user.jubelio_contact_id}`)

    return session

  } catch (error: any) {
    logger.error('Login error:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data, // Log the data payload for better debugging
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })

    // Check for the specific error code from our Laravel backend
    if (error.statusCode === 403 && error.data?.code === 'ACCOUNT_INACTIVE') {
      // This is our new inactive account error.
      // We just re-throw it as is, so the frontend store can handle it.
      throw createError({
        statusCode: 403,
        statusMessage: error.data.message || 'Account is pending activation.',
        data: {
          code: 'ACCOUNT_INACTIVE'
        }
      })
    }

    // This handles the original unverified email case
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Your email is not verified. Please resend the verification email.',
        data: {
          ...error.data,
          isEmailUnverified: true,
        },
      })
    }

    // Re-throw any other errors that already have a status code
    if (error.statusCode) {
      throw error
    }

    // Generic error for unexpected issues
    throw createError({
      statusCode: 500,
      statusMessage: 'An unexpected error occurred during login'
    })
  }
})

