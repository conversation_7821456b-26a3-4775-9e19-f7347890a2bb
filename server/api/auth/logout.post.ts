import { clearCookie } from '~/server/utils/cookies'
import { validateCsrfToken } from '~/server/utils/csrf'
import { clearJubelioToken } from '~/server/services/jubelioToken'
import { clearOtoapiToken, otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    // Validate CSRF token
    const csrfToken = getRequestHeader(event, 'X-CSRF-Token')
    if (!csrfToken || !validateCsrfToken(event, csrfToken)) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Invalid CSRF token'
      })
    }

    // Optional: Call OtoAPI logout endpoint if it exists
    try {
      await otoapiApiRequest('/logout', 'POST', event)
      logger.info('Successfully logged out from OtoAPI')
    } catch (error: any) {
      // Don't fail the entire logout if OtoAPI logout fails
      logger.warn('OtoAPI logout failed, but continuing with local logout:', error.message)
    }

    // Clear all authentication cookies and tokens
    clearCookie(event, 'reseller_auth_token')
    clearCookie(event, 'reseller_csrf_token')
    clearOtoapiToken(event)
    clearJubelioToken(event)

    logger.info('User logged out successfully')

    return {
      status: 'success',
      message: 'Logged out successfully'
    }

  } catch (error: any) {
    logger.error('Logout error:', {
      message: error.message,
      statusCode: error.statusCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })

    // Re-throw if it's already a proper HTTP error
    if (error.statusCode) {
      throw error
    }

    // Generic error for unexpected issues
    throw createError({
      statusCode: 500,
      statusMessage: 'An unexpected error occurred during logout'
    })
  }
})
