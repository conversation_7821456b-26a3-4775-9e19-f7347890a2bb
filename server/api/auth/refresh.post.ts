import { getAuthUser } from '~/server/middleware/02.auth'
import { generateTokenUser } from '~/server/utils/jwt'
import { setSecureCookie } from '~/server/utils/cookies'
import { setCsrfToken, validateCsrfToken } from '~/server/utils/csrf'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    // Validate CSRF token
    const csrfToken = getRequestHeader(event, 'X-CSRF-Token')
    if (!csrfToken || !validateCsrfToken(event, csrfToken)) {
      return createError({
        statusCode: 403,
        message: 'Invalid CSRF token'
      })
    }
    
    // Get current auth user
    const auth = await getAuthUser(event)
    
    if (!auth) {
      return createError({
        statusCode: 401,
        message: 'Unauthorized'
      })
    }
    
    // Generate a new token with just the user data
    const token = await generateTokenUser(auth.user)
    
    // Set the new token in a cookie using our utility
    setSecureCookie(event, 'reseller_auth_token', token, {
      maxAge: 12 * 60 * 60, // 12 hours
    })
    
    // Generate and set a new CSRF token
    const newCsrfToken = setCsrfToken(event)
    
    return {
      status: 'success',
      token,
      csrfToken: newCsrfToken
    }
  } catch (error: any) {
    logger.error('Token refresh error:', error)
    return createError({
      statusCode: 500,
      message: error.message || 'Failed to refresh token'
    })
  }
})
