import { defineEventHand<PERSON>, readBody } from 'h3'
import { registerSchema } from '~/schemas/frontend/register'
import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { turnstileToken, ...registerDetails } = body;

    if (!turnstileToken) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Turnstile token not provided.'
      });
    }
    try {
      const isValid = await verifyTurnstileToken(turnstileToken);
      if (!isValid) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Invalid Turnstile token.'
        });
      }
    } catch (turnstileError: any) {
      logger.error('Turnstile verification failed:', turnstileError);
      // Check if it's the specific fetch error
      if (turnstileError.message.includes('fetch failed')) {
        throw createError({
          statusCode: 503, // Service Unavailable
          statusMessage: 'Unable to verify captcha. Please try again later.'
        });
      }
      // Re-throw other Turnstile errors
      throw createError({
        statusCode: 500,
        statusMessage: 'An unexpected error occurred during captcha verification.'
      });
    }
    
    const result = registerSchema.safeParse(registerDetails)

    if (!result.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid registration data',
        data: result.error.errors,
      })
    }

    const { name, email, password, password_confirmation, phone } = result.data

    // Step 1: Register with OtoAPI
    const registerData = await otoapiPublicRequest('/register', 'POST', { name, email, password, password_confirmation, phone })
    logger.info('Registration successful:', registerData)

    logger.info(`Successful registration for user: ${email}`)

    return {
      status: 'success',
      message: registerData.message
    }

  } catch (error: any) {
    logger.error('Registration error:', {
      message: error.message,
      statusCode: error.statusCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      data: error.data
    })

    // Re-throw if it's already a proper HTTP error
    if (error.statusCode) {
      throw error
    }

    // Generic error for unexpected issues
    throw createError({
      statusCode: 500,
      statusMessage: 'An unexpected error occurred during registration'
    })
  }
})