import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody<{ email: string, turnstileToken: string }>(event)
    const { email, turnstileToken } = body

    if (!turnstileToken) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Turnstile token not provided.'
      });
    }
    const isValid = await verifyTurnstileToken(turnstileToken);
    if (!isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid Turnstile token.'
      });
    }

    if (!email) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email is required',
      })
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format',
      })
    }

    await otoapiPublicRequest('/email/resend', 'POST', { email })

    logger.info(`Verification email resend request for: ${email}`)
  } catch (error: any) {
    // If the error is 422, it means the email is already verified.
    // We will swallow this error and proceed to return a generic success message
    // to prevent user enumeration.
    if (error.statusCode === 422) {
      // logger.info(`Resend request for already verified email: ${email}`)
      // Do nothing, just fall through to the success response.
    } else if (error.statusCode === 429) {
      throw createError({
        statusCode: 429,
        statusMessage: 'You have requested this too many times. Please wait a moment before trying again.',
      })
    } else {
      // Log and re-throw any other unexpected errors.
      logger.error('Resend verification email error:', {
        message: error.message,
        statusCode: error.statusCode,
        data: error.data,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      })

      if (error.statusCode) {
        // Forward other errors from the backend
        throw createError({
          statusCode: error.statusCode,
          statusMessage: error.data?.message || error.statusMessage,
        })
      }

      throw createError({
        statusCode: 500,
        statusMessage: 'An unexpected error occurred while resending the verification email.',
      })
    }
  }

  // Always return a generic success message to prevent user enumeration.
  return {
    status: 'success',
    message: 'If an account with that email exists, a new verification link has been sent. If you are already verified, please try logging in.',
  }
})
