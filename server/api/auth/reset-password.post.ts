import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { resetPasswordSchema } from '~/schemas/frontend/reset-password'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { turnstileToken, ...resetPasswordDetails } = body;

    if (!turnstileToken) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Turnstile token not provided.'
      });
    }
    const isValid = await verifyTurnstileToken(turnstileToken);
    if (!isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid Turnstile token.'
      });
    }
    
    const validation = resetPasswordSchema.safeParse(resetPasswordDetails)

    if (!validation.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid input',
        data: validation.error.flatten(),
      })
    }

    const { token, email, password, password_confirmation } = validation.data

    const response = await otoapiPublicRequest('/reset-password', 'POST', {
      token,
      email,
      password,
      password_confirmation,
    })

    logger.info(`Password reset successful for: ${email}`)

    return {
      status: 'success',
      message: response.message,
    }
  } catch (error: any) {
    logger.error('Reset password error:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    })

    if (error.statusCode) {
      throw createError({
        statusCode: error.statusCode,
        statusMessage: error.data?.message || error.statusMessage,
        data: error.data,
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'An unexpected error occurred.',
    })
  }
})