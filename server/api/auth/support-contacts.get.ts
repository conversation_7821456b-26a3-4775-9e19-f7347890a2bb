import { z } from 'zod'
import { otoapiPublicRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const query = await getValidatedQuery(event, (query) => {
      return z.object({
        category: z.string().optional(),
        type: z.string().optional(),
        limit: z.coerce.number().int().min(1).optional(),
      }).parse(query)
    })

    logger.info('Validated data:', query)

    // Forward the request to the OtoAPI backend
    const response = await otoapiPublicRequest('/public/support-contacts', 'GET', query)

    return response
  } catch (error: any) {
    logger.error('Error fetching support contacts:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    })

    // Re-throw a structured error for the frontend
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.data?.message || 'Failed to fetch support contacts.',
    })
  }
})