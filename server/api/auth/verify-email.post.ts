import { z } from 'zod/v3'
import { createUserSession } from '~/server/services/session'
import { H3Event } from 'h3'
import { otoapiApiRequestWithToken } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'

const schema = z.object({
  verify_url: z.string().url(),
})

const POLLING_INTERVAL = 5000 // 5 seconds
const MAX_ATTEMPTS = 10 // 20 seconds max

async function pollForJubelioContactId(token: string, maxAttempts: number, interval: number) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const profileResponse: any = await otoapiApiRequestWithToken('/profile', 'GET', token)
      logger.info(`Polling attempt ${i + 1}:`, profileResponse)

      if (profileResponse.data && profileResponse.data.jubelio_contact_id) {
        return profileResponse.data
      }
    }
    catch (error) {
      // Log and ignore error during polling
      logger.error(`Polling attempt ${i + 1} failed:`, error)
    }
    await new Promise(resolve => setTimeout(resolve, interval))
  }
  return null
}

export default defineEventHandler(async (event: H3Event) => {
  const body = await readBody(event)

  const validation = schema.safeParse(body)
  if (!validation.success) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bad Request',
      data: validation.error.errors,
    })
  }

  try {
    const { verify_url } = validation.data
    const initialResponse = await $fetch(verify_url) as any
    logger.warn('Initial verification response:', initialResponse)

    // Handle cases where the email is already verified or other issues occur
    if (initialResponse.message === 'Email already verified.') {
      throw createError({
        statusCode: 409, // Conflict
        statusMessage: 'Email has already been verified. Please login.',
      })
    }

    // According to the new backend flow, a successful verification
    // does not return a token, only a success message.
    // We will simply return a success response to the frontend,
    // which will then handle redirecting the user.
    return {
      status: 'success',
      message: initialResponse.message || 'Email verified successfully.'
    }
  }
  catch (error) {
    logger.error('Email verification error:', error)
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid verification link or failed to process.',
    })
  }
})