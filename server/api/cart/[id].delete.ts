import { otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { cartItemIdSchema } from '~/schemas/otoapi/cart'

export default defineEventHandler(async (event) => {
  try {
    const params = await getValidatedRouterParams(event, cartItemIdSchema.parse)

    logger.debug(`Removing cart item: ${params.id}`)

    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/cart/${params.id}`,
      'DELETE',
      event
    )
    
    return response
    
  } catch (error: any) {
    logger.error('Error removing cart item:', {
      message: error.message,
      statusCode: error.statusCode
    })
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to remove cart item'
    })
  }
})
