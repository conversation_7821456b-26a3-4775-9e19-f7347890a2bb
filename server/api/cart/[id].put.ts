import { otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { updateCartItemSchema, cartItemIdSchema } from '~/schemas/otoapi/cart'

export default defineEventHandler(async (event) => {
  try {
    const params = await getValidatedRouterParams(event, cartItemIdSchema.parse)
    const body = await readValidatedBody(event, updateCartItemSchema.parse)

    logger.debug(`Updating cart item: ${params.id}`, { quantity: body.quantity })

    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/cart/${params.id}`,
      'PUT',
      event,
      body
    )
    
    return response
    
  } catch (error: any) {
    logger.error('Error updating cart item:', {
      message: error.message,
      statusCode: error.statusCode
    })
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update cart item'
    })
  }
})
