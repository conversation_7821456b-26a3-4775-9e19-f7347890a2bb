import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    // Make request to OtoAPI clear cart endpoint
    const response = await otoapiApiRequest(
      '/cart/clear',
      'DELETE',
      event
    )

    return response
  } catch (error: any) {
    console.error('Error clearing cart:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to clear cart'
    })
  }
})
