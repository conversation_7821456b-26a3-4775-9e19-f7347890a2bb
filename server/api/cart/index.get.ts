import { otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { getCartQuerySchema } from '~/schemas/otoapi/cart'

export default defineEventHandler(async (event) => {
  try {
    const query = await getValidatedQuery(event, getCartQuerySchema.parse)
    const userId = query.user_id

    logger.debug(`Fetching cart for user_id: ${userId}`)

    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/cart?user_id=${userId}`,
      'GET',
      event
    )
    
    return response
    
  } catch (error: any) {
    logger.error('Error fetching cart:', {
      message: error.message,
      statusCode: error.statusCode
    })
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch cart data'
    })
  }
})
