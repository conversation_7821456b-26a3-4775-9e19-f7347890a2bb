import { otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { addToCartSchema } from '~/schemas/otoapi/cart'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)

    const validationResult = addToCartSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.flatten()
      })
    }

    const validatedBody = validationResult.data

    logger.debug('Adding to cart', {
      item_id: validatedBody.jubelio_item_id,
      quantity: validatedBody.quantity
    })

    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      '/cart',
      'POST',
      event,
      validatedBody
    )
    
    return response
    
  } catch (error: any) {
    logger.error('Error adding to cart:', {
      message: error.message,
      statusCode: error.statusCode
    })
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to add item to cart'
    })
  }
})
