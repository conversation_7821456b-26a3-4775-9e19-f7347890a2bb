import { otoapiApiRequest } from '~/server/services/otoapi'
import { logger } from '~/utils/logger'
import { deleteMultipleCartItemsSchema } from '~/schemas/otoapi/cart'

export default defineEventHandler(async (event) => {
  try {
    const body = await readValidatedBody(event, deleteMultipleCartItemsSchema.parse)

    logger.info('Received request to delete cart items:', body.ids)

    // Make request to OtoAPI batch delete endpoint
    const response = await otoapiApiRequest(
      '/cart/multiple',
      'DELETE',
      event,
      { ids: body.ids }
    )

    return response
  } catch (error: any) {
    console.error('Error in batch delete cart items:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete cart items'
    })
  }
})
