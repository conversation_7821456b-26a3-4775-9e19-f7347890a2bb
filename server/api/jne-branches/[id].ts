import { otoapiApiRequest } from '~/server/services/otoapi'
import { jneBranchIdSchema, updateJneBranchSchema } from '~/schemas/otoapi/jne-branch'

export default defineEventHandler(async (event) => {
  const method = event.node.req.method

  // Get and validate the route parameter
  const params = event.context.params
  const idValidationResult = jneBranchIdSchema.safeParse(params)

  if (!idValidationResult.success) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid route parameters',
      data: idValidationResult.error.errors,
    })
  }

  const { id } = idValidationResult.data

  try {
    switch (method) {
      case 'GET': {
        const response = await otoapiApiRequest(
          `/jne-branches/${id}`,
          'GET',
          event
        )
        return response
      }

      case 'PUT': {
        const body = await readBody(event)
        const bodyValidationResult = updateJneBranchSchema.safeParse(body)

        if (!bodyValidationResult.success) {
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid request body',
            data: bodyValidationResult.error.errors,
          })
        }

        const validatedBody = bodyValidationResult.data
        const response = await otoapiApiRequest(
          `/jne-branches/${id}`,
          'PUT',
          event,
          validatedBody
        )
        return response
      }

      case 'DELETE': {
        const response = await otoapiApiRequest(
          `/jne-branches/${id}`,
          'DELETE',
          event
        )
        return response
      }

      default:
        throw createError({
          statusCode: 405,
          statusMessage: 'Method not allowed',
        })
    }
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})