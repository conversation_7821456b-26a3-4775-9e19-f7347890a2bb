import { otoapiApiRequest } from '~/server/services/otoapi'
import { jneBranchFilterSchema } from '~/schemas/otoapi/jne-branch'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate query parameters
    const query = getQuery(event)
    const validationResult = jneBranchFilterSchema.safeParse(query)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: validationResult.error.errors,
      })
    }

    const validatedQuery = validationResult.data

    // Build query string for OtoAPI
    const queryParams = new URLSearchParams()
    Object.entries(validatedQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        let paramValue = value.toString();
        if (key === 'is_default' && typeof value === 'boolean') {
          paramValue = value ? '1' : '0';
        }
        queryParams.append(key, paramValue);
      }
    })

    const response = await otoapiApiRequest(
      `/jne-branches?${queryParams.toString()}`,
      'GET',
      event
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})