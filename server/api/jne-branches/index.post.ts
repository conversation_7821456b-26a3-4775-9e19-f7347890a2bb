import { otoapiApiRequest } from '~/server/services/otoapi'
import { createJneBranchSchema } from '~/schemas/otoapi/jne-branch'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the request body
    const body = await readBody(event)
    const validationResult = createJneBranchSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.errors,
      })
    }

    const validatedBody = validationResult.data

    const response = await otoapiApiRequest(
      '/jne-branches',
      'POST',
      event,
      validatedBody
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})