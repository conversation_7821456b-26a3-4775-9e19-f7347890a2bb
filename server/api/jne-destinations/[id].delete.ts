import { otoapiApiRequest } from '~/server/services/otoapi'
import { jneDestinationIdSchema } from '~/schemas/otoapi/jne-destination'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the route parameter
    const params = event.context.params
    const paramsValidationResult = jneDestinationIdSchema.safeParse(params)

    if (!paramsValidationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid route parameters',
        data: paramsValidationResult.error.errors,
      })
    }

    const { id } = paramsValidationResult.data

    // Make the API request to delete the JNE destination
    const response = await otoapiApiRequest(
      `/jne-destinations/${id}`,
      'DELETE',
      event
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})