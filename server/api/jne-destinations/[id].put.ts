import { otoapiApiRequest } from '~/server/services/otoapi'
import { jneDestinationIdSchema, updateJneDestinationSchema } from '~/schemas/otoapi/jne-destination'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the route parameter
    const params = event.context.params
    const paramsValidationResult = jneDestinationIdSchema.safeParse(params)

    if (!paramsValidationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid route parameters',
        data: paramsValidationResult.error.errors,
      })
    }

    const { id } = paramsValidationResult.data
    logger.info(`Validated ID: ${id}`)

    // Get and validate the request body
    const body = await readBody(event)
    const bodyValidationResult = updateJneDestinationSchema.safeParse(body)

    if (!bodyValidationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: bodyValidationResult.error.errors,
      })
    }

    const validatedBody = bodyValidationResult.data
    logger.info(`Validated body: ${JSON.stringify(validatedBody)}`)


    // Make the API request to update the JNE destination
    const response = await otoapiApiRequest(
      `/jne-destinations/${id}`,
      'PUT',
      event,
      validatedBody
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})