import { otoapiApiRequest } from '~/server/services/otoapi'
import { jneDestinationFilterSchema } from '~/schemas/otoapi/jne-destination'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate query parameters
    const query = getQuery(event)
    const validationResult = jneDestinationFilterSchema.safeParse(query)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: validationResult.error.errors,
      })
    }

    const validatedQuery = validationResult.data

    // Build query string for OtoAPI
    const queryParams = new URLSearchParams()
    Object.entries(validatedQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    })

    logger.info('Query parameters:', queryParams.toString())

    const response = await otoapiApiRequest(
      `/jne-destinations?${queryParams.toString()}`,
      'GET',
      event
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})