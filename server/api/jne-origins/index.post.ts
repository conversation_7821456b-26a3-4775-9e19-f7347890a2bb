import { otoapiApiRequest } from '~/server/services/otoapi'
import { createJneOriginSchema } from '~/schemas/otoapi/jne-origin'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the request body
    const body = await readBody(event)
    const validationResult = createJneOriginSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.errors,
      })
    }

    const validatedBody = validationResult.data

    // Make the API request to create the JNE origin
    const response = await otoapiApiRequest(
      '/jne-origins',
      'POST',
      event,
      validatedBody
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})