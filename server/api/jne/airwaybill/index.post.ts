import { jneApiRequest } from '~/server/services/jne'
import { jneAirwaybillRequestSchema } from '~/schemas/jne'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const validationResult = jneAirwaybillRequestSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.errors,
      })
    }

    const response = await jneApiRequest(
      '/tracing/api/generatecnote',
      validationResult.data
    )

    return response
  } catch (error: any) {
    throw error
  }
})