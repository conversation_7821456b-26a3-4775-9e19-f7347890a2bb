import { jneApiRequest } from '~/server/services/jne'
import { jneTrackingParamsSchema } from '~/schemas/jne'

export default defineEventHandler(async (event) => {
  try {
    const params = event.context.params
    const validationResult = jneTrackingParamsSchema.safeParse(params)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request parameters',
        data: validationResult.error.errors,
      })
    }

    const { awb } = validationResult.data

    const response = await jneApiRequest(
      `/tracing/api/list/v1/cnote/${awb}`
    )

    return response
  } catch (error: any) {
    throw error
  }
})