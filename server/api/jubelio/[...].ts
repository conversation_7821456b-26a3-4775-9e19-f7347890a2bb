import { jubelioApiRequest } from '~/server/services/jubelio'
import { logger } from '~/utils/logger'
import { H3Event } from 'h3'

interface ProxyRequestOptions {
  endpoint: string
  method: string
  event: H3Event
  body?: any
  queryParams?: Record<string, any>
}

export default defineEventHandler(async (event) => {
  try {
    // Extract and validate the request
    const requestOptions = await parseProxyRequest(event)
    
    logger.debug(`Processing Jubelio API proxy request: ${requestOptions.method} ${requestOptions.endpoint}`)
    
    // Forward the request to Jubelio
    const response = await jubelioApiRequest(
      requestOptions.endpoint,
      requestOptions.method as any,
      requestOptions.event,
      requestOptions.body
    )
    
    logger.debug(`Jubelio API proxy request successful: ${requestOptions.endpoint}`)
    return response
    
  } catch (error: any) {
    return handleProxyError(error, event)
  }
})

/**
 * Parse and validate the incoming proxy request
 */
async function parseProxyRequest(event: H3Event): Promise<ProxyRequestOptions> {
  const url = getRequestURL(event)
  const method = event.method
  
  // Extract endpoint from URL path
  let endpoint = url.pathname.replace('/api/jubelio', '')
  
  // Handle root path
  if (!endpoint || endpoint === '/') {
    endpoint = '/'
  }
  
  // Normalize endpoint - ensure it starts with /
  if (!endpoint.startsWith('/')) {
    endpoint = `/${endpoint}`
  }
  
  // Ensure endpoint has a trailing slash if it's a base path (single segment)
  const pathSegments = endpoint.split('/').filter(Boolean)
  if (pathSegments.length === 1 && !endpoint.endsWith('/')) {
    endpoint = `${endpoint}/`
  }
  
  // Handle query parameters for GET requests
  let fullEndpoint = endpoint
  if (method === 'GET') {
    const query = getQuery(event)
    if (Object.keys(query).length > 0) {
      // Filter out undefined/null values and convert to strings
      const cleanQuery: Record<string, string> = {}
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          cleanQuery[key] = String(value)
        }
      })
      
      const queryString = new URLSearchParams(cleanQuery).toString()
      if (queryString) {
        fullEndpoint = `${endpoint}?${queryString}`
      }
    }
  }
  
  // Get request body for non-GET methods
  let body: any = undefined
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    try {
      const rawBody = await readBody(event)
      if (rawBody !== undefined && rawBody !== null) {
        body = rawBody
        
        if (process.env.NODE_ENV === 'development') {
          logger.debug(`Proxy request body: ${JSON.stringify(body)}`)
        }
      }
    } catch (error) {
      logger.warn('Failed to parse request body, continuing with undefined body:', error)
    }
  }
  
  return {
    endpoint: fullEndpoint,
    method,
    event,
    body
  }
}

/**
 * Handle proxy errors with appropriate HTTP responses
 */
function handleProxyError(error: any, event: H3Event) {
  const url = getRequestURL(event)
  const endpoint = url.pathname.replace('/api/jubelio', '')
  
  logger.error(`Jubelio API proxy error for ${endpoint}:`, {
    message: error.message,
    statusCode: error.statusCode,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  })
  
  // Handle specific error types
  switch (error.statusCode) {
    case 400:
      return createError({
        statusCode: 400,
        statusMessage: extractErrorMessage(error, 'Invalid request. Please check your input and try again.')
      })
      
    case 401:
      return createError({
        statusCode: 401,
        statusMessage: 'Authentication error with Jubelio. Please login again.'
      })
      
    case 403:
      return createError({
        statusCode: 403,
        statusMessage: 'Access denied. You do not have permission to access this resource.'
      })
      
    case 404:
      return createError({
        statusCode: 404,
        statusMessage: 'Resource not found in Jubelio API.'
      })
      
    case 422:
      return createError({
        statusCode: 422,
        statusMessage: extractErrorMessage(error, 'Validation error. Please check your data and try again.')
      })
      
    case 429:
      return createError({
        statusCode: 429,
        statusMessage: 'Too many requests. Please wait a moment and try again.'
      })
      
    case 500:
      return createError({
        statusCode: 500,
        statusMessage: 'Internal server error in Jubelio API.'
      })
      
    case 502:
    case 503:
    case 504:
      return createError({
        statusCode: 503,
        statusMessage: 'Jubelio service is temporarily unavailable. Please try again later.'
      })
      
    default:
      // For unknown errors or network issues
      return createError({
        statusCode: error.statusCode || 500,
        statusMessage: error.statusMessage || 'Error communicating with Jubelio API. Please try again later.'
      })
  }
}

/**
 * Extract meaningful error message from error object
 */
function extractErrorMessage(error: any, fallback: string): string {
  // Try to get a meaningful error message from various possible locations
  if (error.data?.message) {
    return error.data.message
  }
  
  if (error.data?.error) {
    return typeof error.data.error === 'string' ? error.data.error : fallback
  }
  
  if (error.data?.errors && Array.isArray(error.data.errors)) {
    return error.data.errors.join(', ')
  }
  
  if (error.statusMessage && error.statusMessage !== 'Unknown') {
    return error.statusMessage
  }
  
  return fallback
}
