import { refreshJubelioToken } from '~/server/services/jubelioToken'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const newToken = await refreshJubelioToken(event)
    
    if (!newToken) {
      return createError({
        statusCode: 500,
        message: 'Failed to refresh Jubelio token'
      })
    }
    
    return {
      status: 'success',
      message: 'Jubelio token refreshed successfully'
    }
  } catch (error) {
    logger.error('Error refreshing Jubelio token:', error)
    
    return createError({
      statusCode: 500,
      message: 'An error occurred while refreshing the Jubelio token'
    })
  }
})
