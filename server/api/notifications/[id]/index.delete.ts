import { logger } from '~/utils/logger'
import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, 'id')
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Notification ID is required'
      })
    }
    
    logger.info(`Deleting notification ${id}`)
    
    const response = await otoapiApiRequest(
      `/notifications/${id}`,
      'DELETE',
      event
    )
    
    return response
  } catch (error: any) {
    logger.error(`Failed to delete notification:`, error)
    throw error
  }
})
