import { logger } from '~/utils/logger'
import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    logger.info('Clearing all notifications')
    
    const response = await otoapiApiRequest(
      '/notifications/clear',
      'DELETE',
      event
    )
    
    return response
  } catch (error: any) {
    logger.error('Failed to clear notifications:', error)
    throw error
  }
})
