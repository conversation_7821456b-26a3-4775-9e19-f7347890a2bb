import { logger } from '~/utils/logger'
import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    logger.info('Getting notifications list')
    
    // Get query parameters for pagination
    const query = getQuery(event)
    const page = query.page || 1
    const per_page = query.per_page || 15
    
    // Build query string
    const queryParams = new URLSearchParams({
      page: page.toString(),
      per_page: per_page.toString()
    })
    
    const response = await otoapiApiRequest(
      `/notifications?${queryParams.toString()}`,
      'GET',
      event
    )
    
    return response
  } catch (error: any) {
    logger.error('Failed to get notifications:', error)
    throw error
  }
})
