import { logger } from '~/utils/logger'
import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    logger.info('Marking all notifications as read')
    
    const response = await otoapiApiRequest(
      '/notifications/mark-all-read',
      'POST',
      event
    )
    
    return response
  } catch (error: any) {
    logger.error('Failed to mark all notifications as read:', error)
    throw error
  }
})
