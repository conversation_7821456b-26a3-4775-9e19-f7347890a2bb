import { logger } from '~/utils/logger'
import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    logger.info('Getting unread notifications count')
    
    const response = await otoapiApiRequest(
      '/notifications/unread-count',
      'GET',
      event
    )
    
    return response
  } catch (error: any) {
    logger.error('Failed to get unread notifications count:', error)
    throw error
  }
})
