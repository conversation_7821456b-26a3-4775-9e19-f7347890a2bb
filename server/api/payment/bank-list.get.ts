import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    const response = await otoapiApiRequest('/payment/banks', 'GET', event)
    
    return {
      success: true,
      data: response.data
    }
  } catch (error: any) {
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Failed to fetch bank list'
    })
  }
})
