import { otoapiApiRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  try {
    const { id } = event.context.params as { id: string }

    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Product discount ID is required',
      })
    }

    // Forward the request to the OtoAPI
    const response = await otoapiApiRequest(
      `/product-discounts/${id}`,
      'DELETE',
      event
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})