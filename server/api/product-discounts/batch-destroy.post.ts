import { otoapiApiRequest } from '~/server/services/otoapi'
import { batchDestroyProductDiscountSchema } from '~/schemas/otoapi/product-discount'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the request body
    const body = await readBody(event)
    const validationResult = batchDestroyProductDiscountSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.errors,
      })
    }

    const validatedBody = validationResult.data
    logger.warn('Batch deleting discounts:', validatedBody)

    // Forward the request to the OtoAPI
    const response = await otoapiApiRequest(
      '/product-discounts/batch-destroy',
      'POST',
      event,
      validatedBody
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})