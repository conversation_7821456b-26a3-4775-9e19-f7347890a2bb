import { otoapiApiRequest } from '~/server/services/otoapi'
import { batchIndexProductDiscountSchema } from '~/schemas/otoapi/product-discount'

export default defineEventHandler(async (event) => {
  try {
    // Get and validate the request body
    const body = await readBody(event)
    const validationResult = batchIndexProductDiscountSchema.safeParse(body)

    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request body',
        data: validationResult.error.errors,
      })
    }

    const validatedBody = validationResult.data

    // Forward the request to the OtoAPI
    const response = await otoapiApiRequest(
      '/product-discounts/batch',
      'POST',
      event,
      { jubelio_item_group_ids: validatedBody.jubelio_item_group_ids }
    )

    return response
  } catch (error: any) {
    // Re-throw the error to be handled by the global error handler
    throw error
  }
})