import { calculateDomesticCost } from '~/server/services/rajaongkir'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const result = await calculateDomesticCost(body)
    return result
  } catch (error: any) {
    logger.error('Error in domestic cost calculation endpoint:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to calculate domestic shipping cost'
    })
  }
})
