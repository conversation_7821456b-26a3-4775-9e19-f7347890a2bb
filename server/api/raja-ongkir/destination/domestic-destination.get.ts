import { getDomesticDestinations } from '~/server/services/rajaongkir'
import { logger } from '~/utils/logger'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const result = await getDomesticDestinations(query as any)
    return result
  } catch (error: any) {
    logger.error('Error in domestic destinations endpoint:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch domestic destinations'
    })
  }
})
