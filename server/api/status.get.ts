import { otoapiPublicRequest } from '~/server/services/otoapi'

export default defineEventHandler(async (event) => {
  // 1. Get the runtime config on the server
  const config = useRuntimeConfig(event)
  
  // 2. Get the specific identifier for the app
  const identifier = config.public.app.headerAppIdentifier
  
  // 3. Construct the correct endpoint with the identifier
  const endpoint = `/status/${identifier}`

  try {
    // 4. Make the public request to the correct endpoint (e.g., /api/status/otoresell-web)
    const statusData = await otoapiPublicRequest(endpoint, 'GET');
    
    // The backend returns a flat object: { "maintenance_status": boolean }
    // We will return it as { "isMaintenance": boolean } for consistent client-side handling.
    return { isMaintenance: statusData.maintenance_status || false };
  } catch (error) {
    console.error(`Failed to fetch maintenance status for identifier: ${identifier}`, error);
    // Fail safely by assuming maintenance is off
    return { isMaintenance: false };
  }
})