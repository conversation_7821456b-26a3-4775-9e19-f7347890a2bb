import { z } from 'zod/v3'
import { otoapiApiRequest } from '~/server/services/otoapi'
import { OrderDetailSchema } from '~/schemas/transaction/order'

// Define validation schema for the ID parameter
const getTransactionSchema = z.object({
  id: z.coerce.number().int().min(1)
})

export default defineEventHandler(async (event) => {
  try {
    // Only allow GET method
    assertMethod(event, 'GET')

    // Get and validate the ID from the route parameters
    const params = await getValidatedRouterParams(event, (params) => {
      return getTransactionSchema.safeParse(params)
    })

    if (!params.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid order ID',
        data: params.error.errors
      })
    }

    const { id } = params.data

    // Make request to OtoAPI for a single order
    const response = await otoapiApiRequest(
      `/orders/${id}`,
      'GET',
      event
    )

    // The external API response has a top-level 'data' property containing the order details.
    // We define a schema to validate this structure.
    const OtoApiDetailResponseSchema = z.object({
      message: z.string(),
      data: OrderDetailSchema,
    })

    const parsedResponse = OtoApiDetailResponseSchema.safeParse(response)

    if (!parsedResponse.success) {
      console.error(`Invalid API response structure for order ID ${id}:`, parsedResponse.error.issues)
      throw createError({
        statusCode: 500,
        statusMessage: 'Received invalid data structure from external API.',
        data: parsedResponse.error.issues
      })
    }

    // Return only the nested 'data' object, as the store expects
    return parsedResponse.data.data
  } catch (error: any) {
    console.error(`Get transaction by ID error (ID: ${event.context.params?.id}):`, error)

    // Handle specific OtoAPI errors
    if (error.statusCode) {
      throw createError({
        statusCode: error.statusCode,
        statusMessage: error.data?.message || error.statusMessage || 'Failed to fetch transaction'
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'An internal server error occurred'
    })
  }
})