import { z } from 'zod/v3'
import { otoapiApiRequest } from '~/server/services/otoapi'
import { OrderResponseSchema } from '~/schemas/transaction/order'

// Define validation schema for query parameters
const getTransactionsSchema = z.object({
  status: z.string().optional(),
  jubelio_contact_id: z.coerce.number().int().min(1).optional(),
  jubelio_order_id: z.coerce.number().int().min(1).optional(),
  user_id: z.coerce.number().int().min(1).optional(),
  search: z.string().max(255).optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'jubelio_order_id', 'status', 'grand_total']).optional(),
  sort_direction: z.enum(['asc', 'desc']).optional(),
  per_page: z.coerce.number().int().min(1).max(100).default(10),
  page: z.coerce.number().int().min(1).default(1),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  min_total: z.coerce.number().min(0).optional(),
  max_total: z.coerce.number().min(0).optional()
}).refine((data) => {
  // Validate date_to is after or equal to date_from
  if (data.date_from && data.date_to) {
    return new Date(data.date_to) >= new Date(data.date_from)
  }
  return true
}, {
  message: "date_to must be after or equal to date_from",
  path: ["date_to"]
}).refine((data) => {
  // Validate max_total is greater than or equal to min_total
  if (data.min_total !== undefined && data.max_total !== undefined) {
    return data.max_total >= data.min_total
  }
  return true
}, {
  message: "max_total must be greater than or equal to min_total",
  path: ["max_total"]
})

export default defineEventHandler(async (event) => {
  try {
    // Only allow GET method
    assertMethod(event, 'GET')
    
    // Get query parameters
    const query = getQuery(event)
    
    // Validate query parameters
    const validationResult = getTransactionsSchema.safeParse(query)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid query parameters',
        data: validationResult.error.errors
      })
    }
    
    const validatedQuery = validationResult.data
    
    // Build query string for OtoAPI
    const queryParams = new URLSearchParams()
    
    // Add all valid parameters to query string
    Object.entries(validatedQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      `/orders?${queryParams.toString()}`,
      'GET',
      event
    )
    
    // The external OtoAPI response doesn't have a `success` field.
    // We'll create a schema for the external response by omitting `success`.
    const OtoApiResponseSchema = OrderResponseSchema.omit({ success: true })

    // Validate the structure of the response from the external API
    const parsedResponse = OtoApiResponseSchema.safeParse(response)

    if (!parsedResponse.success) {
      console.error('Invalid API response structure from OtoAPI:', parsedResponse.error.issues)
      throw createError({
        statusCode: 500,
        statusMessage: 'Received invalid data structure from external API.',
        data: parsedResponse.error.issues
      })
    }
    
    // Now, construct the final response for our frontend, adding the `success` field.
    return {
      success: true,
      data: parsedResponse.data.data,
      meta: parsedResponse.data.meta || null,
      links: parsedResponse.data.links || null
    }
    
  } catch (error: any) {
    console.error('Get transactions error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid request parameters'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized access'
      })
    }
    
    if (error.statusCode === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access forbidden'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Validation failed',
        data: error.data?.errors || {}
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch transactions'
    })
  }
})
