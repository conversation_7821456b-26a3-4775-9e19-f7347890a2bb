import { otoapiApiRequest } from '~/server/services/otoapi'
import { jubelioApiRequest } from '~/server/services/jubelio'
import { logger } from '~/utils/logger'
import type { CreateOrderRequest } from '~/types/checkout'
import { CreateOtoApiOrderSchema, type CreateOtoApiOrderRequest } from '~/schemas/transaction/create'

interface CreateOrderPayload {
  jubelio_order: CreateOrderRequest
  otoapi_order: CreateOtoApiOrderRequest
}

export default defineEventHandler(async (event) => {
  let jubelioOrderId: number | null = null
  let jubelioResponse: any = null
  let body: CreateOrderPayload | null = null

  try {
    body = await readBody<CreateOrderPayload>(event)

    // Validate the otoapi_order part of the payload
    const validatedOtoApiOrder = CreateOtoApiOrderSchema.parse(body.otoapi_order)

    // Step 1: Create order in Jubelio first to get jubelio_order_id
    logger.info('Creating order in Jubelio...')
    jubelioResponse = await jubelioApiRequest(
      '/sales/orders/',
      'POST',
      event,
      body.jubelio_order
    )

    if (!jubelioResponse?.id) {
      throw new Error('Failed to create order in Jubelio - no ID returned')
    }

    jubelioOrderId = jubelioResponse.id
    logger.info(`Jubelio order created with ID: ${jubelioOrderId}`)

    // Step 2: Create order in OtoAPI with the Jubelio jubelio_order_id
    const otoapiOrderData = {
      ...validatedOtoApiOrder,
      jubelio_order_id: jubelioOrderId // Use Jubelio order ID
    }
    
    logger.info('Creating order in OtoAPI...')
    const otoapiResponse = await otoapiApiRequest(
      '/orders', 
      'POST', 
      event, 
      otoapiOrderData
    )
    
    logger.info('Order created successfully in both systems')
    
    return {
      success: true,
      data: {
        jubelio_order: jubelioResponse,
        otoapi_order: otoapiResponse,
        jubelio_order_id: jubelioOrderId
      }
    }
    
  } catch (error: any) {
    logger.error('Error creating order:', error)
    
    // If Jubelio succeeded but OtoAPI failed, try to handle the failure
    if (jubelioOrderId && jubelioResponse && body) {
      logger.info(`Attempting to handle OtoAPI failure for Jubelio order ${jubelioOrderId}`)

      try {
        // Option 1: Try OtoAPI one more time
        logger.info('Retrying OtoAPI order creation...')
        const retryOtoapiOrderData = {
          ...body.otoapi_order,
          jubelio_order_id: jubelioOrderId
        }
        
        const retryOtoapiResponse = await otoapiApiRequest(
          '/orders', 
          'POST', 
          event, 
          retryOtoapiOrderData
        )
        
        logger.info('OtoAPI retry successful')
        return {
          success: true,
          data: {
            jubelio_order: jubelioResponse,
            otoapi_order: retryOtoapiResponse,
            jubelio_order_id: jubelioOrderId
          },
          warning: 'Order created after retry'
        }
        
      } catch (retryError: any) {
        logger.error('OtoAPI retry failed, canceling Jubelio order:', retryError)
        
        // Option 2: Cancel the Jubelio order
        try {
          // First, get the existing order details
          logger.info(`Fetching Jubelio order details for ID: ${jubelioOrderId}`)
          const existingOrder = await jubelioApiRequest(
            `/sales/orders/${jubelioOrderId}`,
            'GET',
            event
          )

          if (!existingOrder) {
            throw new Error('Could not fetch existing order for cancellation')
          }

          // Prepare cancellation data using existing order structure
          const cancelOrderData = {
            salesorder_id: existingOrder.salesorder_id,
            salesorder_no: existingOrder.salesorder_no,
            contact_id: existingOrder.contact_id,
            customer_name: existingOrder.customer_name,
            transaction_date: existingOrder.transaction_date,
            location_id: existingOrder.location_id,
            source: existingOrder.source,
            is_canceled: true,
            cancel_reason: 'System Error',
            cancel_reason_detail: 'Failed to sync with internal system',
            items: (existingOrder.items || []).map((item: any) => ({
              salesorder_detail_id: item.salesorder_detail_id,
              item_id: item.item_id,
              tax_id: item.tax_id,
              price: parseFloat(item.price) || 0,
              unit: item.unit,
              qty_in_base: parseFloat(item.qty_in_base) || 0,
              disc: parseFloat(item.disc) || 0,
              disc_amount: parseFloat(item.disc_amount) || 0,
              tax_amount: parseFloat(item.tax_amount) || 0,
              amount: parseFloat(item.amount) || 0,
              location_id: item.loc_id,
              serial_no: item.serial_no,
              description: item.description,
              shipper: existingOrder.shipper,
              channel_order_detail_id: item.channel_order_detail_id,
              tracking_no: '',
            })),
          }

          // Cancel the order using POST (edit operation)
          await jubelioApiRequest(
            '/sales/orders/',
            'POST',
            event,
            cancelOrderData
          )

          logger.info(`Jubelio order ${jubelioOrderId} has been canceled due to OtoAPI failure`)
 
           // Return a user-friendly error response. The logs contain the technical details.
           throw createError({
             statusCode: 400, // Use a client error code like 400 or 422
             statusMessage: 'Terjadi kesalahan saat membuat pesanan. Pesanan Anda telah dibatalkan secara otomatis. Silakan coba lagi.',
             data: {
               canceled: true,
               jubelio_order_id: jubelioOrderId
             }
           })
 
         } catch (cancelError: any) {
          logger.error('Failed to cancel Jubelio order:', cancelError)

          throw createError({
            statusCode: 500,
            statusMessage: 'Terjadi kesalahan saat membuat pesanan. Pesanan Anda telah dibatalkan secara otomatis. Silakan coba lagi.',
            data: {
              jubelio_order_id: jubelioOrderId,
              jubelio_order: jubelioResponse,
              canceled: false,
              original_error: error.message,
              cancel_error: cancelError.message
            }
          })
        }
      }
    }
    
    // If Jubelio failed or no rollback needed
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: 'Gagal membuat pesanan. Silakan periksa kembali detail pesanan Anda dan coba lagi.'
    })
  }
})
