import { otoapiApiRequest } from '~/server/services/otoapi'
import { changePasswordSchema } from '~/schemas/otoapi/user'

export default defineEventHandler(async (event) => {
  try {
    // Only allow POST method
    assertMethod(event, 'POST')
    
    const body = await readBody(event)
    
    // Validate request body
    const validationResult = changePasswordSchema.safeParse(body)
    
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Validation failed',
        data: validationResult.error.errors
      })
    }
    
    const { current_password, new_password, new_password_confirmation } = validationResult.data
    
    // Make request to OtoAPI
    const response = await otoapiApiRequest(
      '/change-password',
      'POST',
      event,
      {
        current_password,
        new_password,
        new_password_confirmation
      }
    )
    
    return {
      success: true,
      message: 'Password changed successfully'
    }
    
  } catch (error: any) {
    console.error('Change password error:', error)
    
    // Handle specific OtoAPI errors
    if (error.statusCode === 400) {
      throw createError({
        statusCode: 400,
        statusMessage: error.data?.message || 'Invalid password data'
      })
    }
    
    if (error.statusCode === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Current password is incorrect'
      })
    }
    
    if (error.statusCode === 422) {
      throw createError({
        statusCode: 422,
        statusMessage: 'Password validation failed',
        data: error.data?.errors || {}
      })
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to change password'
    })
  }
})
