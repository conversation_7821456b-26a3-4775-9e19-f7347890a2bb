import { otoapiApiRequest } from '~/server/services/otoapi'
import { userSchema } from '~/schemas/otoapi/user'
import { createSingleItemResponseSchema } from '~/schemas/api-response'

export default defineEventHandler(async (event) => {
  try {
    // 1. Get profile from OtoAPI (primary source)
    const rawProfile = await otoapiApiRequest('/profile', 'GET', event)

    // 2. Create a schema for the expected single item response
    const profileResponseSchema = createSingleItemResponseSchema(userSchema)

    // 3. Validate the raw response from the API
    const validationResult = profileResponseSchema.safeParse(rawProfile)

    // 4. Handle validation failure
    if (!validationResult.success) {
      console.error('API profile response validation failed:', validationResult.error)
      // Depending on requirements, you could throw an error or return a default/error state
      throw createError({
        statusCode: 500,
        statusMessage: 'Invalid profile data received from the server.',
      })
    }

    // 5. Return the successfully validated profile data
    return validationResult.data

  } catch (error: any) {
    console.error('Get profile error:', error)

    // Re-throw known errors (like 401, 404) from otoapiApiRequest
    if (error.statusCode) {
      throw error
    }

    // Throw a generic server error for any other issues
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user profile',
    })
  }
})
