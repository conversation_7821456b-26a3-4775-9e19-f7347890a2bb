import { otoapiApiRequest } from '~/server/services/otoapi'
import { updateProfileSchema, userSchema } from '~/schemas/otoapi/user'
import { createSingleItemResponseSchema } from '~/schemas/api-response'
import { createUserSession } from '~/server/services/session'
import { getSecureCookie } from '~/server/utils/cookies'
import type { BackendResponse } from '~/server/services/session'

export default defineEventHandler(async (event) => {
  try {
    // 1. Read and parse the request body
    const body = await readBody(event)
    const validationResult = updateProfileSchema.safeParse(body)

    // 2. Validate the request body
    if (!validationResult.success) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Validation failed',
        data: validationResult.error.errors,
      })
    }

    const { name, phone } = validationResult.data

    // 3. Make the API request to the external service to update the profile
    const otoapiResult = await otoapiApiRequest('/profile', 'PUT', event, {
      name,
      phone,
    })

    // 4. Validate the response from the external service
    // 4. Validate the response from the external service
    const singleItemSchema = createSingleItemResponseSchema(userSchema)
    const validatedResponse = singleItemSchema.safeParse(otoapiResult)

    if (!validatedResponse.success) {
      console.error(
        'API response validation failed after profile update:',
        validatedResponse.error,
      )
      // Even if validation fails, we can't proceed to create a session.
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to validate profile update response from backend.',
      })
    }

    // 5. We have fresh user data. Now we re-create the session to issue a new JWT.
    const otoapiToken = getSecureCookie(event, 'reseller_otoapi_token')
    const jubelioToken = getSecureCookie(event, 'reseller_jubelio_token')

    if (!otoapiToken || !jubelioToken) {
      console.error('Missing API or Jubelio token during profile update session refresh')
      throw createError({
        statusCode: 401,
        statusMessage: 'Authentication tokens are missing.',
      })
    }

    // Adapt the profile response to what createUserSession expects.
    const backendResponse: BackendResponse = {
      token: otoapiToken,
      token_type: 'Bearer',
      jubelio_token: jubelioToken,
      user: validatedResponse.data.data, // The user object is nested in `data`
    }

    // 6. Create a new session. This sets new auth cookies and a new CSRF token.
    const newSession = await createUserSession(event, backendResponse)

    // 7. Return the complete new session data to the frontend.
    return newSession
  }
  catch (error: any) {
    console.error('Profile update error:', error)

    // Re-throw known errors (like from otoapiApiRequest)
    if (error.statusCode) {
      throw error
    }

    // Throw a generic server error for unknown issues
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during profile update',
    })
  }
})
