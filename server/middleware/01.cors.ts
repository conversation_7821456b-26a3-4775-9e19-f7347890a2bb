import { logger } from '~/utils/logger'

export default defineEventHandler((event) => {
  const config = useRuntimeConfig()
  const url = getRequestURL(event)
  const method = event.node.req.method || 'GET'

  // logger.debug(`Handling CORS for ${method} ${url.pathname}`)
  
  // Only apply CORS headers to API routes
  if (!url.pathname.startsWith('/api/')) {
    return
  }

  // Get request origin
  const origin = getRequestHeader(event, 'Origin')
  const referer = getRequestHeader(event, 'Referer')
  
  // logger.debug(`Origin: ${origin}, Referer: ${referer}`)

  // If no Origin header, this is likely a same-origin request
  if (!origin) {
    // For same-origin requests, we can optionally validate the referer
    // or just allow them through (they're from your own domain)
    logger.debug('No Origin header - likely same-origin request')
    return
  }

  // We have an Origin header - this is a cross-origin request
  // Apply CORS policy
  
  // Set credentials header (if you need cookies/auth)
  setResponseHeader(event, 'Access-Control-Allow-Credentials', 'true')

  if (process.env.NODE_ENV === 'production') {
    const allowedOrigins = (config.cors?.allowedOrigins || '').split(',').filter(Boolean)
    
    // logger.debug(`Allowed origins: ${allowedOrigins.join(', ')}`)
    
    if (allowedOrigins.length > 0) {
      // Check if origin is in allowed list
      if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        setResponseHeader(event, 'Access-Control-Allow-Origin', origin)
        logger.info(`CORS: Allowed cross-origin request from ${origin}`)
      } else {
        // Origin not allowed
        logger.warn(`CORS: Blocked cross-origin request from ${origin}`)
        
        if (method === 'OPTIONS') {
          event.node.res.statusCode = 403
          event.node.res.end()
          return
        }
        
        // Don't set CORS headers - browser will block the response
        return
      }
    } else {
      // No allowed origins configured - block all cross-origin requests
      logger.warn('CORS: No allowed origins configured. Blocking cross-origin request.')
      
      if (method === 'OPTIONS') {
        event.node.res.statusCode = 403
        event.node.res.end()
        return
      }
      
      return
    }
  } else {
    // Development: allow all cross-origin requests
    setResponseHeader(event, 'Access-Control-Allow-Origin', origin)
    // logger.debug(`CORS: Development - allowing ${origin}`)
  }

  // Handle preflight requests
  if (method === 'OPTIONS') {
    setResponseHeader(event, 'Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,PATCH')
    setResponseHeader(event, 'Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRF-Token')
    
    event.node.res.statusCode = 204
    event.node.res.end()
    return
  }
})
