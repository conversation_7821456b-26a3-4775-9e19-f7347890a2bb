import { verifyToken, extractUserData } from '../utils/jwt'
import { H3Event } from 'h3'
import { getSecureCookie } from '../utils/cookies'
import { logger } from '~/utils/logger'
import { validateCsrfToken } from '../utils/csrf'
import { getJubelioToken } from '~/server/services/jubelioToken'

// Define public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/auth/login',
  '/api/auth/logout',
  '/api/auth/check',
  '/api/auth/refresh',
  '/api/status',
  '/api/auth/register',
  '/api/auth/resend-verification',
  '/api/auth/verify-email',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/auth/support-contacts'
]

/**
 * Get authenticated user from request
 * Supports both header-based and cookie-based authentication
 */
export const getAuthUser = async (event: H3Event) => {
  const url = getRequestURL(event)
  const method = event.node.req.method || 'GET'
  const origin = getRequestHeader(event, 'Origin') || ''
  const referer = getRequestHeader(event, 'Referer') || ''
  
  // Get CSRF token for state-changing requests
  const csrfToken = getRequestHeader(event, 'X-CSRF-Token')
  
  let token: string | undefined = undefined
  let authSource = 'none'
  
  // First try to get token from Authorization header
  const authHeader = getRequestHeader(event, 'Authorization')
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7)
    authSource = 'bearer'
    logger.debug(`Authentication via Bearer token for ${url.pathname}`)
  } else {
    // Then try cookie-based auth
    token = getSecureCookie(event, 'reseller_auth_token')
    
    if (token) {
      authSource = 'cookie'
      logger.debug(`Authentication via cookie for ${url.pathname}`)
      
      // For cookie-based auth, verify CSRF token for state-changing methods
      if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
        // Skip CSRF validation for login route
        if (url.pathname !== '/api/auth/login') {
          if (!csrfToken || !validateCsrfToken(event, csrfToken)) {
            logger.warn(`CSRF token validation failed for ${url.pathname}`)
            return null
          }
        }
      }
    }
  }
  
  if (!token) {
    logger.debug(`No authentication token found for ${url.pathname}`)
    return null
  }
  
  try {
    // Extract user data from token
    const userData = await extractUserData(token)
    
    if (!userData) {
      logger.warn(`Invalid token for ${url.pathname}`)
      return null
    }
    
    // Get Jubelio token separately
    const jubelioToken = await getJubelioToken(event)
    
    if (!jubelioToken) {
      logger.warn(`Failed to get Jubelio token for ${url.pathname}`)
      return null
    }
    
    return {
      user: userData,
      jubelioToken,
      authSource
    }
  } catch (error) {
    logger.error(`Token validation error: ${error}`)
    return null
  }
}

/**
 * Server middleware to protect API routes
 * Handles authentication and CORS validation
 */
export default defineEventHandler(async (event) => {
  const url = getRequestURL(event)
  const method = event.node.req.method || 'GET'
  
  // Skip auth for non-API routes
  if (!url.pathname.startsWith('/api/')) {
    return
  }
  
  // Skip auth for public API routes
  if (
    PUBLIC_ROUTES.includes(url.pathname) ||
    url.pathname.startsWith('/api/public/')
  ) {
    // For public routes, we still need to validate CSRF for state-changing methods
    // except for login which generates the initial CSRF token
    const csrfExemptedRoutes = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/resend-verification',
      '/api/auth/verify-email',
      '/api/auth/forgot-password',
      '/api/auth/reset-password',
      '/api/auth/support-contacts'
    ]

    if (
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method) &&
      !csrfExemptedRoutes.includes(url.pathname)
    ) {
      const csrfToken = getRequestHeader(event, 'X-CSRF-Token')
      if (!csrfToken || !validateCsrfToken(event, csrfToken)) {
        logger.warn(`CSRF token validation failed for public route ${url.pathname}`)
        return createError({
          statusCode: 403,
          message: 'Invalid CSRF token'
        })
      }
    }
    return
  }
  
  // Check origin for API requests
  const origin = getRequestHeader(event, 'Origin')
  const config = useRuntimeConfig()
  
  // If we're in production and have an origin header, validate it
  if (process.env.NODE_ENV === 'production' && origin) {
    const allowedOrigins = (config.cors?.allowedOrigins || '').split(',').filter(Boolean)
    
    // If we have configured allowed origins and this origin isn't in the list
    if (
      allowedOrigins.length > 0 && 
      !allowedOrigins.includes(origin) && 
      !allowedOrigins.includes('*')
    ) {
      logger.warn(`Blocked request from unauthorized origin: ${origin}`)
      return createError({
        statusCode: 403,
        message: 'Forbidden: Cross-origin request not allowed'
      })
    }
  }
  
  // Get authenticated user
  const auth = await getAuthUser(event)
  
  if (!auth) {
    logger.warn(`Unauthorized access attempt to ${url.pathname}`)
    return createError({
      statusCode: 401,
      message: 'Unauthorized'
    })
  }
  
  // Add user and jubelioToken to event context
  event.context.auth = auth
})
