import { logger } from '~/utils/logger'

/**
 * Helper function to make authenticated requests to JNE API
 * @param endpoint - API endpoint path
 * @param body - Request body
 * @returns Promise with the API response
 */
export const jneApiRequest = async (
  endpoint: string,
  body?: Record<string, any>
): Promise<any> => {
  const config = useRuntimeConfig()
  
  const fullUrl = `${config.jne.baseurl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
  
  logger.debug(`Making POST request to JNE API: ${fullUrl}`)
  
  const requestBody = new URLSearchParams({
    username: config.jne.username,
    api_key: config.jne.apiKey,
    ...body
  })

  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: requestBody,
    })

    if (!response.ok) {
      await handleJneError(response, fullUrl)
    }

    const data = await response.json()
    
    if (data.error) {
        throw createError({
            statusCode: 400,
            statusMessage: data.error,
        });
    }

    logger.debug(`JNE API request to ${endpoint} successful`)
    return data
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }
    
    logger.error(`Network error in JNE API request to ${endpoint}:`, {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
    
    throw createError({
      statusCode: 503,
      statusMessage: 'JNE service unavailable'
    })
  }
}

/**
 * Handle JNE API error responses
 */
const handleJneError = async (
  response: Response,
  fullUrl: string
): Promise<any> => {
  let errorMessage = 'Error communicating with JNE API'
  let errorDetails = null
  
  try {
    const errorData = await response.json()
    logger.error(`JNE API request to ${fullUrl} failed:`, {
      status: response.status,
      statusText: response.statusText,
      error: errorData
    })
    
    if (errorData.error) {
      errorMessage = errorData.error
    }
    
    errorDetails = errorData
  } catch (parseError) {
    logger.error(`Error parsing JNE error response:`, parseError)
    errorMessage = response.statusText || `HTTP ${response.status} Error`
  }
  
  throw createError({
    statusCode: response.status,
    statusMessage: errorMessage,
    data: errorDetails
  })
}