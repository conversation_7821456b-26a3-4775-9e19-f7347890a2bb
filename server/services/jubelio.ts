import { logger } from '~/utils/logger'
import { getJ<PERSON><PERSON>Token, refreshJubelioToken, isTokenExpiredError } from './jubelioToken'
import { H3Event } from 'h3'

/**
 * Helper function to make authenticated requests to Jubelio API
 * @param endpoint - API endpoint path
 * @param method - HTTP method
 * @param event - H3Event for cookie access
 * @param body - Request body (for POST, PUT, PATCH, DELETE)
 * @param additionalHeaders - Additional headers to include
 * @param directToken - Optional direct token to use instead of fetching from cookie
 * @returns Promise with the API response
 */
export const jubelioApiRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  event: H3Event,
  body?: any,
  additionalHeaders?: Record<string, string>,
  directToken?: string // Add optional direct token parameter
): Promise<any> => {
  const config = useRuntimeConfig()
  
  // Normalize endpoint - ensure it starts with /
  let normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  
  // Ensure endpoint has a trailing slash if it's a base path (single segment)
  const pathSegments = normalizedEndpoint.split('/').filter(Boolean)
  if (pathSegments.length === 1 && !normalizedEndpoint.endsWith('/')) {
    normalizedEndpoint = `${normalizedEndpoint}/`
  }
  
  const fullUrl = `${config.jubelio.baseurl}${normalizedEndpoint}`
  
  logger.debug(`Making ${method} request to Jubelio API: ${fullUrl}`)
  
  // Get authentication token - use direct token if provided, otherwise get from cookie
  let token = directToken
  if (!token) {
    token = await getJubelioToken(event) as string
    if (!token) {
      logger.error('No Jubelio token available for request')
      throw createError({
        statusCode: 401,
        statusMessage: 'Jubelio authentication token not found'
      })
    }
  }
  
  return await makeJubelioRequest(fullUrl, method, token, body, additionalHeaders, event, normalizedEndpoint)
}

/**
 * Internal function to make the actual HTTP request
 */
const makeJubelioRequest = async (
  fullUrl: string,
  method: string,
  token: string,
  body?: any,
  additionalHeaders?: Record<string, string>,
  event?: H3Event,
  endpoint?: string,
  isRetry: boolean = false
): Promise<any> => {
  try {
    // Prepare headers
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${token}`,
      ...additionalHeaders
    }
    
    // Add Content-Type for requests with body
    if (body !== undefined && !headers['Content-Type']) {
      headers['Content-Type'] = 'application/json'
    }
    
    if (body && process.env.NODE_ENV === 'development') {
      logger.debug(`Request body: ${JSON.stringify(body)}`)
    }
    
    // Make the request
    const response = await fetch(fullUrl, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined
    })
    
    // Handle error responses
    if (!response.ok) {
      await handleJubelioError(response, fullUrl, method, token, body, additionalHeaders, event, endpoint, isRetry)
    }
    
    // Handle empty responses
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      logger.debug(`Jubelio API request to ${endpoint} successful (no JSON response)`)
      return { success: true }
    }
    
    // Parse and return the response
    const data = await response.json()
    logger.debug(`Jubelio API request to ${endpoint} successful`)
    return data
    
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }
    
    logger.error(`Network error in Jubelio API request to ${endpoint}:`, {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
    
    throw createError({
      statusCode: 503,
      statusMessage: 'Jubelio service unavailable'
    })
  }
}

/**
 * Handle Jubelio API error responses
 */
const handleJubelioError = async (
  response: Response,
  fullUrl: string,
  method: string,
  token: string,
  body?: any,
  additionalHeaders?: Record<string, string>,
  event?: H3Event,
  endpoint?: string,
  isRetry: boolean = false
): Promise<any> => {
  let errorMessage = 'Error communicating with Jubelio API'
  let errorDetails = null
  
  try {
    const errorData = await response.json()
    logger.error(`Jubelio API request to ${endpoint} failed:`, {
      status: response.status,
      statusText: response.statusText,
      error: errorData
    })
    
    // Extract meaningful error information
    if (errorData.message) {
      errorMessage = errorData.message
    } else if (errorData.error) {
      errorMessage = errorData.error
    }
    
    errorDetails = errorData
  } catch (parseError) {
    // If error response is not JSON, use status text
    logger.error(`Error parsing Jubelio error response:`, parseError)
    errorMessage = response.statusText || `HTTP ${response.status} Error`
  }
  
  // Handle token expiration and retry logic
  if (response.status === 401 && !isRetry && event && endpoint) {
    logger.info('Token appears to be expired, attempting to refresh and retry request')
    
    try {
      const newToken = await refreshJubelioToken(event)
      if (newToken) {
        logger.info('Retrying request with refreshed token')
        return await makeJubelioRequest(fullUrl, method, newToken, body, additionalHeaders, event, endpoint, true)
      }
    } catch (refreshError) {
      logger.error('Failed to refresh Jubelio token:', refreshError)
    }
  }
  
  // Create appropriate error
  throw createError({
    statusCode: response.status,
    statusMessage: errorMessage,
    data: errorDetails
  })
}

/**
 * Get Jubelio customer by email
 */
export const getJubelioCustomerByEmail = async (
  email: string,
  event: H3Event,
  additionalFilters?: Record<string, string>,
  jubelioToken?: string // Add optional token parameter
): Promise<any> => {
  const queryParams = new URLSearchParams({
    page: '1',
    pageSize: '10',
    q: email,
    ...additionalFilters
  }).toString()
  
  return jubelioApiRequest(`/contacts/customers/?${queryParams}`, 'GET', event, undefined, undefined, jubelioToken)
}

/**
 * Verify if user is a reseller in Jubelio
 */
export const verifyJubelioReseller = async (
  email: string,
  phone: string,
  event: H3Event,
  jubelioToken?: string // Add optional token parameter
): Promise<any> => {
  const response = await getJubelioCustomerByEmail(email, event, undefined, jubelioToken)
  
  if (!response.data || !Array.isArray(response.data)) {
    throw createError({
      statusCode: 502,
      statusMessage: 'Invalid response from Jubelio API'
    })
  }
  
  // Find matching user with exact email and phone match
  const customer = response.data.find((contact: any) => 
    contact.email?.toLowerCase() === email.toLowerCase() &&
    contact.phone === phone &&
    contact.is_reseller === true
  )
  
  if (!customer) {
    throw createError({
      statusCode: 403,
      statusMessage: 'User not authorized as a reseller'
    })
  }
  
  return customer
}

/**
 * Specialized function for Jubelio customer/contact requests
 */
// export async function jubelioCustomerRequest(
//   endpoint: string,
//   method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
//   event: H3Event,
//   body?: any
// ): Promise<any> {
//   const customerEndpoint = endpoint.startsWith('/contacts/customers') 
//     ? endpoint 
//     : `/contacts/customers${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
  
//   return jubelioApiRequest(customerEndpoint, method, event, body)
// }