import { logger } from '~/utils/logger'
import { setSecureCookie, getSecureCookie, clearCookie } from '~/server/utils/cookies'
import { otoapiApiRequest } from '~/server/services/otoapi'
import { H3Event } from 'h3'

// <PERSON><PERSON> name for storing the Jubelio token
const JUBELIO_TOKEN_COOKIE = 'reseller_jubelio_token'

export const setjubelioToken = async (event: H3Event, token: string) => {
  setSecureCookie(event, JUBELIO_TOKEN_COOKIE, token, {
    maxAge: 11 * 60 * 60, // 11 hours
  })
}

/** 
 * Get the Jubelio token from cookies or fetch a new one if needed 
 */
export const getJubelioToken = async (event: H3Event): Promise<string | null> => {
  // Try to get token from cookie first
  const token = getSecureCookie(event, JUBELIO_TOKEN_COOKIE)
    
  if (token) {
    logger.debug('Using existing Jubelio token from cookie')
    return token
  }
    
  // If no token in cookie, fetch a new one
  logger.info('No Jubelio token found in cookie, fetching new token')
  return refreshJubelioToken(event)
}

/** 
 * Fetch a new Jubelio token from OtoAPI and store it in a cookie 
 */
export const refreshJubelioToken = async (event: H3Event): Promise<string | null> => {
  try {
    logger.info('Fetching new Jubelio token from OtoAPI')
    
    const response = await otoapiApiRequest(
      '/jubelio-refresh-token',
      'GET',
      event
    )
    
    if (!response.jubelio_token) {
      logger.error('OtoAPI response did not contain a jubelio_token')
      return null
    }
    
    // Store token in cookie
    // Set expiry to slightly less than 12 hours to ensure we refresh before it expires
    setSecureCookie(event, JUBELIO_TOKEN_COOKIE, response.jubelio_token, {
      maxAge: 11 * 60 * 60, // 11 hours
    })
    
    logger.info('Successfully fetched and stored new Jubelio token from OtoAPI')
    return response.jubelio_token
    
  } catch (error: any) {
    logger.error('Error fetching Jubelio token from OtoAPI:', {
      message: error.message,
      statusCode: error.statusCode
    })
    
    // If it's an authentication error, the user might need to login again
    if (error.statusCode === 401) {
      logger.warn('OtoAPI authentication failed while refreshing Jubelio token - user may need to login again')
    }
    
    return null
  }
}

/** 
 * Clear the Jubelio token cookie 
 */
export const clearJubelioToken = (event: H3Event): void => {
  clearCookie(event, JUBELIO_TOKEN_COOKIE)
  logger.info('Cleared Jubelio token cookie')
}

/** 
 * Check if a token error is due to an expired token 
 */
export const isTokenExpiredError = (error: any): boolean => {
  return (
    error.statusCode === 401 || 
    (error.message && (
      error.message.includes('401') || 
      error.message.toLowerCase().includes('unauthorized') ||
      error.message.toLowerCase().includes('expired')
    ))
  )
}
