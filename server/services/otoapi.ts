import { logger } from '~/utils/logger'
import { setSecureCookie, getSecureCookie, clearCookie } from '~/server/utils/cookies'
import { H3Event } from 'h3'

const OTOAPI_TOKEN_COOKIE = 'reseller_otoapi_token'

export const setOtoapiToken = async (event: H3Event, token: string) => {
  setSecureCookie(event, OTOAPI_TOKEN_COOKIE, token, {
    maxAge: 20 * 60 * 60, // 20 hours
  })
}

export const getOtoapiToken = async (event: H3Event): Promise<string | null> => {
  // Try to get token from cookie first
  const token = getSecureCookie(event, OTOAPI_TOKEN_COOKIE)
    
  if (!token) {
    logger.debug('No otoapi token found in cookie')
    return null
  }
    
  return token
}

export const clearOtoapiToken = (event: H3Event): void => {
  clearCookie(event, OTOAPI_TOKEN_COOKIE)
  logger.info('Cleared OtoAPI token cookie')
}

/**
 * Make authenticated requests to OtoAPI
 */
export const otoapiApiRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  event: H3Event,
  body?: any,
  additionalHeaders?: Record<string, string>
): Promise<any> => {
  const config = useRuntimeConfig()
  const token = await getOtoapiToken(event)
  
  if (!token) {
    logger.error('No OtoAPI token available for request')
    throw createError({
      statusCode: 401,
      statusMessage: 'Authentication token not found'
    })
  }

  const url = `${config.otoapi.baseurl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'X-App-Identifier': config.public.app.headerAppIdentifier,
    ...additionalHeaders
  }

  const requestOptions: RequestInit = {
    method,
    headers
  }

  // Include body for POST, PUT, PATCH, and DELETE methods
  if (body && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    requestOptions.body = JSON.stringify(body)
  }

  try {
    logger.debug(`Making OtoAPI request: ${method} ${url}`)
    
    const response = await fetch(url, requestOptions)
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error')
      let errorData
      
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { message: errorText }
      }

      logger.error('OtoAPI request failed:', {
        url,
        method,
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        errors: errorData.errors
      })

      // Handle token expiration
      if (response.status === 401) {
        clearOtoapiToken(event)
        throw createError({
          statusCode: 401,
          statusMessage: 'Authentication token expired'
        })
      }

      throw createError({
        statusCode: response.status,
        statusMessage: errorData.message || `OtoAPI request failed: ${response.statusText}`,
        data: errorData
      })
    }

    // Handle empty responses
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      return { success: true }
    }

    const responseData = await response.json()
    logger.debug(`OtoAPI request successful: ${method} ${url}`)
    
    return responseData
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }
    logger.error('OtoAPI request error:', {
      url,
      method,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
    throw createError({
      statusCode: 503,
      statusMessage: 'OtoAPI service unavailable'
    })
  }
}

/**
 * Make authenticated requests to OtoAPI with a provided token
 */
export const otoapiApiRequestWithToken = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  token: string,
  body?: any,
  additionalHeaders?: Record<string, string>
): Promise<any> => {
  const config = useRuntimeConfig()

  if (!token) {
    logger.error('No OtoAPI token provided for request')
    throw createError({
      statusCode: 401,
      statusMessage: 'Authentication token not provided'
    })
  }

  const url = `${config.otoapi.baseurl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'X-App-Identifier': config.public.app.headerAppIdentifier,
    ...additionalHeaders
  }

  const requestOptions: RequestInit = {
    method,
    headers
  }

  // Include body for POST, PUT, PATCH, and DELETE methods
  if (body && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    requestOptions.body = JSON.stringify(body)
  }

  try {
    logger.debug(`Making OtoAPI request with token: ${method} ${url}`)

    const response = await fetch(url, requestOptions)

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error')
      let errorData

      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { message: errorText }
      }

      logger.error('OtoAPI request with token failed:', {
        url,
        method,
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })

      // Do not clear cookie here as we are using a provided token
      if (response.status === 401) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Authentication token expired or invalid'
        })
      }

      throw createError({
        statusCode: response.status,
        statusMessage: errorData.message || `OtoAPI request failed: ${response.statusText}`,
        data: errorData
      })
    }

    // Handle empty responses
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      return { success: true }
    }

    const responseData = await response.json()
    logger.debug(`OtoAPI request with token successful: ${method} ${url}`)

    return responseData
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }
    logger.error('OtoAPI request with token error:', {
      url,
      method,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
    throw createError({
      statusCode: 503,
      statusMessage: 'OtoAPI service unavailable'
    })
  }
}


/**
 * Make public requests to OtoAPI (no authentication required)
 */
export const otoapiPublicRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  body?: any,
  additionalHeaders?: Record<string, string>
): Promise<any> => {
  const config = useRuntimeConfig()
  let url = `${config.otoapi.baseurl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`

  if (method === 'GET' && body) {
    const queryParams = new URLSearchParams(body as Record<string, string>).toString()
    if (queryParams) {
      url += `?${queryParams}`
    }
  }
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'X-App-Identifier': config.public.app.headerAppIdentifier,
    ...additionalHeaders
  }

  const requestOptions: RequestInit = {
    method,
    headers
  }

  if (body && ['POST', 'PUT', 'PATCH'].includes(method)) {
    requestOptions.body = JSON.stringify(body)
  }

  try {
    logger.debug(`Making OtoAPI public request: ${method} ${url}`)
    
    const response = await fetch(url, requestOptions)
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error')
      let errorData
      
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { message: errorText }
      }

      logger.error('OtoAPI public request failed:', {
        url,
        method,
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })

      throw createError({
        statusCode: response.status,
        statusMessage: errorData.message || `OtoAPI request failed: ${response.statusText}`,
        data: errorData
      })
    }

    const responseData = await response.json()
    logger.debug(`OtoAPI public request successful: ${method} ${url}`)
    
    return responseData

  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }

    logger.error('OtoAPI public request error:', {
      url,
      method,
      error: error.message
    })

    throw createError({
      statusCode: 503,
      statusMessage: 'OtoAPI service unavailable'
    })
  }
}
