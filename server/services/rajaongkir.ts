import { logger } from '~/utils/logger'
import { H3Event } from 'h3'
import type { DomesticShippingParams, DomesticDestinationParams } from '~/schemas/raja-ongkir'


/**
 * Make authenticated requests to RajaOngkir API
 */
async function rajaOngkirApiRequest(
  endpoint: string,
  method: 'GET' | 'POST' = 'GET',
  data?: any,
  useFormData: boolean = false
): Promise<any> {
  const config = useRuntimeConfig()
  const rajaOngkirConfig = config.rajaongkir
  
  const url = `${rajaOngkirConfig.baseurl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`
  
  const headers: Record<string, string> = {
    'key': rajaOngkirConfig.apikey
  }
  
  let body: string | URLSearchParams | undefined
  
  if (data && method === 'POST') {
    if (useFormData) {
      // For form-data requests
      headers['Content-Type'] = 'application/x-www-form-urlencoded'
      const formData = new URLSearchParams()
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, String(value))
        }
      })
      body = formData
    } else {
      // For JSON requests
      headers['Content-Type'] = 'application/json'
      body = JSON.stringify(data)
    }
  }
  
  // For GET requests with query parameters
  if (data && method === 'GET') {
    const urlObj = new URL(url)
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, String(value))
      }
    })
    const finalUrl = urlObj.toString()
    logger.debug(`Making RajaOngkir ${method} request: ${finalUrl}`)
  } else {
    logger.debug(`Making RajaOngkir ${method} request: ${url}`)
  }
  
  try {
    const requestUrl = data && method === 'GET' ? new URL(url) : url
    if (data && method === 'GET') {
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          (requestUrl as URL).searchParams.append(key, String(value))
        }
      })
    }
    
    const response = await fetch(data && method === 'GET' ? (requestUrl as URL).toString() : url, {
      method,
      headers,
      body
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      logger.error('RajaOngkir API error response:', errorText)
      
      let errorMessage = `RajaOngkir API error: ${response.statusText}`
      try {
        const errorData = JSON.parse(errorText)
        if (errorData?.meta?.message) {
          errorMessage = errorData.meta.message
        }
      } catch (e) {
        // If parsing fails, use the original error message
      }
      
      throw createError({
        statusCode: response.status,
        statusMessage: errorMessage
      })
    }
    
    const responseText = await response.text()
    logger.debug('RajaOngkir API response received')
    
    try {
      return JSON.parse(responseText)
    } catch (e) {
      logger.error('Error parsing RajaOngkir response:', e)
      throw createError({
        statusCode: 500,
        statusMessage: 'Invalid response from RajaOngkir API'
      })
    }
    
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }
    
    logger.error('RajaOngkir request error:', {
      url,
      method,
      error: error.message
    })
    
    throw createError({
      statusCode: 503,
      statusMessage: 'RajaOngkir service unavailable'
    })
  }
}

/**
 * Calculate domestic shipping cost
 */
export const calculateDomesticCost = async (
  data: DomesticShippingParams
): Promise<any> => {
  // Validate required fields
  if (!data.origin || !data.destination || !data.weight || !data.courier) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required parameters: origin, destination, weight, and courier are required'
    })
  }
  
  logger.info('Calculating domestic shipping cost:', data)
  
  return await rajaOngkirApiRequest(
    '/calculate/domestic-cost',
    'POST',
    data,
    true // Use form data
  )
}

/**
 * Get domestic destinations
 */
export const getDomesticDestinations = async (
  query: DomesticDestinationParams
): Promise<any> => {
  // Validate required fields
  if (!query.search) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing required parameter: search is required'
    })
  }
  
  logger.debug('Fetching domestic destinations:', query)
  
  return await rajaOngkirApiRequest(
    '/destination/domestic-destination',
    'GET',
    query
  )
}
