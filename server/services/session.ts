import { H3Event } from 'h3'
import { z } from 'zod/v3'
import { setOtoapiToken } from '~/server/services/otoapi'
import { setjubelioToken } from '~/server/services/jubelioToken'
import { generateTokenUser } from '~/server/utils/jwt'
import { setSecureCookie } from '~/server/utils/cookies'
import { setCsrfToken } from '~/server/utils/csrf'
import { userSchema, type User } from '~/schemas/otoapi/user'
import { createAuthResponseSchema } from '~/schemas/api-response'

const backendResponseSchema = createAuthResponseSchema(userSchema)

export type BackendResponse = z.infer<typeof backendResponseSchema>

interface CreateUserSessionOptions {
  rememberMe?: boolean;
}

export async function createUserSession(
  event: H3Event,
  backendResponse: BackendResponse,
  options: CreateUserSessionOptions = {}
) {
  // Set tokens from the backend response
  await setOtoapiToken(event, backendResponse.token)
  await setjubelioToken(event, backendResponse.jubelio_token)

  const user: User = backendResponse.user

  // Generate JWT token
  const token = await generateTokenUser(user)

  // Set secure cookie
  const cookieMaxAge = options.rememberMe ? 12 * 60 * 60 : 11 * 60 * 60 // 12 or 11 hours
  setSecureCookie(event, 'reseller_auth_token', token, { maxAge: cookieMaxAge })

  // Generate CSRF token
  const csrfToken = setCsrfToken(event)

  return {
    status: 'success',
    token,
    csrfToken,
    user,
  }
}