import { H3Event } from 'h3'
import { getCookie, setCookie } from 'h3'

const config = useRuntimeConfig()

// Default cookie options
export const defaultCookieOptions = {
  httpOnly: true,
  secure: config.public.useSecureCookies as boolean,
  sameSite: 'lax' as const, // Changed to 'lax' for better compatibility
  path: '/'
}

// Set a secure cookie with consistent options
export const setSecureCookie = (
  event: H3Event,
  name: string,
  value: string,
  options: any = {}
) => {
  setCookie(event, name, value, {
    ...defaultCookieOptions,
    ...options
  })
}

// Get a cookie
export const getSecureCookie = (
  event: H3Event,
  name: string
) => {
  return getCookie(event, name)
}

// Clear a cookie
export const clearCookie = (
  event: H3Event,
  name: string
) => {
  setCookie(event, name, '', {
    ...defaultCookieOptions,
    maxAge: 0
  })
}
