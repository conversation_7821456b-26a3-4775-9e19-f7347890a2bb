import { H3Event } from 'h3'
import { randomBytes } from 'crypto'
import { setSecureCookie, getSecureCookie } from './cookies'

/** 
 * Generate a new CSRF token 
 */
export const generateCsrfToken = (): string => {
  return randomBytes(32).toString('hex')
}

/** 
 * Set a CSRF token in a cookie and return it for the client to use in headers 
 */
export const setCsrfToken = (event: H3Event): string => {
  const token = generateCsrfToken()
  
  // Store the token in an HttpOnly cookie for security
  setSecureCookie(event, 'reseller_csrf_token', token, {
    httpOnly: true, // Make it HttpOnly for security
    path: '/',
    maxAge: 60 * 60 * 24, // 24 hours
    sameSite: 'lax'
  })
  
  return token // Return the token to be sent in the response body
}

/** 
 * Validate a CSRF token against the stored one 
 */
export const validateCsrfToken = (event: H3Event, token: string): boolean => {
  const storedToken = getSecureCookie(event, 'reseller_csrf_token')
  return !!storedToken && storedToken === token
}
