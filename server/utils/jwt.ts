import { SignJWT, jwtVerify } from 'jose'
import { useRuntimeConfig } from '#imports'

// Create a secret key from environment variable
const getSecretKey = () => {
  const config = useRuntimeConfig()
  // Use cookies.secret as specified in your config
  const secret = config.jwt?.secret || config.cookies?.secret
  return new TextEncoder().encode(secret)
}

// Generate a token with user data and Jubelio token
export const generateToken = async (userData: any, jubelioToken: string): Promise<string> => {
  const config = useRuntimeConfig()
  
  const token = await new SignJWT({
    user: userData,
    jubelioToken: jubelioToken
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(config.jwt?.expiry || '12h')
    .sign(getSecretKey())
    
  return token
}

// Generate a token with just user data (no Jubelio token)
export const generateTokenUser = async (userData: any): Promise<string> => {
  const config = useRuntimeConfig()
  
  const token = await new SignJWT({
    user: userData
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(config.jwt?.expiry || '12h')
    .sign(getSecretKey())
    
  return token
}

// Verify and decode a token
export const verifyToken = async (token: string): Promise<any> => {
  try {
    const { payload } = await jwtVerify(token, getSecretKey())
    return payload
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

// Extract user and Jubelio token from JWT
export const extractTokenData = async (token: string): Promise<{ user: any, jubelioToken: string } | null> => {
  try {
    const payload = await verifyToken(token)
    if (!payload) return null
    
    return {
      user: payload.user,
      jubelioToken: payload.jubelioToken
    }
  } catch (error) {
    console.error('Token extraction failed:', error)
    return null
  }
}

// Extract just user data from JWT (for tokens generated with generateTokenUser)
export const extractUserData = async (token: string): Promise<any | null> => {
  try {
    const payload = await verifyToken(token)
    if (!payload) return null
    
    return payload.user
  } catch (error) {
    console.error('User data extraction failed:', error)
    return null
  }
}
