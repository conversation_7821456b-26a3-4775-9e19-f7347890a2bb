import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useJubelio } from '~/composables/useJubelio';
import {
  type JubelioProvince,
  type JubelioCity,
  type JubelioDistrict,
  type JubelioSubdistrict,
  type JubelioLocation,
  type JubelioShops,
  provincesResponseSchema,
  citiesResponseSchema,
  districtsResponseSchema,
  subdistrictsResponseSchema,
  locationsResponseSchema,
  shopsResponseSchema,
  type ProvincesResponse,
  type CitiesResponse,
  type DistrictsResponse,
  type SubdistrictsResponse,
  type LocationsResponse,
  type ShopsResponse
} from '~/schemas/jubelio/address';

export const useAddressStore = defineStore('address', () => {
  // Composables
  const jubelio = useJubelio();
  
  // State
  const provinces = ref<JubelioProvince[]>([]);
  const cities = ref<JubelioCity[]>([]);
  const districts = ref<JubelioDistrict[]>([]);
  const subdistricts = ref<JubelioSubdistrict[]>([]);
  const locations = ref<JubelioLocation[]>([]);
  const shops = ref<JubelioShops[]>([]);
  const otoresellShop = ref<JubelioShops| null>(null);
  
  const loadingProvinces = ref(false);
  const loadingCities = ref(false);
  const loadingDistricts = ref(false);
  const loadingSubdistricts = ref(false);
  const loadingLocations = ref(false);
  const loadingShops = ref(false);
  
  const error = ref<string | null>(null);
  
  // Actions
  /**
   * Fetch all provinces
   */
  const fetchProvinces = async () => {
    loadingProvinces.value = true;
    error.value = null;
    
    try {
      const response = await jubelio.get<ProvincesResponse>('/region/provinces');
      const validatedResponse = provincesResponseSchema.parse(response);
      provinces.value = validatedResponse;
      return validatedResponse;
    } catch (err: any) {
      console.error('Error fetching or validating provinces:', err);
      error.value = err.message || 'Failed to fetch provinces';
      return [];
    } finally {
      loadingProvinces.value = false;
    }
  }
  
  /**
   * Fetch cities by province ID
   * @param provinceId - The ID of the province
   */
  const fetchCities = async (provinceId: string) => {
    if (!provinceId) {
      cities.value = [];
      return [];
    }
    
    loadingCities.value = true;
    error.value = null;
    
    try {
      const response = await jubelio.get<CitiesResponse>('/region/cities', {
        province_id: provinceId
      });
      const validatedResponse = citiesResponseSchema.parse(response);
      cities.value = validatedResponse;
      return validatedResponse;
    } catch (err: any) {
      console.error('Error fetching or validating cities:', err);
      error.value = err.message || 'Failed to fetch cities';
      return [];
    } finally {
      loadingCities.value = false;
    }
  }
  
  /**
   * Fetch districts by city ID
   * @param cityId - The ID of the city
   */
  const fetchDistricts = async (cityId: string) => {
    if (!cityId) {
      districts.value = [];
      return [];
    }
    
    loadingDistricts.value = true;
    error.value = null;
    
    try {
      const response = await jubelio.get<DistrictsResponse>('/region/districts', {
        city_id: cityId
      });
      const validatedResponse = districtsResponseSchema.parse(response);
      districts.value = validatedResponse;
      return validatedResponse;
    } catch (err: any) {
      console.error('Error fetching or validating districts:', err);
      error.value = err.message || 'Failed to fetch districts';
      return [];
    } finally {
      loadingDistricts.value = false;
    }
  }
  
  /**
   * Fetch subdistricts by district ID
   * @param districtId - The ID of the district
   */
  const fetchSubdistricts = async (districtId: string) => {
    if (!districtId) {
      subdistricts.value = [];
      return [];
    }
    
    loadingSubdistricts.value = true;
    error.value = null;
    
    try {
      const response = await jubelio.get<SubdistrictsResponse>('/region/subdistricts', {
        district_id: districtId
      });
      const validatedResponse = subdistrictsResponseSchema.parse(response);
      subdistricts.value = validatedResponse;
      return validatedResponse;
    } catch (err: any) {
      console.error('Error fetching or validating subdistricts:', err);
      error.value = err.message || 'Failed to fetch subdistricts';
      return [];
    } finally {
      loadingSubdistricts.value = false;
    }
  }

  /**
   * Fetch locations (warehouses) where products are placed
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  const fetchLocations = async (page: number = 1, pageSize: number = 10) => {
    loadingLocations.value = true;
    error.value = null;
    
    try {
      const response = await jubelio.get<LocationsResponse>('/locations/', {
        page,
        pageSize
      });
      const validatedResponse = locationsResponseSchema.parse(response);
      locations.value = validatedResponse.data;
      return validatedResponse;
    } catch (err: any) {
      console.error('Error fetching or validating locations:', err);
      error.value = err.message || 'Failed to fetch locations';
      return { data: [], totalCount: 0 };
    } finally {
      loadingLocations.value = false;
    }
  }

  // Fix the fetchShops function
  const fetchShops = async (page: number = 1, pageSize: number = 20) => {
    loadingShops.value = true;
    error.value = null;
        
    try {
      const response = await jubelio.get<ShopsResponse>('/locations/store/', {
        page,
        pageSize
      });
      const validatedResponse = shopsResponseSchema.parse(response);
      shops.value = validatedResponse.data;
      return validatedResponse.data; // This returns JubelioShops[]
    } catch (err: any) {
      console.error('Error fetching or validating shops:', err);
      error.value = err.message || 'Failed to fetch shops';
      return []; // Change this to return empty array instead of object
    } finally {
      loadingShops.value = false;
    }
  }

  // Update findShops function
  const findShops = async (storeName: string = 'otoresell', page: number = 1, pageSize: number = 20) => {
    try {
      const allShops = await fetchShops(page, pageSize);
      
      // Now allShops is guaranteed to be JubelioShops[]
      const filteredShops = allShops.filter((shop: JubelioShops) => 
        shop.store_name.toLowerCase().includes(storeName.toLowerCase())
      );
      
      return filteredShops;
    } catch (error) {
      console.error('Error finding shops:', error);
      return [];
    }
  }


  // Add a new function to get Otoresell shop specifically
  const getOtoresellShop = async () => {
    try {
      const shops = await findShops('otoresell');
      // Return the first match or null if not found
      otoresellShop.value = shops.length > 0 ? shops[0] : null;
      return shops.length > 0 ? shops[0] : null;
    } catch (error) {
      console.error('Error getting Otoresell shop:', error);
      otoresellShop.value = null;
      return null;
    }
  }

  /**
   * Reset all region data
   */
  const resetRegionData = () => {
    provinces.value = [];
    cities.value = [];
    districts.value = [];
    subdistricts.value = [];
    error.value = null;
  }
  
  /**
   * Reset city, district, and subdistrict data
   */
  const resetCityData = () => {
    cities.value = [];
    districts.value = [];
    subdistricts.value = [];
  }
  
  /**
   * Reset district and subdistrict data
   */
  const resetDistrictData = () => {
    districts.value = [];
    subdistricts.value = [];
  }
  
  /**
   * Reset subdistrict data
   */
  const resetSubdistrictData = () => {
    subdistricts.value = [];
  }
  
  return {
    // State
    provinces,
    cities,
    districts,
    subdistricts,
    locations,
    shops,
    loadingProvinces,
    loadingCities,
    loadingDistricts,
    loadingSubdistricts,
    loadingLocations,
    loadingShops,
    error,
    otoresellShop,
    
    // Actions
    fetchProvinces,
    fetchCities,
    fetchDistricts,
    fetchSubdistricts,
    fetchLocations,
    resetRegionData,
    resetCityData,
    resetDistrictData,
    resetSubdistrictData,
    fetchShops,
    findShops,
    getOtoresellShop
  };
});
