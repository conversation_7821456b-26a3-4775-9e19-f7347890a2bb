import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi';
import type { User } from '~/schemas/otoapi/user'
import type { RegisterForm } from '~/schemas/frontend/register'
import type { SuccessResponse } from '~/schemas/api-response';
import type { Login } from '~/schemas/frontend/login';
import type { ForgotPasswordForm, ResetPasswordForm } from '~/schemas/frontend/reset-password';


export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const validationErrors = ref<Record<string, string[]>>({})
  const redirectPath = ref<string>('/')
  const rememberedEmail = ref<string | null>(null)
  const registrationSuccessMessage = ref<string | null>(null)
  const unverifiedEmail = ref<string | null>(null)
  const emailForVerification = ref<string | null>(null)
  const token = ref<string | null>(null) // Add token state
  const csrfToken = ref<string | null>(null) // Add CSRF token state
  const resendLoading = ref(false)
  const resendStatus = ref<{ success: boolean; message: string } | null>(null)
  const resendCooldownEndTime = ref<number | null>(null)
  const forgotPasswordLoading = ref(false)
  const forgotPasswordStatus = ref<{ success: boolean; message: string } | null>(null)
  const forgotPasswordCooldownEndTime = ref<number | null>(null)
  const resetPasswordLoading = ref(false)
  const resetPasswordStatus = ref<{ success: boolean; message: string } | null>(null)
  const api = useApi();
  
  // Only use localStorage for non-sensitive data like redirect path
  
  
  // Computed
  const userFullName = computed(() => user.value?.name || 'Guest')
  
  // Actions
  const login = async (form: Login) => {
    loading.value = true
    error.value = null
    unverifiedEmail.value = null
    resendStatus.value = null // Reset resend status on new login
    validationErrors.value = {}

    try {
      const response = await $fetch<{
        status: string;
        user: User;
        token: string;
        csrfToken: string;
        message?: string
      }>('/api/auth/login', {
        method: 'POST',
        body: form
      })

      if (response.status === 'success') {
        user.value = response.user
        isAuthenticated.value = true
        token.value = response.token // Store the token
        csrfToken.value = response.csrfToken // Store the CSRF token

        // Store email if rememberMe is true
        if (form.rememberMe) {
          rememberedEmail.value = form.email;
        } else {
          rememberedEmail.value = null;
        }

        // Navigate to saved redirect path if available
        const savedPath = redirectPath.value
        if (savedPath && savedPath !== '/auth/login') {
          redirectPath.value = '/'
          return navigateTo(savedPath)
        }

        return true
      } else if (response.message) {
        error.value = response.message
        return false
      } else {
        error.value = 'Login failed'
        return false
      }
    } catch (err: any) {
      const errorPayload = err.data?.data; // Data from createError is in err.data.data
      const errorMessage = err.data?.message || err.message; // General error message

      if (errorPayload?.code === 'ACCOUNT_INACTIVE') {
        navigateTo('/auth/pending-activation');
        return false;
      }

      if (errorPayload?.isEmailUnverified) {
        unverifiedEmail.value = form.email;
        error.value = errorPayload.message || err.data.statusMessage || "Your email address is not verified.";
      } else if (errorMessage) {
        error.value = errorMessage;
      } else {
        error.value = 'Login failed';
      }
      return false;
    } finally {
      loading.value = false
    }
  }
  
  const register = async (form: RegisterForm) => {
    loading.value = true
    error.value = null
    validationErrors.value = {}
    registrationSuccessMessage.value = null // Reset on new attempt
    
    try {
      const response = await $fetch<{ status: string, message: string }>('/api/auth/register', {
        method: 'POST',
        body: form
      })
      
      if (response.status === 'success' && response.message) {
        unverifiedEmail.value = form.email
        emailForVerification.value = form.email
        registrationSuccessMessage.value = response.message
        clearResendCooldown()
        return true // Indicate success
      } else if (response.message) {
        error.value = response.message
        return false
      } else {
        error.value = 'Registration failed'
        return false
      }
    } catch (err: any) {
      if (err.statusCode === 422 && err.data?.data?.errors) {
        validationErrors.value = err.data.data.errors
        error.value = err.data.message || 'Validation failed'
      } else if (err.data && err.data.message) {
        error.value = err.data.message
      } else {
        error.value = err.message || 'Registration failed'
      }
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = async (options: { redirect?: boolean } = { redirect: true }) => {
    try {
      await $fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'X-CSRF-Token': csrfToken.value || '' // Include CSRF token
        }
      })
    } catch (err) {
      console.error('Logout API call failed:', err)
    } finally {
      // Clear local state
      user.value = null
      isAuthenticated.value = false
      token.value = null // Clear token
      csrfToken.value = null // Clear CSRF token
      
      // Redirect to login page only if not suppressed
      if (options.redirect) {
        navigateTo('/auth/login')
      }
    }
  }
  
  // Check if user is authenticated
  const checkAuth = async () => {
    // If already authenticated with user data, no need to check again
    if (isAuthenticated.value && user.value) {
      return true
    }
    
    try {
      loading.value = true
      const response = await $fetch<{ 
        authenticated: boolean; 
        user?: User; 
        token?: string;
        csrfToken?: string;
      }>('/api/auth/check')
      
      if (response.authenticated && response.user) {
        user.value = response.user
        isAuthenticated.value = true
        if (response.token) {
          token.value = response.token // Store the token from check response
        }
        if (response.csrfToken) {
          csrfToken.value = response.csrfToken // Store the CSRF token
        }
        return true
      } else {
        // Clear auth state if not authenticated
        user.value = null
        isAuthenticated.value = false
        token.value = null // Clear token
        csrfToken.value = null // Clear CSRF token
        return false
      }
    } catch (err) {
      console.error('Auth check error:', err)
      user.value = null
      isAuthenticated.value = false
      token.value = null // Clear token
      csrfToken.value = null // Clear CSRF token
      return false
    } finally {
      loading.value = false
    }
  }
  
  const getRememberedEmail = () => {
    return rememberedEmail.value || '';
  }
  
  const refreshToken = async () => {
    try {
      loading.value = true
      const response = await $fetch<{ 
        status: string; 
        token: string;
        csrfToken?: string;
      }>('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'X-CSRF-Token': csrfToken.value || '' // Include CSRF token
        }
      })
      
      if (response.status === 'success') {
        // Store the new token
        token.value = response.token
        // Update CSRF token if provided
        if (response.csrfToken) {
          csrfToken.value = response.csrfToken
        }
        // We don't need to update user here since the token is refreshed via cookie
        // Just mark that we're still authenticated
        isAuthenticated.value = true
        return true
      }
    } catch (err) {
      console.error('Failed to refresh token:', err)
    } finally {
      loading.value = false
    }
    
    return false
  }
  
  // Save redirect path for after login
  const saveRedirectPath = (path: string) => {
    redirectPath.value = path;
  }
  
  // Helper function to get auth headers for API requests
  const getAuthHeaders = () => {
    const headers: Record<string, string> = {}
    
    if (token.value) {
      headers['Authorization'] = `Bearer ${token.value}`
    }
    
    if (csrfToken.value) {
      headers['X-CSRF-Token'] = csrfToken.value
    }
    
    return headers
  }

  const setSession = (sessionData: { user: User; token: string; csrfToken: string }) => {
    user.value = sessionData.user
    token.value = sessionData.token
    csrfToken.value = sessionData.csrfToken
    isAuthenticated.value = true
  }

  const getTokenExpiry = () => {
    if (!token.value) return 0
    
    try {
      // Decode JWT payload (simple base64 decode for the payload part)
      const payload = JSON.parse(atob(token.value.split('.')[1]))
      return payload.exp * 1000 // Convert to milliseconds
    } catch (error) {
      console.error('Error decoding token:', error)
      return 0
    }
  }
  
  const resendVerificationEmail = async (email: string, turnstileToken: string) => {
    resendLoading.value = true
    resendStatus.value = null
    try {
      const response = await $fetch<SuccessResponse>('/api/auth/resend-verification', {
        method: 'POST',
        body: { email, turnstileToken },
      })
      if (response.success && response.message) {
        resendStatus.value = { success: true, message: response.message }
      }
    }
    catch (err: any) {
      const message = err.data?.message || 'Failed to send link. Please try again.'
      resendStatus.value = { success: false, message }
    }
    finally {
      resendLoading.value = false
    }
  }

  const verifyEmail = async (verifyUrl: string) => {
    loading.value = true
    error.value = null

    try {
      // The backend now only returns a success message, not a user session.
      await $fetch('/api/auth/verify-email', {
        method: 'POST',
        body: { verify_url: verifyUrl },
      })
      // No session is created, so we don't set user/token here.
      // The component will handle the redirect to the pending-activation page.
    }
    catch (err: any) {
      error.value = err.data?.message || 'Email verification failed.'
    }
    finally {
      loading.value = false
    }
  }

  const clearAuthErrors = () => {
    error.value = null
    unverifiedEmail.value = null
    validationErrors.value = {}
    resendStatus.value = null
  }

  const setResendCooldown = (durationSeconds: number) => {
    resendCooldownEndTime.value = Date.now() + durationSeconds * 1000
  }

  const clearResendCooldown = () => {
    resendCooldownEndTime.value = null
  }

  const forgotPassword = async (form: ForgotPasswordForm) => {
    forgotPasswordLoading.value = true
    forgotPasswordStatus.value = null
    error.value = null
    validationErrors.value = {}

    try {
      const response = await $fetch<{ status: string; message: string }>('/api/auth/forgot-password', {
        method: 'POST',
        body: form,
      })
      forgotPasswordStatus.value = { success: true, message: response.message }
      return true
    } catch (err: any) {
      const message = err.data?.message || 'Failed to send reset link. Please try again.'
      forgotPasswordStatus.value = { success: false, message }
      error.value = message
      if (err.statusCode === 422 && err.data?.data) {
        validationErrors.value = err.data.data.errors
      }
      return false
    } finally {
      forgotPasswordLoading.value = false
    }
  }

  const resetPassword = async (form: ResetPasswordForm) => {
    resetPasswordLoading.value = true
    resetPasswordStatus.value = null
    error.value = null
    validationErrors.value = {}

    try {
      const response = await $fetch<{ status: string; message: string }>('/api/auth/reset-password', {
        method: 'POST',
        body: form,
      })
      resetPasswordStatus.value = { success: true, message: response.message }
      return true
    } catch (err: any) {
      const message = err.data?.message || 'Failed to reset password. Please try again.'
      resetPasswordStatus.value = { success: false, message }
      error.value = message
      if (err.statusCode === 422 && err.data?.data) {
        validationErrors.value = err.data.data.errors
      }
      return false
    } finally {
      resetPasswordLoading.value = false
    }
  }
  
  return {
    // State
    user,
    isAuthenticated,
    loading,
    error,
    validationErrors,
    redirectPath,
    registrationSuccessMessage,
    unverifiedEmail,
    emailForVerification,
    token, // Expose token
    csrfToken, // Expose CSRF token
    resendLoading,
    resendStatus,
    resendCooldownEndTime,
    forgotPasswordLoading,
    forgotPasswordStatus,
    resetPasswordLoading,
    resetPasswordStatus,
    forgotPasswordCooldownEndTime,
    
    // Computed
    userFullName,
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    resendVerificationEmail,
    verifyEmail,
    getRememberedEmail,
    refreshToken,
    saveRedirectPath,
    getAuthHeaders, // New method that includes CSRF token
    setSession,
    getTokenExpiry,
    clearAuthErrors,
    setResendCooldown,
    clearResendCooldown,
    forgotPassword,
    resetPassword,
  }
}, {
  persist: {
    key: 'reseller-auth-store',
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['isAuthenticated', 'user', 'token', 'csrfToken', 'redirectPath', 'rememberedEmail', 'emailForVerification', 'resendCooldownEndTime', 'forgotPasswordCooldownEndTime'],
  },
})
