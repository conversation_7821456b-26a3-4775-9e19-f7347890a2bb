import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useDayjs } from '#dayjs'
import { cartApiResponseSchema, type AddToCartPayload, type CartProduct, type UpdateCartPayload } from '~/schemas/otoapi/cart'

export const useCartStore = defineStore('cart', () => {
  const items = ref<CartProduct[]>([])
  const selectedItemIds = ref<(string | number)[]>([])
  const isLoading = ref(false)
  const lastSyncTime = ref<Date | null>(null)

  const itemCount = computed((): number => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const total = computed((): number => {
    return items.value.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  })

  const selectedItems = computed((): CartProduct[] => {
    return items.value.filter(item => selectedItemIds.value.includes(item.jubelio_item_id))
  })

  const selectedTotal = computed((): number => {
    return selectedItems.value.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  })

  const getItemQuantityInCart = (itemId: string | number): number => {
    const item = items.value.find(item => item.jubelio_item_id === itemId)
    return item ? item.quantity : 0
  }

  const loadCartFromServer = async (): Promise<void> => {
    const { getCartItems } = useCart()
    isLoading.value = true
    try {
      const rawServerItems = await getCartItems()
      const validationResult = cartApiResponseSchema.safeParse(rawServerItems)

      if (!validationResult.success) {
        console.error('Failed to parse cart from server:', validationResult.error)
        items.value = []
      } else {
        items.value = validationResult.data
      }
      lastSyncTime.value = new Date()
    } catch (error) {
      console.error('Failed to load cart from server:', error)
      items.value = []
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const addItem = async (product: AddToCartPayload): Promise<void> => {
    isLoading.value = true
    try {
      const { addToCart } = useCart()
      const serverResponse = await addToCart(product)
      const updatedItem = serverResponse.data
      const existingItemIndex = items.value.findIndex(i => i.id === updatedItem.id)

      if (existingItemIndex > -1) {
        items.value[existingItemIndex] = updatedItem
      } else {
        items.value.push(updatedItem)
      }
      console.log('Successfully added/updated item in cart')
    } catch (error) {
      console.error('Failed to add/update cart item on server:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Removed syncToServer parameter - always sync
  const removeItem = async (jubelioItemId: number | string): Promise<void> => {
    const index = items.value.findIndex(item => item.jubelio_item_id === jubelioItemId)
    if (index === -1) return

    const item = items.value[index]
    
    // Remove from local cart
    items.value.splice(index, 1)
    
    // Remove from selected items
    const selectedIndex = selectedItemIds.value.indexOf(jubelioItemId)
    if (selectedIndex !== -1) {
      selectedItemIds.value.splice(selectedIndex, 1)
    }

    // Always sync to server
    if (item.id) {
      try {
        const { removeFromCart } = useCart()
        await removeFromCart(item.id)
      } catch (error) {
        console.error('Failed to remove cart item from server:', error)
        // Restore item on error
        items.value.splice(index, 0, item)
        if (selectedIndex !== -1) {
          selectedItemIds.value.splice(selectedIndex, 0, jubelioItemId)
        }
        throw error
      }
    }
  }

  // Removed syncToServer parameter - always sync
  const updateQuantity = async (cartItemId: number, newQuantity: number): Promise<void> => {
    const itemIndex = items.value.findIndex(item => item.id === cartItemId)
    if (itemIndex === -1) return

    const originalQuantity = items.value[itemIndex].quantity
    items.value[itemIndex].quantity = newQuantity

    try {
      const { updateCartItem } = useCart()
      const payload: UpdateCartPayload = { quantity: newQuantity }
      await updateCartItem(cartItemId, payload)
    } catch (error) {
      console.error('Failed to update quantity on server:', error)
      items.value[itemIndex].quantity = originalQuantity
      throw error
    }
  }

  // Removed syncToServer parameter - always sync
  const clearCart = async (): Promise<void> => {
    const oldItems = [...items.value]
    const oldSelectedIds = [...selectedItemIds.value]
    
    items.value = []
    selectedItemIds.value = []

    // Always sync to server
    try {
      const { clearCart: clearServerCart } = useCart()
      await clearServerCart()
      console.log('Successfully cleared cart on server')
    } catch (error) {
      console.error('Failed to clear cart on server:', error)
      items.value = oldItems
      selectedItemIds.value = oldSelectedIds
      throw error
    }
  }

  // Removed syncToServer parameter - always sync
  const removeMultipleItems = async (cartIds: number[]): Promise<void> => {
    const itemsToRemove = items.value.filter(item => cartIds.includes(item.id))
    if (itemsToRemove.length === 0) return

    const originalItems = [...items.value]
    const originalSelectedIds = [...selectedItemIds.value]

    try {
      // Remove from local state
      cartIds.forEach(cartId => {
        const index = items.value.findIndex(item => item.id === cartId)
        if (index !== -1) {
          const item = items.value[index]
          items.value.splice(index, 1)
          
          // Remove from selected items using jubelio_item_id
          const selectedIndex = selectedItemIds.value.indexOf(item.jubelio_item_id)
          if (selectedIndex !== -1) {
            selectedItemIds.value.splice(selectedIndex, 1)
          }
        }
      })

      // Always sync to server
      const { removeMultipleFromCart } = useCart()
      const validCartIds = cartIds.filter(id => id && Number(id) > 0)
      
      if (validCartIds.length > 0) {
        await removeMultipleFromCart(validCartIds)
        console.log('Successfully removed items from server cart:', validCartIds)
      }
    } catch (error) {
      console.error('Failed to remove items from server cart:', error)
      items.value = originalItems
      selectedItemIds.value = originalSelectedIds
      throw error
    }
  }

  const setSelectedItems = (ids: (string | number)[]) => {
    selectedItemIds.value = ids
  }

  const getSelectedItems = () => {
    return items.value.filter(item => selectedItemIds.value.includes(item.jubelio_item_id))
  }

  const clearSelectedItems = () => {
    selectedItemIds.value = []
  }
 
   return {
     items,
    selectedItemIds,
    isLoading,
    lastSyncTime,
    itemCount,
    total,
    selectedItems,
    selectedTotal,
    addItem,
    removeItem,
    removeMultipleItems,
    updateQuantity,
    clearCart,
    loadCartFromServer,
    setSelectedItems,
    getSelectedItems,
    getItemQuantityInCart,
    clearSelectedItems
  }
}, {
  persist: {
    key: 'reseller-cart-store',
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['selectedItemIds']
  }
})
