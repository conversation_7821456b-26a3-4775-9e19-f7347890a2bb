import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useApi } from '~/composables/useApi';
import type { CreateOtoApiOrderRequest, ShippingType } from '~/schemas/transaction/create';
import type { CreateOrderRequest, PaymentMethod } from '~/types/checkout';
import { useOngkirStore } from './ongkir'

export const useCheckoutStore = defineStore('checkout', () => {
  const api = useApi();
  const ongkirStore = useOngkirStore()
  
  // State
  const isCreatingOrder = ref(false);
  const orderError = ref<string | null>(null);
  const createdOrderId = ref<number | null>(null);
  const orderWarning = ref<string | null>(null);
  const grandTotal = ref(0);
  const selectedShippingType = ref<ShippingType>('expedition');

  const paymentMethods = computed<PaymentMethod[]>(() => {
    const methods: PaymentMethod[] = [];
    if (grandTotal.value <= 650000) {
      methods.push({
        id: 'qris',
        name: 'QRIS',
        method: 'qr_code',
        description: 'Pay with any QRIS-supported app',
        icon: 'mdi-qrcode-scan'
      });
    } else {
      methods.push({
        id: 'virtual_account',
        name: 'Virtual Account',
        method: 'bank_transfer',
        description: 'Pay with a Virtual Account',
        icon: 'mdi-bank-transfer'
      });
    }
    return methods;
  });

  // Computed
  const bankTransferMethods = computed(() => 
    paymentMethods.value.filter(method => method.method === 'bank_transfer')
  );

  const qrisMethod = computed(() => 
    paymentMethods.value.find(method => method.id === 'qris')
  );

  const shippingTypes = computed(() => [
    {
      value: 'expedition',
      title: 'Use Expedition',
      description: 'Rates calculated from courier API',
      icon: 'mdi-truck-delivery-outline'
    },
    {
      value: 'online_recipient',
      title: 'Online Recipient (No Shipping)',
      description: 'Manually input tracking number',
      icon: 'mdi-account-arrow-right-outline'
    },
    {
      value: 'store_pickup',
      title: 'In-Store Pickup',
      description: 'Collect your order directly from our store',
      icon: 'mdi-storefront-outline'
    },
    {
      value: 'jne_expedition',
      title: 'JNE Expedition',
      description: 'Rates calculated from JNE',
      icon: 'mdi-truck-delivery-outline'
    }

  ] as const);

  const onlineRecipientShippingMethods = computed(() => {
    if (selectedShippingType.value !== 'online_recipient') {
      return []
    }
    const expeditions = {
      'JNE': 'JNE',
      'J&T Cashless': 'J&T Cashless',
      'Shopee Ekspress': 'Spx',
      'Si Cepat': 'SiCepat',
      'Anter Aja': 'Anteraja',
      'ID Express' : 'ID Express',
      'Pos Indonesia' : 'Pos Indonesia'
    }

    const getLogo = (code: string) => {
      switch (code) {
        case 'JNE':
          return ongkirStore.jneLogo
        case 'J&T Cashless':
          return ongkirStore.jntLogo
        case 'Spx':
          return ongkirStore.shopeeExpressLogo
        case 'SiCepat':
          return ongkirStore.sicepatLogo
        case 'Anteraja':
          return ongkirStore.anterajaLogo
        case 'ID Express' :
          return ongkirStore.ideLogo
        case 'Pos Indonesia' :
          return ongkirStore.posLogo
        default:
          return ongkirStore.defaultLogo
      }
    }

    return Object.entries(expeditions).map(([name, code]) => {
      return {
        name,
        code,
        service: 'by_tracking_number',
        description: `Pickup by ${name}`,
        cost: 0, // This is for pickup, so cost is 0
        etd: 'N/A',
        logo: getLogo(code)
      }
    })
  })

  const storePickupShippingMethod = computed(() => ({
    name: 'In-Store Pickup',
    code: 'PICKUP', // A unique internal code
    service: 'In-Store',
    description: 'Collect your order at our physical store.',
    cost: 0,
    price: 0, // Ensure price is also 0
    etd: 'Ready for pickup',
    logo: '/images/otoresell-icon-trasnparent.png' // Or a store icon
  }));
  
  // Actions
  const setGrandTotal = (total: number) => {
    grandTotal.value = total;
  }
  
  const createOrder = async (orderData: {
    jubelio_order: CreateOrderRequest,
    otoapi_order: CreateOtoApiOrderRequest
  // }): Promise<number | null> {
  }): Promise<{ orderId: number, invoiceUrl: string } | null> => {
    isCreatingOrder.value = true;
    orderError.value = null;
    orderWarning.value = null;
    
    try {
      console.log('Creating order...', orderData);
      
      const response = await api.post('/api/transactions', orderData);
      
      // if (response.success && response.data.jubelio_order_id) {
      if (response.success && response.data.jubelio_order_id && response.data.otoapi_order?.data?.invoice_url) {
        createdOrderId.value = response.data.jubelio_order_id;
        
        if (response.warning) {
          orderWarning.value = response.warning;
        }
        
        // return response.data.jubelio_order_id;
        return {
          orderId: response.data.jubelio_order_id,
          invoiceUrl: response.data.otoapi_order.data.invoice_url
        };
      } else if (response.success && response.data.jubelio_order_id) {
        // Fallback for orders that might not have an invoice URL (e.g., COD)
        createdOrderId.value = response.data.jubelio_order_id;
        if (response.warning) {
          orderWarning.value = response.warning;
        }
        console.warn('Order created but no invoice_url found. Navigating to success page.');
        return {
          orderId: response.data.jubelio_order_id,
          invoiceUrl: '' // Empty string indicates no external redirect
        };
      }
      else {
        throw new Error('Invalid response from server');
      }
    } catch (err: any) {
      console.error('Error creating order:', err);

      // Prioritize the most specific user-friendly message from the API response
      if (err.data?.cancel_error) {
        // This happens when cancellation fails after a retry
        orderError.value = err.data.cancel_error;
      } else if (err.data?.message) {
        // General validation or other API errors
        orderError.value = err.data.message;
      } else if (err.statusMessage) {
        // Fallback to the main status message
        orderError.value = err.statusMessage;
      } else {
        // Generic fallback
        orderError.value = 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi.';
      }
      
      return null;
    } finally {
      isCreatingOrder.value = false;
    }
  }
  
  const getPaymentMethodById = (id: string): PaymentMethod | undefined => {
    return paymentMethods.value.find(method => method.id === id);
  }
  
  const reset = () => {
    createdOrderId.value = null;
    orderError.value = null;
    orderWarning.value = null;
  }
  
  return {
    // State
    isCreatingOrder,
    orderError,
    orderWarning,
    createdOrderId,
    selectedShippingType,
    shippingTypes,
    // paymentMethods,
    bankTransferMethods,
    paymentMethods,
    qrisMethod,
    onlineRecipientShippingMethods,
    storePickupShippingMethod,
    
    // Actions
    createOrder,
    getPaymentMethodById,
    setGrandTotal,
    reset
  };
});

