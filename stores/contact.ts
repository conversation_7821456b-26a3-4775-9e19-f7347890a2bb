import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { JubelioContact } from '~/schemas/jubelio/contact'
import { useJ<PERSON>lio } from '~/composables/useJubelio'

export const useContactStore = defineStore('contact', () => {
  // State
  const contact = ref<JubelioContact | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Composables
  const jubelio = useJubelio()
  
  // Actions
  const fetchContact = async (jubelio_contact_id: number) => {
    if (!jubelio_contact_id) {
      error.value = 'Contact ID is required'
      return null
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await jubelio.get<JubelioContact>(`/contacts/${jubelio_contact_id}`)
      contact.value = response
      return response
    } catch (err: any) {
      console.error('Failed to fetch contact:', err)
      error.value = err.message || 'Failed to fetch contact details'
      return null
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    contact,
    loading,
    error,
    
    // Actions
    fetchContact,
  }
}, {
  persist: {
    key: 'reseller-contact-store',
    storage: piniaPluginPersistedstate.localStorage(),
  },
})
