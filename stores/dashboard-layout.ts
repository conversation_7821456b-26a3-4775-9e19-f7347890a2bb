import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useDashboardLayoutStore = defineStore('dashboard-layout', () => {
  // Navigation state
  const drawer = ref(true)
  const rail = ref(false)
  
  // Cart drawer state
  const cartDrawer = ref(false)
  
  // Loading state
  const isLoading = ref(true)
  
  // Logo path
  const otoresellLogoPath = ref('/images/otoresell.png')
  
  // Functions
  const toggleSidebar = () => {
    rail.value = !rail.value
  }
  
  const toggleCart = () => {
    cartDrawer.value = !cartDrawer.value
  }
  
  const finishLoading = () => {
    isLoading.value = false
  }
  
  return {
    drawer,
    rail,
    cartDrawer,
    isLoading,
    otoresellLogoPath,
    toggleSidebar,
    toggleCart,
    finishLoading
  }
}, {
  persist: {
    key: 'reseller-dashboard-layout-store',
    storage: piniaPluginPersistedstate.localStorage(),
  },
})
