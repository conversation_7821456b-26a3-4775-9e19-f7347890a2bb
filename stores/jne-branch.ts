import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'
import type {
  JneBranch,
  CreateJneBranch,
  UpdateJneBranch,
  JneBranchFilter,
} from '~/schemas/otoapi/jne-branch'
import type { PaginatedResponse, SingleItemResponse } from '~/schemas/api-response'
import { jneBranchSchema } from '~/schemas/otoapi/jne-branch'

// Define a type for the paginated JNE branch response
export type JneBranchesListResponse = PaginatedResponse<typeof jneBranchSchema>;
export type JneBranchResponse = SingleItemResponse<typeof jneBranchSchema>;

export const useJneBranchStore = defineStore('jne-branch', () => {
  const api = useApi()

  // State
  const jneBranches = ref<JneBranch[]>([])
  const jneBranchesTotal = ref(0)
  const currentPage = ref(1)
  const lastPage = ref(1)
  const perPage = ref(15)
  const from = ref<number | null>(0)
  const to = ref<number | null>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed getters for pagination info
  const hasNextPage = computed(() => currentPage.value < lastPage.value)
  const hasPrevPage = computed(() => currentPage.value > 1)
  const isFirstPage = computed(() => currentPage.value === 1)
  const isLastPage = computed(() => currentPage.value === lastPage.value)

  // Actions
  const fetchJneBranches = async (params: Partial<JneBranchFilter> = {}) => {
    loading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams()
      
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.per_page) queryParams.append('per_page', params.per_page.toString())
      if (params.search) queryParams.append('search', params.search)
      if (params.is_default !== undefined) queryParams.append('is_default', String(params.is_default))
      if (params.branch_code) queryParams.append('branch_code', params.branch_code)
      if (params.name) queryParams.append('name', params.name)
      if (params.sort_by) queryParams.append('sort_by', params.sort_by)
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction)

      const paginatedData = await api.get<JneBranchesListResponse>(`/api/jne-branches?${queryParams.toString()}`)
      
      jneBranches.value = paginatedData.data || []
      
      if (paginatedData.meta) {
        jneBranchesTotal.value = paginatedData.meta.total
        currentPage.value = paginatedData.meta.current_page
        lastPage.value = paginatedData.meta.last_page
        perPage.value = paginatedData.meta.per_page
        from.value = paginatedData.meta.from
        to.value = paginatedData.meta.to
      }

      return paginatedData
    } catch (err: any) {
      console.error('Failed to fetch JNE branches:', err)
      error.value = err.message || 'Failed to fetch JNE branches'
      throw err
    } finally {
      loading.value = false
    }
  }

  const refreshJneBranches = async () => {
    const params: Partial<JneBranchFilter> = {
      page: currentPage.value,
      per_page: perPage.value
    }
    return await fetchJneBranches(params)
  }

  const getJneBranch = async (jneBranchId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/api/jne-branches/${jneBranchId}`)
      return response
    } catch (err: any) {
      console.error('Failed to fetch JNE branch:', err)
      error.value = err.message || 'Failed to fetch JNE branch'
      throw err
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const createJneBranch = async (jneBranchData: CreateJneBranch) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/jne-branches', jneBranchData)
      
      await refreshJneBranches()
      
      return response
    } catch (err: any) {
      console.error('Failed to create JNE branch:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to create JNE branch'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const editJneBranch = async (jneBranchId: number, jneBranchData: UpdateJneBranch) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.put(`/api/jne-branches/${jneBranchId}`, jneBranchData)
      
      const jneBranchIndex = jneBranches.value.findIndex(branch => branch.id === jneBranchId)
      if (jneBranchIndex !== -1) {
        jneBranches.value[jneBranchIndex] = { ...jneBranches.value[jneBranchIndex], ...response.data };
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to edit JNE branch:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to edit JNE branch'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const deleteJneBranch = async (jneBranchId: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.delete(`/api/jne-branches/${jneBranchId}`)
      
      const jneBranchIndex = jneBranches.value.findIndex(branch => branch.id === jneBranchId)
      if (jneBranchIndex !== -1) {
        jneBranches.value.splice(jneBranchIndex, 1)
        jneBranchesTotal.value = Math.max(0, jneBranchesTotal.value - 1)
        to.value = Math.max(0, (to.value || 0) - 1)
      }
      
      if (jneBranches.value.length === 0 && currentPage.value > 1) {
        await fetchJneBranches({ page: currentPage.value - 1, per_page: perPage.value })
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to delete JNE branch:', err)
      error.value = err.message || 'Failed to delete JNE branch'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    jneBranches,
    jneBranchesTotal,
    currentPage,
    lastPage,
    perPage,
    from,
    to,
    loading,
    error,
    
    // Computed
    hasNextPage,
    hasPrevPage,
    isFirstPage,
    isLastPage,
    
    // Actions
    fetchJneBranches,
    refreshJneBranches,
    getJneBranch,
    createJneBranch,
    editJneBranch,
    deleteJneBranch,
  }
})