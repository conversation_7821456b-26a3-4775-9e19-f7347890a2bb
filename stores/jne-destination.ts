import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'
import type {
  JneDestination,
  CreateJneDestination,
  UpdateJneDestination,
  JneDestinationFilter,
} from '~/schemas/otoapi/jne-destination'
import type { PaginatedResponse, SingleItemResponse } from '~/schemas/api-response'
import { jneDestinationSchema } from '~/schemas/otoapi/jne-destination'

// Define a type for the paginated JNE destination response
export type JneDestinationsListResponse = PaginatedResponse<typeof jneDestinationSchema>;
export type JneDestinationResponse = SingleItemResponse<typeof jneDestinationSchema>;

export const useJneDestinationStore = defineStore('jne-destination', () => {
  const api = useApi()

  // State
  const jneDestinations = ref<JneDestination[]>([])
  const jneDestinationsTotal = ref(0)
  const currentPage = ref(1)
  const lastPage = ref(1)
  const perPage = ref(15)
  const from = ref<number | null>(0)
  const to = ref<number | null>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed getters for pagination info
  const hasNextPage = computed(() => currentPage.value < lastPage.value)
  const hasPrevPage = computed(() => currentPage.value > 1)
  const isFirstPage = computed(() => currentPage.value === 1)
  const isLastPage = computed(() => currentPage.value === lastPage.value)

  // Actions
  const fetchJneDestinations = async (params: Partial<JneDestinationFilter> = {}) => {
    loading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams()
      
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.per_page) queryParams.append('per_page', params.per_page.toString())
      if (params.search) queryParams.append('search', params.search)
      if (params.country_name) queryParams.append('country_name', params.country_name)
      if (params.province_name) queryParams.append('province_name', params.province_name)
      if (params.city_name) queryParams.append('city_name', params.city_name)
      if (params.district_name) queryParams.append('district_name', params.district_name)
      if (params.subdistrict_name) queryParams.append('subdistrict_name', params.subdistrict_name)
      if (params.zip_code) queryParams.append('zip_code', params.zip_code)
      if (params.tariff_code) queryParams.append('tariff_code', params.tariff_code)
      if (params.sort_by) queryParams.append('sort_by', params.sort_by)
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction)

      const paginatedData = await api.get<JneDestinationsListResponse>(`/api/jne-destinations?${queryParams.toString()}`)
      
      jneDestinations.value = paginatedData.data || []
      
      if (paginatedData.meta) {
        jneDestinationsTotal.value = paginatedData.meta.total
        currentPage.value = paginatedData.meta.current_page
        lastPage.value = paginatedData.meta.last_page
        perPage.value = paginatedData.meta.per_page
        from.value = paginatedData.meta.from
        to.value = paginatedData.meta.to
      }

      return paginatedData
    } catch (err: any) {
      console.error('Failed to fetch JNE destinations:', err)
      error.value = err.message || 'Failed to fetch JNE destinations'
      throw err
    } finally {
      loading.value = false
    }
  }

  const refreshJneDestinations = async () => {
    const params: Partial<JneDestinationFilter> = {
      page: currentPage.value,
      per_page: perPage.value
    }
    return await fetchJneDestinations(params)
  }

  const getJneDestination = async (jneDestinationId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/api/jne-destinations/${jneDestinationId}`)
      return response
    } catch (err: any) {
      console.error('Failed to fetch JNE destination:', err)
      error.value = err.message || 'Failed to fetch JNE destination'
      throw err
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const createJneDestination = async (jneDestinationData: CreateJneDestination) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/jne-destinations', jneDestinationData)
      
      await refreshJneDestinations()
      
      return response
    } catch (err: any) {
      console.error('Failed to create JNE destination:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to create JNE destination'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const editJneDestination = async (jneDestinationId: number, jneDestinationData: UpdateJneDestination) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.put(`/api/jne-destinations/${jneDestinationId}`, jneDestinationData)
      
      const jneDestinationIndex = jneDestinations.value.findIndex(destination => destination.id === jneDestinationId)
      if (jneDestinationIndex !== -1) {
        jneDestinations.value[jneDestinationIndex] = { ...jneDestinations.value[jneDestinationIndex], ...response.data };
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to edit JNE destination:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to edit JNE destination'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const deleteJneDestination = async (jneDestinationId: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.delete(`/api/jne-destinations/${jneDestinationId}`)
      
      const jneDestinationIndex = jneDestinations.value.findIndex(destination => destination.id === jneDestinationId)
      if (jneDestinationIndex !== -1) {
        jneDestinations.value.splice(jneDestinationIndex, 1)
        jneDestinationsTotal.value = Math.max(0, jneDestinationsTotal.value - 1)
        to.value = Math.max(0, (to.value || 0) - 1)
      }
      
      if (jneDestinations.value.length === 0 && currentPage.value > 1) {
        await fetchJneDestinations({ page: currentPage.value - 1, per_page: perPage.value })
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to delete JNE destination:', err)
      error.value = err.message || 'Failed to delete JNE destination'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    jneDestinations,
    jneDestinationsTotal,
    currentPage,
    lastPage,
    perPage,
    from,
    to,
    loading,
    error,
    
    // Computed
    hasNextPage,
    hasPrevPage,
    isFirstPage,
    isLastPage,
    
    // Actions
    fetchJneDestinations,
    refreshJneDestinations,
    getJneDestination,
    createJneDestination,
    editJneDestination,
    deleteJneDestination,
  }
})