import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'
import type {
  Jne<PERSON><PERSON>in,
  CreateJneOrigin,
  UpdateJneOrigin,
  JneOriginFilter,
} from '~/schemas/otoapi/jne-origin'
import type { PaginatedResponse, SingleItemResponse } from '~/schemas/api-response'
import { jneOriginSchema } from '~/schemas/otoapi/jne-origin'

// Define a type for the paginated JNE origin response
export type JneOriginsListResponse = PaginatedResponse<typeof jneOriginSchema>;
export type JneOriginResponse = SingleItemResponse<typeof jneOriginSchema>;

export const useJneOriginStore = defineStore('jne-origin', () => {
  const api = useApi()

  // State
  const jneOrigins = ref<JneOrigin[]>([])
  const jneOriginsTotal = ref(0)
  const currentPage = ref(1)
  const lastPage = ref(1)
  const perPage = ref(15)
  const from = ref<number | null>(0)
  const to = ref<number | null>(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed getters for pagination info
  const hasNextPage = computed(() => currentPage.value < lastPage.value)
  const hasPrevPage = computed(() => currentPage.value > 1)
  const isFirstPage = computed(() => currentPage.value === 1)
  const isLastPage = computed(() => currentPage.value === lastPage.value)

  // Actions
  const fetchJneOrigins = async (params: Partial<JneOriginFilter> = {}) => {
    loading.value = true
    error.value = null

    try {
      const queryParams = new URLSearchParams()
      
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.per_page) queryParams.append('per_page', params.per_page.toString())
      if (params.search) queryParams.append('search', params.search)
      if (params.is_default !== undefined) queryParams.append('is_default', String(params.is_default))
      if (params.origin_code) queryParams.append('origin_code', params.origin_code)
      if (params.name) queryParams.append('name', params.name)
      if (params.sort_by) queryParams.append('sort_by', params.sort_by)
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction)

      const paginatedData = await api.get<JneOriginsListResponse>(`/api/jne-origins?${queryParams.toString()}`)
      
      jneOrigins.value = paginatedData.data || []
      
      if (paginatedData.meta) {
        jneOriginsTotal.value = paginatedData.meta.total
        currentPage.value = paginatedData.meta.current_page
        lastPage.value = paginatedData.meta.last_page
        perPage.value = paginatedData.meta.per_page
        from.value = paginatedData.meta.from
        to.value = paginatedData.meta.to
      }

      return paginatedData
    } catch (err: any) {
      console.error('Failed to fetch JNE origins:', err)
      error.value = err.message || 'Failed to fetch JNE origins'
      throw err
    } finally {
      loading.value = false
    }
  }

  const refreshJneOrigins = async () => {
    const params: Partial<JneOriginFilter> = {
      page: currentPage.value,
      per_page: perPage.value
    }
    return await fetchJneOrigins(params)
  }

  const getJneOrigin = async (jneOriginId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/api/jne-origins/${jneOriginId}`)
      return response
    } catch (err: any) {
      console.error('Failed to fetch JNE origin:', err)
      error.value = err.message || 'Failed to fetch JNE origin'
      throw err
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const createJneOrigin = async (jneOriginData: CreateJneOrigin) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/jne-origins', jneOriginData)
      
      await refreshJneOrigins()
      
      return response
    } catch (err: any) {
      console.error('Failed to create JNE origin:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to create JNE origin'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const editJneOrigin = async (jneOriginId: number, jneOriginData: UpdateJneOrigin) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.put(`/api/jne-origins/${jneOriginId}`, jneOriginData)
      
      const jneOriginIndex = jneOrigins.value.findIndex(origin => origin.id === jneOriginId)
      if (jneOriginIndex !== -1) {
        jneOrigins.value[jneOriginIndex] = { ...jneOrigins.value[jneOriginIndex], ...response.data };
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to edit JNE origin:', err.data?.data || err.data)
      error.value = err.data?.data?.message || err.data?.message || 'Failed to edit JNE origin'
      throw err.data
    } finally {
      loading.value = false
    }
  }

  // this function used on admin app
  const deleteJneOrigin = async (jneOriginId: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.delete(`/api/jne-origins/${jneOriginId}`)
      
      const jneOriginIndex = jneOrigins.value.findIndex(origin => origin.id === jneOriginId)
      if (jneOriginIndex !== -1) {
        jneOrigins.value.splice(jneOriginIndex, 1)
        jneOriginsTotal.value = Math.max(0, jneOriginsTotal.value - 1)
        to.value = Math.max(0, (to.value || 0) - 1)
      }
      
      if (jneOrigins.value.length === 0 && currentPage.value > 1) {
        await fetchJneOrigins({ page: currentPage.value - 1, per_page: perPage.value })
      }
      
      return response
    } catch (err: any) {
      console.error('Failed to delete JNE origin:', err)
      error.value = err.message || 'Failed to delete JNE origin'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    jneOrigins,
    jneOriginsTotal,
    currentPage,
    lastPage,
    perPage,
    from,
    to,
    loading,
    error,
    
    // Computed
    hasNextPage,
    hasPrevPage,
    isFirstPage,
    isLastPage,
    
    // Actions
    fetchJneOrigins,
    refreshJneOrigins,
    getJneOrigin,
    createJneOrigin,
    editJneOrigin,
    deleteJneOrigin,
  }
})