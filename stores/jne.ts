import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useApi } from '~/composables/useApi'
import { 
  type JnePriceRequest, 
  type JnePriceResponse,
  type JneAirwaybillRequest,
  type JneAirwaybillResponse,
  type JneTrackingResponse,
  type JneTrackingParams,
} from '~/schemas/jne'

export const useJneStore = defineStore('jne', () => {
  const api = useApi()
  
  const loading = ref(false)
  const error = ref<string | null>(null)

  const getPrices = async (request: JnePriceRequest): Promise<JnePriceResponse | null> => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/api/jne/prices', request)
      return response as JnePriceResponse
    } catch (err: any) {
      error.value = err.message || 'Failed to get prices'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createAirwaybill = async (request: JneAirwaybillRequest): Promise<JneAirwaybillResponse | null> => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/api/jne/airwaybill', request)
      return response as JneAirwaybillResponse
    } catch (err: any) {
      error.value = err.message || 'Failed to create airwaybill'
      throw err
    } finally {
      loading.value = false
    }
  }

  const trackShipment = async (params: JneTrackingParams): Promise<JneTrackingResponse | null> => {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/api/jne/tracking/${params.awb}`)
      return response as JneTrackingResponse
    } catch (err: any) {
      error.value = err.message || 'Failed to track shipment'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    getPrices,
    createAirwaybill,
    trackShipment,
  }
})