import { defineStore } from 'pinia'
import { useApi } from '~/composables/useApi'
import { z } from 'zod/v3'
import {
  type PaginatedNotificationsResponse,
  type UnreadCountResponse,
  paginatedNotificationsResponseSchema,
  unreadCountResponseSchema,
  type Notification
} from '~/schemas/otoapi/notification'

// Define a schema for simple API responses for consistency
export const notificationApiResponseSchema = z.object({
  message: z.string(),
})
export type NotificationApiResponse = z.infer<typeof notificationApiResponseSchema>

export const useNotificationAppStore = defineStore('notification-app', () => {
  // Composables
  const api = useApi()

  // State
  const notifications = ref<Notification[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const unreadCountFromServer = ref(0)
  const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
    from: 0,
    to: 0
  })

  // Actions
  const fetchNotifications = async (page: number = 1, perPage: number = 15) => {
    try {
      loading.value = true
      error.value = null

      const rawResponse = await api.get<unknown>('/api/notifications', {
        page,
        per_page: perPage
      })

      const response = paginatedNotificationsResponseSchema.parse(rawResponse)

      notifications.value = response.data.data
      pagination.value = {
        current_page: response.data.current_page,
        last_page: response.data.last_page,
        per_page: response.data.per_page,
        total: response.data.total,
        from: response.data.from ?? 0,
        to: response.data.to ?? 0
      }

    } catch (err: any) {
      error.value = err.message || 'Failed to fetch notifications'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in fetchNotifications:', err.issues)
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearAllNotifications = async (): Promise<NotificationApiResponse> => {
    try {
      loading.value = true
      error.value = null

      const rawResponse = await api.delete<unknown>('/api/notifications/clear')
      const response = notificationApiResponseSchema.parse(rawResponse)

      notifications.value = []
      pagination.value = {
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0,
        from: 0,
        to: 0
      }

      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to clear notifications'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in clearAllNotifications:', err.issues)
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteNotification = async (id: string): Promise<NotificationApiResponse> => {
    try {
      loading.value = true
      error.value = null

      const rawResponse = await api.delete<unknown>(`/api/notifications/${id}`)
      const response = notificationApiResponseSchema.parse(rawResponse)

      notifications.value = notifications.value.filter(notification => notification.id !== id)
      pagination.value.total = Math.max(0, pagination.value.total - 1)

      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to delete notification'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in deleteNotification:', err.issues)
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchUnreadCount = async (): Promise<UnreadCountResponse> => {
    try {
      error.value = null
      const rawResponse = await api.get<unknown>('/api/notifications/unread-count')
      const response = unreadCountResponseSchema.parse(rawResponse)
      
      unreadCountFromServer.value = response.data.unread_count
      
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch unread count'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in fetchUnreadCount:', err.issues)
      }
      throw err
    }
  }

  const markNotificationAsRead = async (id: string): Promise<NotificationApiResponse> => {
    try {
      loading.value = true
      error.value = null

      const rawResponse = await api.post<unknown>(`/api/notifications/${id}/mark-read`)
      const response = notificationApiResponseSchema.parse(rawResponse)

      const notificationIndex = notifications.value.findIndex(notification => notification.id === id)
      if (notificationIndex !== -1) {
        notifications.value[notificationIndex].read_at = new Date()
      }

      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to mark notification as read'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in markNotificationAsRead:', err.issues)
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  const markAllNotificationsAsRead = async (): Promise<NotificationApiResponse> => {
    try {
      loading.value = true
      error.value = null

      const rawResponse = await api.post<unknown>('/api/notifications/mark-all-read')
      const response = notificationApiResponseSchema.parse(rawResponse)

      notifications.value = notifications.value.map(notification => ({
        ...notification,
        read_at: notification.read_at || new Date()
      }))

      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to mark all notifications as read'
      if (err instanceof z.ZodError) {
        console.error('Zod validation error in markAllNotificationsAsRead:', err.issues)
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  // Getters
  const unreadNotifications = computed(() => 
    notifications.value.filter(notification => !notification.read_at)
  )

  const readNotifications = computed(() => 
    notifications.value.filter(notification => notification.read_at)
  )

  const unreadCount = computed(() => {
    if (notifications.value.length > 0) {
      return unreadNotifications.value.length
    }
    return unreadCountFromServer.value
  })

  const hasNextPage = computed(() => 
    pagination.value.current_page < pagination.value.last_page
  )

  const hasPrevPage = computed(() => 
    pagination.value.current_page > 1
  )

  // Reset state
  const resetState = () => {
    notifications.value = []
    loading.value = false
    error.value = null
    pagination.value = {
      current_page: 1,
      last_page: 1,
      per_page: 15,
      total: 0,
      from: 0,
      to: 0
    }
  }

  return {
    // State
    notifications,
    loading,
    error,
    pagination,
    
    // Getters
    unreadNotifications,
    unreadCountFromServer,
    readNotifications,
    unreadCount,
    hasNextPage,
    hasPrevPage,
    
    // Actions
    fetchNotifications,
    clearAllNotifications,
    deleteNotification,
    fetchUnreadCount,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    resetState
  }
})
