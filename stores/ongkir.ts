import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useOngkirStore = defineStore('ongkir', () => {
  // State
  const jneLogo = ref('/images/shipping/jne.png')
  const jntLogo = ref('/images/shipping/jnt.png')
  const posLogo = ref('/images/shipping/pos.png')
  const ideLogo = ref('/images/shipping/ide.jpg')
  const anterajaLogo = ref('/images/shipping/anteraja.png')
  const sicepatLogo = ref('/images/shipping/sicepat.jpg')
  const shopeeExpressLogo = ref('/images/shipping/spx.png')
  const bcalogo = ref('/images/bank/bca.png')
  const mandiriLogo = ref('/images/bank/mandiri.png')
  const defaultLogo = ref('/images/placeholder.png')
  
  return {
    // State
    jneLogo,
    jntLogo,
    posLogo,
    ide<PERSON>ogo,
    anteraja<PERSON>ogo,
    sicepat<PERSON>ogo,
    shopeeExpress<PERSON>ogo,
    bcalogo,
    mandiri<PERSON><PERSON>,
    default<PERSON>ogo
  }
})
