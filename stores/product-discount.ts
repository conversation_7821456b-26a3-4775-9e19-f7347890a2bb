import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useApi } from '~/composables/useApi'
import { useJubelio } from '~/composables/useJubelio'
import type { ProductDiscount, BatchUpdateProductDiscount, BatchDestroyProductDiscount, ProductDiscountFilter } from '~/schemas/otoapi/product-discount'
import type { JubelioPaginatedResponse } from '~/schemas/jubelio/response'
import type { JubelioProductGroup, jubelioProductGroupSchema } from '~/schemas/jubelio/product'

import { logger } from '~/utils/logger'

export const useProductDiscountStore = defineStore('product-discount', () => {
  const api = useApi()
  const jubelio = useJubelio()

  // State
  const productsGroup = ref<JubelioProductGroup[]>([])
  const discounts = ref<Map<number, ProductDiscount>>(new Map())
  const allDiscounts = ref<ProductDiscount[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    page: 1,
    pageSize: 60,
    totalItems: 0,
    totalPages: 0,
    from: 0,
    to: 0,
  })

  // Computed
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)

  const fetchDiscounts = async (params: Partial<ProductDiscountFilter> = {}) => {
    loading.value = true
    error.value = null
    try {
      const queryParams = new URLSearchParams()

      if (params.page) queryParams.append('page', params.page.toString())
      if (params.per_page) queryParams.append('per_page', params.per_page.toString())
      if (params.search) queryParams.append('search', params.search)
      if (params.sort_by) queryParams.append('sort_by', params.sort_by)
      if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction)

      const response = await api.get(`/api/product-discounts?${queryParams.toString()}`)
      allDiscounts.value = response.data
      pagination.value = {
        page: response.meta.current_page,
        pageSize: response.meta.per_page,
        totalItems: response.meta.total,
        totalPages: response.meta.last_page,
        from: response.meta.from,
        to: response.meta.to,
      }
    } catch (err: any) {
      logger.error('Failed to fetch discounts:', err)
      error.value = err.data?.message || 'Failed to fetch discounts'
      throw err
    } finally {
      loading.value = false
    }
  }

   // Actions
   const fetchProductsAndDiscounts = async (
     {
       page = 1,
       pageSize = 15,
       sortDirection = 'DESC',
       sortBy = 'last_modified',
       q = '',
     } = {}
   ) => {
     loading.value = true
     error.value = null
 
     try {
       // 1. Fetch products from Jubelio
       const productResponse = await jubelio.get<JubelioPaginatedResponse<typeof jubelioProductGroupSchema>>('/inventory/items/', {
         page,
         pageSize,
         sortDirection,
         sortBy,
         q: q === '' ? undefined : q,
       });
 
       productsGroup.value = productResponse.data
       const totalItems = productResponse.totalCount;
       const totalPages = Math.ceil(totalItems / pageSize);
       const from = (page - 1) * pageSize + 1;
       const to = Math.min(page * pageSize, totalItems);

       pagination.value = {
         page,
         pageSize,
         totalItems,
         totalPages,
         from: totalItems > 0 ? from : 0,
         to: totalItems > 0 ? to : 0,
       }
 
       if (productResponse.data.length === 0) {
         productsGroup.value = [];
         discounts.value = new Map();
         pagination.value.totalItems = 0;
         pagination.value.totalPages = 0;
         return;
       }
 
       // 2. Fetch existing discounts from our backend
       const itemGroupIds = productResponse.data.map(p => p.item_group_id.toString());
       
       const discountResponse = await api.post('/api/product-discounts/batch', {
         jubelio_item_group_ids: itemGroupIds
       }) as { data: ProductDiscount[] }
 
       // 3. Merge discounts into a map for easy lookup
       const discountMap = new Map<number, ProductDiscount>();
       if (discountResponse && discountResponse.data) {
         discountResponse.data.forEach(discount => {
           discountMap.set(discount.jubelio_item_group_id, discount);
         });
       }
       discounts.value = discountMap;
 
     } catch (err: any) {
       logger.error('Failed to fetch products and discounts:', err)
       error.value = err?.message || 'Failed to fetch data'
       throw err
     } finally {
       loading.value = false
     }
   }

  const batchUpdateDiscounts = async (discountsToUpdate: BatchUpdateProductDiscount) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await api.post('/api/product-discounts/batch-update', discountsToUpdate);
      
      // Refresh data after update
      await fetchProductsAndDiscounts({
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
      });

      return response;
    } catch (err: any) {
      logger.error('Failed to batch update discounts:', err);
      error.value = err.data?.data?.message || err.data?.message || 'Failed to update discounts';
      throw err.data;
    } finally {
      loading.value = false;
    }
  };

  const deleteDiscount = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await api.delete(`/api/product-discounts/${id}`);
      await fetchDiscounts({
        page: pagination.value.page,
        per_page: pagination.value.pageSize,
      });
      return response;
    } catch (err: any) {
      logger.error('Failed to delete discount:', err);
      error.value = err.data?.data?.message || err.data?.message || 'Failed to delete discount';
      throw err.data;
    } finally {
      loading.value = false;
    }
  };

  const batchDeleteDiscounts = async (discountsToDelete: BatchDestroyProductDiscount) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await api.post('/api/product-discounts/batch-destroy', discountsToDelete);
      await fetchDiscounts({
        page: pagination.value.page,
        per_page: pagination.value.pageSize,
      });
      return response;
    } catch (err: any) {
      logger.error('Failed to batch delete discounts:', err);
      error.value = err.data?.data?.message || err.data?.message || 'Failed to delete discounts';
      throw err.data;
    } finally {
      loading.value = false;
    }
  };
  
  const fetchBatchProductDiscounts = async (jubelioItemGroupIds: string[]) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/api/product-discounts/batch', {
        jubelio_item_group_ids: jubelioItemGroupIds,
      }) as { data: ProductDiscount[] }

      const discountMap = new Map<number, ProductDiscount>()
      if (response && response.data) {
        response.data.forEach((discount) => {
          discountMap.set(discount.jubelio_item_group_id, discount)
        })
      }
      discounts.value = discountMap
      return discountMap
    } catch (err: any) {
      logger.error('Failed to fetch batch product discounts:', err)
      error.value = err.data?.message || 'Failed to fetch batch product discounts'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    productsGroup,
    discounts,
    allDiscounts,
    loading,
    error,
    pagination,
    // Computed
    isLoading,
    hasError,
    // Actions
    fetchProductsAndDiscounts,
    batchUpdateDiscounts,
    fetchDiscounts,
    deleteDiscount,
    batchDeleteDiscounts,
    fetchBatchProductDiscounts,
  }
})