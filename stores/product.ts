import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useJubelio } from '~/composables/useJubelio'
import { useDiscountCalculator } from '~/composables/useDiscountCalculator'
import {
  type JubelioProduct,
  type JubelioPriceListItem,
  type JubelioProductGroup,
  type JubelioProductDetail,
  type JubelioProductToSell,
  jubelioProductSchema,
  jubelioPriceListItemSchema,
  jubelioProductGroupSchema,
  jubelioProductDetailSchema,
  jubelioProductToSellSchema
} from '~/schemas/jubelio/product'
import {
  type JubelioChannel,
  type JubelioLocation
} from '~/schemas/jubelio/common'
import {
  createJubelioInventoryResponseSchema,
  createJubelioPaginatedResponseSchema,
  createJubelioPriceListResponseSchema
} from '~/schemas/jubelio/response'

export const useProductStore = defineStore('product', () => {
  // State
  const placeholderImage = ref<string>('/images/placeholder.png')
  const products = ref<JubelioProduct[]>([])
  const productsGroup = ref<JubelioProductGroup[]>([])
  const priceList = ref<JubelioPriceListItem[]>([])
  const channels = ref<JubelioChannel[]>([])
  const locations = ref<JubelioLocation[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const totalCount = ref<number>(0)
  const missingPriceItemCodes = ref<Set<string>>(new Set())
  const pagination = ref({
    page: 1,
    pageSize: 60,
    totalItems: 0,
    totalPages: 0
  })
  const searchQuery = ref('')
  const sortBy = ref('last_modified')
  const sortDirection = ref('DESC')
  const viewMode = ref('grid')
  
  // Product detail state
  const currentProduct = ref<JubelioProductDetail | null>(null)
  const currentProductToSell = ref<JubelioProductToSell | null>(null)
  const productLoading = ref(false)
  const productError = ref<string | null>(null)
  
  // Stock data cache
  const stockDataCache = ref<Map<string, JubelioProduct>>(new Map())
  const fetchingStockData = ref<Set<string>>(new Set())

  // Composables
  const jubelio = useJubelio()

  // Computed
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)
  const isProductLoading = computed(() => productLoading.value)
  const hasProductError = computed(() => productError.value !== null)

  // Actions
  const fetchProducts = async ({
    page = 1,
    pageSize = 60,
    sortDirection = 'NONE',
    sortBy = undefined,
    q = '',
    csv = undefined
  } = {}) => {
    loading.value = true
    error.value = null
    
    try {
      // Define response schemas
      const inventoryResponseSchema = createJubelioInventoryResponseSchema(jubelioProductSchema)
      const priceListResponseSchema = createJubelioPriceListResponseSchema(jubelioPriceListItemSchema)

      // Fetch both product inventory and price list in parallel
      const [inventoryResponse, priceListResponse] = await Promise.all([
        jubelio.get('/inventory/', {
          page,
          pageSize,
          sortDirection,
          sortBy,
          q: q === '' ? undefined : q,
          csv
        }),
        jubelio.get('/inventory/internal-price-list/', {
          page,
          pageSize,
          sortDirection,
          sortBy,
          q: q === '' ? undefined : q
        })
      ]);

      // Validate responses
      const validatedInventoryResponse = inventoryResponseSchema.parse(inventoryResponse)
      const validatedPriceListResponse = priceListResponseSchema.parse(priceListResponse)
      
      // Store the responses
      products.value = validatedInventoryResponse.data
      
      priceList.value = validatedPriceListResponse.data
      
      // Merge channels from both responses (using a Map to avoid duplicates)
      const channelsMap = new Map();
      [...validatedInventoryResponse.channels, ...validatedPriceListResponse.channels].forEach(channel => {
        channelsMap.set(channel.store_id || channel.channel_id, channel);
      });
      channels.value = Array.from(channelsMap.values());
      
      // Store locations
      locations.value = validatedInventoryResponse.locations
      
      // Update pagination
      pagination.value = {
        page,
        pageSize,
        totalItems: validatedInventoryResponse.totalCount,
        totalPages: Math.ceil(validatedInventoryResponse.totalCount / pageSize)
      }
      
      return {
        inventory: validatedInventoryResponse,
        priceList: validatedPriceListResponse
      }
    } catch (err: any) {
      error.value = err?.message || 'Failed to fetch products'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Get formatted products for display with prices
  const getFormattedProducts = () => {
    const formattedProducts = products.value.map(product => {
      // First try to find matching price data by item_id
      let priceData = priceList.value.find(item => item.item_id === product.item_id);
      
      // If not found by item_id, try to find by item_code
      if (!priceData) {
        priceData = priceList.value.find(item => item.item_code === product.item_code);
      }
      
      // Get the price from the internal store (store_id: -1) or the first available price
      let price = 0;
      if (priceData && priceData.prices && priceData.prices.length > 0) {
        // Try to find the internal price first (store_id: -1)
        const internalPrice = priceData.prices.find((p: { store_id: number; }) => p.store_id === -1);
        if (internalPrice) {
          price = internalPrice.price;
        } else {
          // Fallback to the first price in the list
          price = priceData.prices[0].price;
        }
      } else if (product.item_code && !missingPriceItemCodes.value.has(product.item_code)) {
        // Add to missing prices set and fetch individually
        missingPriceItemCodes.value.add(product.item_code);
        fetchPriceByItemCode(product.item_code);
      }
      
      return {
        id: product.item_id,
        name: product.item_name,
        price: price || 0,
        image: product.thumbnail || placeholderImage.value,
        variant: (product.variation_values && product.variation_values.length > 0)
          ? product.variation_values.map(v => v.value).join(', ')
          : product.item_code,
        isWishlisted: false,
        stock: product.total_stocks.available,
        sku: product.item_code
      }
    });
    
    return formattedProducts;
  }

  // Fetch price for a specific item code
  const fetchPriceByItemCode = async (itemCode: string) => {
    if (!itemCode) return;
    
    try {
      const priceListResponseSchema = createJubelioPriceListResponseSchema(jubelioPriceListItemSchema)
      const response = await jubelio.get('/inventory/internal-price-list/', {
        page: 1, // Required parameter
        pageSize: 1, // We only need one result
        q: itemCode // Single item code
      });

      // Validate response
      const validatedResponse = priceListResponseSchema.parse(response)
      
      if (validatedResponse.data && validatedResponse.data.length > 0) {
        // Add this price data to our priceList if it's not already there
        const newPriceData = validatedResponse.data[0];
        const existingIndex = priceList.value.findIndex(item => 
          item.item_id === newPriceData.item_id || item.item_code === newPriceData.item_code
        );
        
        if (existingIndex >= 0) {
          // Update existing price data
          priceList.value[existingIndex] = newPriceData;
        } else {
          // Add new price data
          priceList.value.push(newPriceData);
        }
      }
      
      // Remove from missing set once processed
      missingPriceItemCodes.value.delete(itemCode);
      
      return validatedResponse;
    } catch (err) {
      console.error(`Failed to fetch price for item code ${itemCode}:`, err);
      // Remove from missing set to allow retrying later
      missingPriceItemCodes.value.delete(itemCode);
      return null;
    }
  }

  // New function to fetch stock data by item code (SKU)
  const fetchStockByItemCode = async (itemCode: string): Promise<JubelioProduct | null> => {
    if (!itemCode) return null;
    
    // Check if we already have this data in cache
    if (stockDataCache.value.has(itemCode)) {
      return stockDataCache.value.get(itemCode) || null;
    }
    
    // Check if we're already fetching this data
    if (fetchingStockData.value.has(itemCode)) {
      // Wait a bit and check cache again
      await new Promise(resolve => setTimeout(resolve, 500));
      if (stockDataCache.value.has(itemCode)) {
        return stockDataCache.value.get(itemCode) || null;
      }
    }
    
    // Mark as fetching
    fetchingStockData.value.add(itemCode);
    
    try {
      const inventoryResponseSchema = createJubelioInventoryResponseSchema(jubelioProductSchema)
      const response = await jubelio.get('/inventory/', {
        page: 1, // Required parameter
        pageSize: 1, // We only need one result
        q: itemCode // Single item code
      });

      // Validate response
      const validatedResponse = inventoryResponseSchema.parse(response)
      
      if (validatedResponse.data && validatedResponse.data.length > 0) {
        const stockData = validatedResponse.data[0];
        
        // Add to cache
        stockDataCache.value.set(itemCode, stockData);
        
        // Also add to products array if not already there
        const existingIndex = products.value.findIndex(item => 
          item.item_id === stockData.item_id || item.item_code === stockData.item_code
        );
        
        if (existingIndex >= 0) {
          // Update existing product data
          products.value[existingIndex] = stockData;
        } else {
          // Add new product data
          products.value.push(stockData);
        }
        
        return stockData;
      }
      
      return null;
    } catch (err) {
      console.error(`Failed to fetch stock data for item code ${itemCode}:`, err);
      return null;
    } finally {
      // Remove from fetching set
      fetchingStockData.value.delete(itemCode);
    }
  }

  // Get real-time stock for a specific item
  const getRealTimeStock = async (itemId: string | number) => {
    console.log('real ', itemId);
    
    // First check if we have it in the products array
    let product = products.value.find(p => p.item_id === itemId);
    
    // If not found by ID, check if we have the current product with this ID
    if (!product && currentProduct.value) {
      // Add explicit type for parameter 's'
      const sku = currentProduct.value.product_skus?.find((s) => s.item_id === itemId);
      if (sku) {
        // If we found it in the current product, fetch the latest stock data
        const stockData = await fetchStockByItemCode(sku.item_code);
        // Assign to product only if not null
        if (stockData) {
          product = stockData;
        }
      }
    }
    
    // If still not found and we have an ID, try to fetch it directly
    if (!product && itemId) {
      // Try to find the item code from any source
      const itemCode = findItemCodeById(itemId);
      if (itemCode) {
        const stockData = await fetchStockByItemCode(itemCode);
        // Assign to product only if not null
        if (stockData) {
          product = stockData;
        }
      }
    }
    
    // Return the stock data if found
    if (product) {
      return {
        available: product.total_stocks?.available || 0,
        reserved: product.total_stocks?.reserved || 0,
        onHand: product.total_stocks?.on_hand || 0,
        itemId: product.item_id,
        itemCode: product.item_code
      };
    }
    
    return null;
  }

  // Helper function to find item code by ID
  const findItemCodeById = (itemId: string | number): string | null => {
    // Check in products
    const product = products.value.find(p => p.item_id === itemId);
    if (product) return product.item_code;
    
    // Check in current product
    if (currentProduct.value && currentProduct.value.product_skus) {
      // Add explicit type for parameter 's'
      const sku = currentProduct.value.product_skus.find((s) => s.item_id === itemId);
      if (sku) return sku.item_code;
    }
    
    // Check in price list
    const priceItem = priceList.value.find(p => p.item_id === itemId);
    if (priceItem) return priceItem.item_code;
    
    return null;
  }

  // Fetch items from the inventory endpoint
  const fetchInventoryItems = async ({
    page = 1,
    pageSize = 60,
    sortDirection = 'NONE',
    sortBy = 'last_modified',
    q = '',
    csv = undefined,
    channelId = undefined,
    isFavourite = undefined
  } = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const paginatedResponseSchema = createJubelioPaginatedResponseSchema(jubelioProductGroupSchema)
      const response = await jubelio.get('/inventory/items/', {
        page,
        pageSize,
        sortDirection,
        sortBy,
        q: q === '' ? undefined : q,
        csv,
        channelId,
        isFavourite
      });

      // Validate response
      const validatedResponse = paginatedResponseSchema.parse(response)
      
      // Store the response data
      productsGroup.value = validatedResponse.data
      
      // Update pagination and total count
      totalCount.value = validatedResponse.totalCount
      pagination.value = {
        page,
        pageSize,
        totalItems: validatedResponse.totalCount,
        totalPages: Math.ceil(validatedResponse.totalCount / pageSize)
      }
      
      return validatedResponse;
    } catch (err: any) {
      error.value = err?.message || 'Failed to fetch inventory items'
      throw err
    } finally {
      loading.value = false
    }
  }

  // Format inventory items for display
  const getFormattedInventoryItems = (discounts: Map<number, any> = new Map()) => {
    const { calculateDiscountPercentage, calculateDiscountedPrice } = useDiscountCalculator();

    return productsGroup.value.map((product) => {
      // Get the lowest and highest prices from variants
      const prices = product.variants.map((variant: any) => variant.sell_price).filter((price: any) => price !== null && price !== undefined)
      const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0
      const highestPrice = prices.length > 0 ? Math.max(...prices) : 0

      // Check if all variants are out of stock
      const allVariantsOutOfStock = product.variants.every((variant: any) => {
        if (variant.end_qty === 0 || variant.end_qty === null) { return true }
        if (variant.available_qty === null || variant.available_qty <= 0) { return true }
        return false
      })

      // Find the first variant with stock
      const firstVariantWithStock = product.variants.find((variant: any) => {
        if (variant.end_qty === 0 || variant.end_qty === null) { return false }
        if (variant.available_qty === null || variant.available_qty <= 0) { return false }
        return true
      })

      // Format price range
      const priceRange = lowestPrice === highestPrice
        ? formatPrice(lowestPrice)
        : `${formatPrice(lowestPrice)} - ${formatPrice(highestPrice)}`

      const productDiscount = discounts.get(product.item_group_id)
      const discountPercentage = calculateDiscountPercentage(productDiscount?.max_discount_percentage)
      
      const discountedLowestPrice = calculateDiscountedPrice(lowestPrice, discountPercentage)
      const discountedHighestPrice = calculateDiscountedPrice(highestPrice, discountPercentage)

      const discountedPriceRange = discountedLowestPrice === discountedHighestPrice
        ? formatPrice(discountedLowestPrice)
        : `${formatPrice(discountedLowestPrice)} - ${formatPrice(discountedHighestPrice)}`

      return {
        id: firstVariantWithStock?.item_id || product.variants[0]?.item_id,
        name: product.item_name,
        priceRange,
        discountedPriceRange,
        lowestPrice,
        highestPrice,
        image: product.thumbnail || placeholderImage.value,
        variantCount: product.variants.length,
        isWishlisted: false,
        sku: product.variants[0]?.item_code || '',
        allVariantsOutOfStock,
        discountPercentage: discountPercentage,
        item_group_id: product.item_group_id,
      }
    })
  }

  // Fetch product detail by ID
  const fetchProductDetail = async (productId: string | number): Promise<JubelioProductDetail> => {
    productLoading.value = true;
    productError.value = null;
      
    try {
      // Use the path variable approach for the endpoint
      const response = await jubelio.get(`/inventory/items/${productId}`);
      const validatedResponse = jubelioProductDetailSchema.parse(response)
      currentProduct.value = validatedResponse;
          
      return validatedResponse;
    } catch (err: any) {
      productError.value = err?.message || 'Failed to fetch product details';
      console.error('Failed to load product details:', err);
      throw err;
    } finally {
      productLoading.value = false;
    }
  }

  // Update the fetchProductToSell function return type
  const fetchProductToSell = async (itemCode: string): Promise<JubelioProductToSell | null> => {
    if (!itemCode) return null;
      
    try {
      const paginatedResponseSchema = createJubelioPaginatedResponseSchema(jubelioProductToSellSchema)
      const response = await jubelio.get('/inventory/items/to-sell/-1', {
        page: 1,
        pageSize: 1,
        q: itemCode
      });
      
      // Validate response
      const validatedResponse = paginatedResponseSchema.parse(response)
          
      if (validatedResponse.data && validatedResponse.data.length > 0) {
        currentProductToSell.value = validatedResponse.data[0];
        return validatedResponse.data[0];
      }
          
      return null;
    } catch (err) {
      console.error(`Failed to fetch product to sell for item code ${itemCode}:`, err);
      return null;
    }
  }

  // Format price with currency
  const formatPrice = (price: number | string | null | undefined) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : (price || 0);
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numPrice);
  }

  const clearProducts = () => {
    products.value = []
    priceList.value = []
    missingPriceItemCodes.value.clear()
    pagination.value = {
      page: 1,
      pageSize: 60,
      totalItems: 0,
      totalPages: 0
    }
  }

  const clearProductDetail = () => {
    currentProduct.value = null;
    currentProductToSell.value = null;
    productError.value = null;
  }

  const resetFilters = () => {
    searchQuery.value = '';
    sortBy.value = 'last_modified';
    sortDirection.value = 'DESC';
    viewMode.value = 'grid';
    pagination.value.page = 1;
  }

  return {
    // State
    products,
    productsGroup,
    priceList,
    channels,
    locations,
    loading,
    error,
    pagination,
    totalCount,
    placeholderImage,
    missingPriceItemCodes,
    currentProduct,
    currentProductToSell,
    productLoading,
    productError,
    stockDataCache,
    searchQuery,
    sortBy,
    sortDirection,
    viewMode,
    
    // Computed
    isLoading,
    hasError,
    isProductLoading,
    hasProductError,
    
    // Actions
    fetchProducts,
    fetchInventoryItems,
    fetchPriceByItemCode,
    fetchStockByItemCode,
    getRealTimeStock,
    fetchProductDetail,
    fetchProductToSell,
    clearProducts,
    clearProductDetail,
    resetFilters,
    getFormattedProducts,
    getFormattedInventoryItems,
    formatPrice
  }
  
}, {
  persist: {
    key: 'reseller-product-store',
    storage: piniaPluginPersistedstate.localStorage(),
    pick: ['pagination', 'channels', 'locations', 'searchQuery', 'sortBy', 'sortDirection', 'viewMode']
  }
})

