import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useApi } from '~/composables/useApi'
import { z } from 'zod/v3'
import {
  rajaOngkirDestinationSchema,
  rajaOngkirShippingCostSchema,
  type RajaOngkirDestination,
  type RajaOngkirShippingCost,
  type DomesticShippingParams,
  type DomesticDestinationParams,
} from '~/schemas/raja-ongkir'
import { createSingleItemResponseSchema } from '~/schemas/api-response'

export const useRajaOngkirStore = defineStore('raja-ongkir', () => {
  const api = useApi()
  
  // State
  const destinations = ref<RajaOngkirDestination[]>([])
  const shippingCosts = ref<RajaOngkirShippingCost[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  /**
   * Search for domestic destinations
   */
  const searchDomesticDestinations = async (params: DomesticDestinationParams) => {
    try {
      loading.value = true
      error.value = null
      
      const rawResponse = await api.get(
        '/api/raja-ongkir/destination/domestic-destination',
        params
      )
      
      const responseSchema = createSingleItemResponseSchema(z.array(rajaOngkirDestinationSchema))
      const parsedResponse = responseSchema.parse(rawResponse)
      
      destinations.value = parsedResponse.data
      return parsedResponse
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch destinations'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  /**
   * Calculate domestic shipping costs
   */
  const calculateDomesticShipping = async (params: DomesticShippingParams) => {
    try {
      loading.value = true
      error.value = null
      
      const rawResponse = await api.post(
        '/api/raja-ongkir/calculate/domestic-cost',
        params
      )
      
      const responseSchema = createSingleItemResponseSchema(z.array(rajaOngkirShippingCostSchema))
      const parsedResponse = responseSchema.parse(rawResponse)
      
      shippingCosts.value = parsedResponse.data
      return parsedResponse
    } catch (err: any) {
      error.value = err.message || 'Failed to calculate shipping costs'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    destinations,
    shippingCosts,
    loading,
    error,
    
    // Actions
    searchDomesticDestinations,
    calculateDomesticShipping
  }
})
