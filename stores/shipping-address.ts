import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useApi } from '~/composables/useApi'
import {
  shippingAddressSchema,
  type ShippingAddress,
  type ShippingAddressFilter,
  type CreateShippingAddress,
  type UpdateShippingAddress,
} from '~/schemas/otoapi/shipping-address'
import type { SuccessfulPaginatedResponse, SuccessResponse } from '~/schemas/api-response'

export const useShippingAddressStore = defineStore('shipping-address', () => {
  // State
  const addresses = ref<ShippingAddress[]>([])
  const currentAddress = ref<ShippingAddress | null>(null)
  const totalCount = ref<number>(0)
  const loading = ref<boolean>(false)
  const loadingDetail = ref<boolean>(false)
  const loadingCreate = ref<boolean>(false)
  const loadingUpdate = ref<boolean>(false)
  const loadingDelete = ref<boolean>(false)
  const error = ref<string | null>(null)
  const errorDetail = ref<string | null>(null)

  // Use API composable
  const api = useApi()

  // Fetch all addresses with pagination and filters
  const fetchAddresses = async (params: Partial<ShippingAddressFilter> = {}) => {
    loading.value = true
    error.value = null

    try {
      // Create query parameters object
      const queryParams: Record<string, any> = {
        page: params.page || 1,
        per_page: params.per_page || 15,
        sort_by: params.sort_by || 'created_at',
        sort_direction: params.sort_direction || 'desc',
        ...params
      }

      // Call server API endpoint
      const response = await api.get<SuccessfulPaginatedResponse<typeof shippingAddressSchema>>('/api/addresses', queryParams)

      if (response && response.success && response.data) {
        addresses.value = response.data
        totalCount.value = response.meta?.total || 0
      } else {
        addresses.value = []
        totalCount.value = 0
      }

      return addresses.value
    } catch (err: any) {
      error.value = err.data?.message || err.message || 'Failed to load shipping addresses'
      console.error('Error fetching addresses:', err)
      addresses.value = []
      totalCount.value = 0
      return []
    } finally {
      loading.value = false
    }
  }

  // Get specific address by ID
  const getAddressById = async (id: number) => {
    loadingDetail.value = true
    errorDetail.value = null

    try {
      const response = await api.get<SuccessResponse & { data: ShippingAddress }>(`/api/addresses/${id}`)

      if (response && response.success && response.data) {
        currentAddress.value = response.data
        return response.data
      } else {
        currentAddress.value = null
        errorDetail.value = 'Failed to load address details'
        return null
      }
    } catch (err: any) {
      errorDetail.value = err.data?.message || err.message || 'Failed to load address details'
      console.error('Error fetching address details:', err)
      currentAddress.value = null
      return null
    } finally {
      loadingDetail.value = false
    }
  }

  // Create new address
  const createAddress = async (data: CreateShippingAddress) => {
    loadingCreate.value = true
    error.value = null

    try {
      const response = await api.post<SuccessResponse & { data: ShippingAddress }>('/api/addresses', data)

      if (response && response.success && response.data) {
        // Add new address to the beginning of the list
        addresses.value.unshift(response.data)
        totalCount.value += 1
        return response.data
      } else {
        throw new Error('Failed to create address')
      }
    } catch (err: any) {
      error.value = err.data?.message || err.message || 'Failed to create shipping address'
      console.error('Error creating address:', err)
      throw err
    } finally {
      loadingCreate.value = false
    }
  }

  // Update address
  const updateAddress = async (id: number, data: UpdateShippingAddress) => {
    loadingUpdate.value = true
    error.value = null

    try {
      const response = await api.put<SuccessResponse & { data: ShippingAddress }>(`/api/addresses/${id}`, data)

      if (response && response.success && response.data) {
        // Update address in the list
        const index = addresses.value.findIndex(addr => addr.id === id)
        if (index !== -1) {
          addresses.value[index] = response.data
        }
        
        // Update current address if it's the same
        if (currentAddress.value?.id === id) {
          currentAddress.value = response.data
        }
        
        return response.data
      } else {
        throw new Error('Failed to update address')
      }
    } catch (err: any) {
      error.value = err.data?.message || err.message || 'Failed to update shipping address'
      console.error('Error updating address:', err)
      throw err
    } finally {
      loadingUpdate.value = false
    }
  }

  // Delete single address
  const deleteAddress = async (id: number) => {
    loadingDelete.value = true
    error.value = null

    try {
      const response = await api.delete<SuccessResponse>(`/api/addresses/${id}`)

      if (response && response.success) {
        // Remove address from the list
        addresses.value = addresses.value.filter(addr => addr.id !== id)
        totalCount.value -= 1
        
        // Clear current address if it's the deleted one
        if (currentAddress.value?.id === id) {
          currentAddress.value = null
        }
        
        return true
      } else {
        throw new Error('Failed to delete address')
      }
    } catch (err: any) {
      error.value = err.data?.message || err.message || 'Failed to delete shipping address'
      console.error('Error deleting address:', err)
      throw err
    } finally {
      loadingDelete.value = false
    }
  }

  // Bulk delete addresses
  const bulkDeleteAddresses = async (ids: number[]) => {
    loadingDelete.value = true
    error.value = null

    try {
      const response = await api.delete<SuccessResponse>('/api/addresses/bulk-delete', { ids })

      if (response && response.success) {
        // Remove deleted addresses from the list
        addresses.value = addresses.value.filter(addr => !ids.includes(addr.id))
        totalCount.value -= ids.length
        
        // Clear current address if it's one of the deleted ones
        if (currentAddress.value && ids.includes(currentAddress.value.id)) {
          currentAddress.value = null
        }
        
        return true
      } else {
        throw new Error('Failed to delete addresses')
      }
    } catch (err: any) {
      error.value = err.data?.message || err.message || 'Failed to delete shipping addresses'
      console.error('Error bulk deleting addresses:', err)
      throw err
    } finally {
      loadingDelete.value = false
    }
  }

  // Clear all addresses
  const clearAddresses = () => {
    addresses.value = []
    currentAddress.value = null
    totalCount.value = 0
    error.value = null
    errorDetail.value = null
  }

  // Clear errors
  const clearErrors = () => {
    error.value = null
    errorDetail.value = null
  }

  // Clear current address
  const clearCurrentAddress = () => {
    currentAddress.value = null
    errorDetail.value = null
  }

  // Get addresses by user ID
  // const getAddressesByUser = async (userId: number) => {
  //   loading.value = true
  //   error.value = null

  //   try {
  //     const response = await api.get<{
  //       success: boolean
  //       data: ShippingAddress[]
  //     }>(`/api/addresses/user/${userId}`)

  //     if (response && response.success && response.data) {
  //       addresses.value = response.data
  //       totalCount.value = response.data.length
  //       return response.data
  //     } else {
  //       addresses.value = []
  //       totalCount.value = 0
  //       return []
  //     }
  //   } catch (err: any) {
  //     error.value = err.data?.message || err.message || 'Failed to load user addresses'
  //     console.error('Error fetching user addresses:', err)
  //     addresses.value = []
  //     totalCount.value = 0
  //     return []
  //   } finally {
  //     loading.value = false
  //   }
  // }

  return {
    // State
    addresses,
    currentAddress,
    totalCount,
    loading,
    loadingDetail,
    loadingCreate,
    loadingUpdate,
    loadingDelete,
    error,
    errorDetail,

    // Actions
    fetchAddresses,
    getAddressById,
    // getAddressesByUser,
    createAddress,
    updateAddress,
    deleteAddress,
    bulkDeleteAddresses,
    clearAddresses,
    clearErrors,
    clearCurrentAddress
  }
})
