import { defineStore } from 'pinia'

interface SnackbarState {
  show: boolean
  message: string
  color: 'success' | 'error' | 'warning' | 'info'
  timeout: number
}

export const useSnackbarStore = defineStore('snackbar', {
  state: (): SnackbarState => ({
    show: false,
    message: '',
    color: 'info',
    timeout: 3000,
  }),
  actions: {
    showSnackbar(payload: { message: string; color?: SnackbarState['color']; timeout?: number }) {
      this.show = true
      this.message = payload.message
      this.color = payload.color || 'info'
      this.timeout = payload.timeout || 3000
    },
    hideSnackbar() {
      this.show = false
    },
  },
})