import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useJ<PERSON><PERSON> } from '~/composables/useJubelio'
import { useApi } from '~/composables/useApi'
import type { Order, OrderFilter, OrderResponse, OrderDetail as OtoOrderDetail } from '~/schemas/transaction/order'
import type { OrderDetail as JubelioOrderDetail } from '~/types/transaction'

export const useTransactionStore = defineStore('transaction', () => {
  // State for the list of orders
  const orders = ref<Order[]>([])
  const totalCount = ref<number>(0)
  const loading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // State for OtoAPI order detail (the new detail page)
  const orderDetail = ref<OtoOrderDetail | null>(null)
  const loadingDetail = ref<boolean>(false)
  const errorDetail = ref<string | null>(null)
  
  // State for Jubelio order detail (for the existing payment page)
  const jubelioOrderDetail = ref<JubelioOrderDetail | null>(null)
  const loadingJubelioDetail = ref<boolean>(false)
  const errorJubelioDetail = ref<string | null>(null)
  
  // Use composables
  const api = useApi()
  const jubelio = useJubelio()
  
  // Fetch orders from our server API
  const fetchOrders = async (params: Partial<OrderFilter> = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const queryParams: Record<string, any> = {
        page: params.page || 1,
        per_page: params.per_page || 10,
        ...params
      }
      
      const response = await api.get<OrderResponse>('/api/transactions', queryParams)
      
      if (response && response.success && response.data) {
        orders.value = response.data
        totalCount.value = response.meta?.total || 0
      } else {
        orders.value = []
        totalCount.value = 0
      }
      
      return orders.value
    } catch (err: any) {
      error.value = err.message || 'Failed to load orders'
      console.error('Error fetching orders:', err)
      orders.value = []
      totalCount.value = 0
      return []
    } finally {
      loading.value = false
    }
  }
  
  // Fetch a single order detail from our server API
  const fetchOrderDetail = async (id: number) => {
    loadingDetail.value = true
    errorDetail.value = null
    try {
      // The useApi composable directly returns the data payload from our Nuxt server route
      const responseData = await api.get<OtoOrderDetail>(`/api/transactions/${id}`)
      orderDetail.value = responseData
      return responseData
    } catch (err: any) {
      errorDetail.value = err.data?.message || err.message || 'Failed to load order detail'
      console.error('Error fetching order detail:', err)
      orderDetail.value = null
      return null
    } finally {
      loadingDetail.value = false
    }
  }

  // Get order detail from Jubelio (keeping this function for payment page)
  const getJubelioOrderDetail = async (orderId: number) => {
    loadingJubelioDetail.value = true
    errorJubelioDetail.value = null
    
    try {
      const response = await jubelio.get<JubelioOrderDetail>(`/sales/orders/${orderId}`)
      
      if (response) {
        jubelioOrderDetail.value = response
        return response
      } else {
        jubelioOrderDetail.value = null
        errorJubelioDetail.value = 'Failed to load order details'
        return null
      }
    } catch (err: any) {
      errorJubelioDetail.value = err.message || 'Failed to load order details'
      console.error('Error fetching Jubelio order details:', err)
      jubelioOrderDetail.value = null
      return null
    } finally {
      loadingJubelioDetail.value = false
    }
  }
  
  // Clear all orders
  function clearOrders() {
    orders.value = []
    totalCount.value = 0
  }
  
  return {
    // State
    orders,
    totalCount,
    loading,
    error,
    orderDetail,
    loadingDetail,
    errorDetail,
    jubelioOrderDetail,
    loadingJubelioDetail,
    errorJubelioDetail,
    
    // Actions
    fetchOrders,
    fetchOrderDetail,
    getJubelioOrderDetail,
    clearOrders
  }
})
