import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useApi } from '~/composables/useApi'
import { useAuthStore } from '~/stores/auth'
import {
  type UpdateProfile,
  type ChangePassword,
} from '~/schemas/otoapi/user'
import { successResponseSchema } from '~/schemas/api-response'

export const useUserStore = defineStore('user', () => {
  const api = useApi()
  const authStore = useAuthStore()

  // State for actions, not for storing the profile itself
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Actions
  const updateProfile = async (updates: UpdateProfile) => {
    loading.value = true
    error.value = null
    try {
      // The API call now updates the backend AND returns a fresh session
      const newSession = await api.put('/api/users/profile', updates)

      // After a successful update, we update the entire auth state
      // using the new session data from the API response.
      if (newSession && newSession.user) {
        authStore.setSession(newSession)
      } else {
        // If the response is not what we expect, we should probably
        // trigger a full auth refresh to be safe.
        await authStore.checkAuth()
      }
    } catch (err: any) {
      console.error('Failed to update profile:', err)
      error.value = err.message || 'Failed to update profile'
      throw err
    } finally {
      loading.value = false
    }
  }

  const changePassword = async (passwordData: ChangePassword) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/api/users/change-password', passwordData)
      const validatedData = successResponseSchema.parse(response)
      return validatedData
    } catch (err: any) {
      console.error('Failed to change password:', err)
      if (err.data && typeof err.data === 'object') {
        const validationMessages = Object.values(err.data).flat() as string[]
        if (validationMessages.length > 0) {
          error.value = validationMessages.join(', ')
        } else {
          error.value = err.message || 'Failed to change password'
        }
      } else {
        error.value = err.message || 'Failed to change password'
      }
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    error,
    // Actions
    updateProfile,
    changePassword,
  }
})
