export interface OrderItem {
  salesorder_detail_id: number; //required
  item_id: number; // required
  serial_no?: string;
  description?: string;
  tax_id: number; // required
  price: number; // required
  unit: string; // required
  qty_in_base: number; // required
  disc: number; // required
  disc_amount: number; // required
  tax_amount: number; // required
  amount: number; // required
  location_id: number; // required
  shipper?: string;
  channel_order_detail_id?: number;
  tracking_no?: string;
}

export interface CreateOrderRequest {
  salesorder_id: number;
  salesorder_no: string;
  contact_id: number;
  customer_name: string;
  transaction_date: string;
  is_tax_included?: boolean;
  note?: string;
  sub_total: number;
  total_disc: number;
  total_tax: number;
  grand_total: number;
  ref_no?: string;
  location_id: number;
  source: number;
  is_canceled?: boolean;
  cancel_reason?: string;
  cancel_reason_detail?: string;
  channel_status?: string;
  shipping_cost?: number;
  insurance_cost?: number;
  is_paid?: boolean;
  items: OrderItem[];
  shipping_full_name: string;
  shipping_phone?: string;
  shipping_address?: string;
  shipping_country?: string;
  shipping_province_id: string;
  shipping_province: string;
  shipping_city_id: string;
  shipping_city: string;
  shipping_district_id: string;
  shipping_area: string;
  shipping_subdistrict_id: string;
  shipping_subdistrict: string;
  shipping_post_code?: string;
  payment_method: string;
  add_fee: number;
  add_disc: number;
  service_fee: number;
  salesmen_id?: number;
  store_id?: string;
}

export interface CreateOrderResponse {
  id: number;
}

export interface PaymentMethod {
  id: string;
  name: string;
  method: string;
  description?: string;
  icon?: string;
}

export interface ShippingMethod {
  id: string;
  name: string;
  logo: string;
  description: string;
  price: number;
  etd?: string;
  courier: string;
  hasInsurance?: boolean;
}