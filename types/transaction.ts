
export interface ListOrder {
  contact_id: number;
  order_id: number;
}

export interface Transaction {
  salesorder_id: number;
  salesorder_no: string;
  invoice_no: string | null;
  invoice_print_count: number | null;
  invoice_created_date: string | null;
  transaction_date: string;
  created_date: string;
  is_paid: boolean;
  tracking_number: string | null;
  shipping_full_name: string;
  customer_name: string;
  location_name: string;
  grand_total: string;
  channel_status: string | null;
  internal_status: string | null;
  is_canceled: boolean | null;
  store_name: string;
  channel_name: string;
  store_id: number | null;
  channel_id: number;
  shipper: string | null;
  internal_do_number: string | null;
  is_instant_courier: boolean;
  is_po: boolean | null;
  label_printed_count: number | null;
  internal_so_number: string | null;
  awb_created_date: string | null;
  source: number;
  is_fbm: boolean;
  is_cod: boolean | null;
  store: string | null;
  marked_as_complete: boolean | null;
  shipment_type: string | null;
  status_details: any[];
  last_modified: string;
  package_count: number;
  cancel_reason: string | null;
  cancel_reason_detail: string | null;
  wms_status: string;
  note: string | null;
  warehouse_type: string | null;
  ref_no: string | null;
  is_tokopedia_plus: boolean | null;
  priority_fulfillment_tag: string | null;
}

export interface TransactionResponse {
  data: Transaction[];
  totalCount: number;
}

export interface TransactionParams {
  page: number;
  pageSize: number;
  sortDirection?: string;
  sortBy?: string;
  q?: string;
  createdSince?: string;
  transactionDateFrom?: string;
  transactionDateTo?: string;
  lastModifiedSince?: string;
  invoiceCreatedSince?: string;
  isInvoiced?: boolean;
  [key: string]: string | number | boolean | null | undefined;
}

export interface OrderItemDetail {
  salesorder_detail_id: number;
  item_id: number;
  tax_id: number;
  disc_marketplace: string;
  price: string;
  qty: string;
  uom_id: number | null;
  unit: string;
  qty_in_base: string;
  disc: string;
  disc_amount: string;
  tax_amount: string;
  amount: string;
  shipped_date: string | null;
  is_canceled_item: boolean;
  channel_order_detail_id: string | null;
  is_return_resolved: boolean | null;
  reject_return_reason: string | null;
  serial_no: string;
  awb_created_date: string | null;
  ticket_no: string | null;
  pack_scanned_date: string | null;
  pick_scanned_date: string | null;
  destination_code: string | null;
  origin_code: string | null;
  status: string | null;
  is_bundle_deal: boolean;
  item_code: string;
  is_bundle: boolean;
  use_serial_number: boolean;
  use_batch_number: boolean;
  item_name: string;
  description: string;
  sell_price: string;
  original_price: string;
  rate: string;
  tax_name: string;
  item_group_id: number;
  loc_id: number;
  loc_name: string;
  weight_in_gram: string;
  fbm: string;
  is_fbm: boolean;
  variant: string;
  thumbnail: string;
  serials: any[];
  qty_picked: number | null;
}

export interface OrderDetail {
  items: OrderItemDetail[];
  escrow_list: any | null;
  sum_cogs: string;
  salesorder_id: number;
  salesorder_no: string;
  contact_id: number;
  customer_name: string;
  transaction_date: string;
  created_date: string;
  is_tax_included: boolean;
  note: string | null;
  sub_total: string;
  total_disc: string;
  total_tax: string;
  grand_total: string;
  ref_no: string | null;
  payment_method: string;
  location_id: number;
  is_canceled: boolean;
  cancel_reason: string | null;
  source: number;
  cancel_reason_detail: string | null;
  is_paid: boolean;
  channel_status: string;
  shipping_cost: string;
  insurance_cost: string;
  shipping_full_name: string;
  shipping_address: string;
  shipping_area: string;
  shipping_city: string;
  shipping_province: string;
  shipping_post_code: string;
  shipping_country: string;
  last_modified: string;
  shipping_phone: string;
  courier: string;
  tracking_number: string;
  internal_status: string;
  total_weight_in_kg: string;
  source_name: string;
  store_name: string | null;
  location_name: string;
  shipper: string;
}
