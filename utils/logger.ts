// Safe logger for Nuxt 3 that works in both client and server
export const logger = {
  log: (...args: any[]) => {
    if (import.meta.server) {
      console.log(...args)
    } else if (import.meta.dev) {
      // In development, use console.log in a way that Vite doesn't complain
      // eslint-disable-next-line no-console
      console.log(...args)
    }
  },
  
  error: (...args: any[]) => {
    if (import.meta.server) {
      console.error(...args)
    } else if (import.meta.dev) {
      // In development, use console.error in a way that Vite doesn't complain
      // eslint-disable-next-line no-console
      console.error(...args)
    }
  },
  
  warn: (...args: any[]) => {
    if (import.meta.server) {
      console.warn(...args)
    } else if (import.meta.dev) {
      // In development, use console.warn in a way that Vite doesn't complain
      // eslint-disable-next-line no-console
      console.warn(...args)
    }
  },
  
  info: (...args: any[]) => {
    if (import.meta.server) {
      console.info(...args)
    } else if (import.meta.dev) {
      // In development, use console.info in a way that Vite doesn't complain
      // eslint-disable-next-line no-console
      console.info(...args)
    }
  },

  debug: (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[DEBUG] ${message}`, ...args)
    }
  },
}
